{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_DEBUG=1 DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e983f8723597279233a8f05ceff9fe3456f", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_PROFILE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e98431d6fbac4fcf14f03a16b06b7267016", "name": "Profile"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_RELEASE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e989c63c53754d36800c8b5a5c73030d5d2", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98d0b25d39b515a574839e998df229c3cb", "path": "../Podfile", "sourceTree": "SOURCE_ROOT", "type": "file"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984db42b00c4448171f25a4d6745123be9", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/audio_session-0.1.23/ios/Classes/AudioSessionPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985755e364f1073713d658e1aef8a5aedd", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/audio_session-0.1.23/ios/Classes/AudioSessionPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9807771708513e908280a46d22a1ec7e18", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/audio_session-0.1.23/ios/Classes/DarwinAudioSession.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f9a9fbfa072397410d4c9364897ceef9", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/audio_session-0.1.23/ios/Classes/DarwinAudioSession.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9852ac1be4036c0c0ba18f2f4844ead04c", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983538555ca46a3a983453e38fea4f20d3", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e48d3704878c4f77068b5a1d9fd38325", "name": "audio_session", "path": "audio_session", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987227f24f11c2c8f28c928ee78b87d3b5", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9860db498364c87fb70564457485cae40e", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b01eabd10895d626e21e37412bb9f460", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c9eadc2df4ddec38c717bfa9ad7cfc52", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a7e4cd8c7d169d7361cdb17fbb43663c", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98636b562531fdecca3639841063046b4a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98936df7ef65bc0d2c93a539ba8c942e3e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ae247674f447795721c0a89210b4ca42", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9874fbcf2ba548ea8968307a62985f9b6f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9824f2b8c6c88638197aaa81eb4ff5855d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9846e40956fa0e2a945e7b259dded73435", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/audio_session-0.1.23/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9812bdeeaf6e91171a62becf861730f609", "path": "../../../../../../../.pub-cache/hosted/pub.dev/audio_session-0.1.23/ios/audio_session.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9865ca09718873b65909beb5ae758089ca", "path": "../../../../../../../.pub-cache/hosted/pub.dev/audio_session-0.1.23/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985054a821dc6865d663da9d432874a412", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9811394c90d2407970a097574306f1d7f8", "path": "audio_session.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dfba83c2b5ce842657ef24758924970e", "path": "audio_session-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9837006ae95227a98bc99daeadecb04498", "path": "audio_session-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98567e1d6163dedfeb62acf697f342ef47", "path": "audio_session-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9859165c5da51a1d26f71d85fbb191acdf", "path": "audio_session-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98530dfdbe7bf9e8ddfc1d99aab29f1e5c", "path": "audio_session.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e988edeffbafb24afc6ea05730baabce485", "path": "audio_session.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e718c410dcaa2c404d81f5e418221bc2", "name": "Support Files", "path": "../../../../Pods/Target Support Files/audio_session", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9847d40805f5c2a6a3ffc5b77322b4f42f", "name": "audio_session", "path": "../.symlinks/plugins/audio_session/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98ef4695841a254c1b38517a7ed2cafc60", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988e8aaab6d9438150e77b951ff40dd639", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ed741105b1c6a967b77193871080c9e5", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98482297448f5a9f331bc7c09cdc91453a", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9836db6e854a79ca0b30199709fce944d7", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9817562540079184d13f9d453e91e414f3", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9807374cbbd71e7db565932ab8c39fb994", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9815d26ac35cb876064c916493d64f7b93", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ea28a7a5dcc0add3e2d5a91d91855a09", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9862db467fe525ef3a8bbbbd7d60498c67", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98546f3ab7d88612f52e127300ac475e3b", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bced8f692a333fefb32af34b4db440db", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9826008daa2aed603cb6c37803e7490795", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986700f4851f9f6f1eb34dc2ae4a182a98", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/CameraPermissionUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98364d55bf5b64bb0e52b8de1eae109b85", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/CameraPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9842bc8e814cb9526edf015c40ff611013", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/CameraProperties.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98045fc60c0a74bb0621470a00c59889a0", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/FLTCam.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e2e761f24e92b22ded3ed87b8acbf18d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/FLTCamMediaSettingsAVWrapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98520b9d151fbef7f286c66690e2b58f93", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/FLTSavePhotoDelegate.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e82de1369fe14ce58fbdb90fab982799", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/FLTThreadSafeEventChannel.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980b8a47f53fc02792572c4ce3e4b790bb", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e9c224b552360b193d1e21e86d17c51e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/QueueUtils.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cc1950a7d96b6733f6c77ef32611e158", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982fd59c64f50a91bf62abdf03a95eafca", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/CameraPermissionUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984ddba538070623742c14717ffc70ef05", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/CameraPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9827cb3e65b9e74bfaf56c6a1b10ec29f7", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/CameraPlugin_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9813e5b5bf2af98250ce309f144632af44", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/CameraProperties.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986ea70c9d3f2a753ba673b587a564aa73", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/FLTCam.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a7a66e695bb44a43760a94260d88625b", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/FLTCam_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985daad70e31af9751009e834878f314de", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/FLTCamMediaSettingsAVWrapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9895f8e3c2fab7e96efce99e44709a60eb", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/FLTSavePhotoDelegate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98916e98ff8091d604b0cd1bf4aaf1d963", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/FLTSavePhotoDelegate_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986e8b393dc1c2836b9526486818d48956", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/FLTThreadSafeEventChannel.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98635e8bb720b7450b6ed3459a79267eac", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/messages.g.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d84d268062097d3abc082f3fe185f562", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/include/camera_avfoundation/QueueUtils.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b73e206aee7cc40e32a8a4c6753b8109", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9806ba79c0ffdf868ef18eaf3fb60808cc", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b55160ff551b17d7561a81cc7451283a", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e46b6535866cf96a00ab3cdd24c19d8b", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98291871fa9b7db2aec3144700cb7f2ae7", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ce8c36e4e916bb8dd190a7c6c0fdcca3", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989d4590407f5280e187c4c97d2b7f6076", "name": "camera_avfoundation", "path": "camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ea7f346d76b23e9dd1f2a97501f991ed", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984d9ac6cd3797cc71dee9a3da9ccdd055", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f09a191c6b24c6ac15439afa361300bf", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9809407d8d8fe33a9ba8747497b87f9c80", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98555e0ab193c621b7bd61a25311badc31", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98703ccf503b262b2fb8565d5e9b9fa5aa", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9877fed5d05dfd4eb6ccc23985c31332f9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b3f56b840bf84088c80570af089d8bc7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b346eadb6868b4e4c64996d1d68a00f8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9809362274d70ba8da4f9ed32ed754cb7f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981de068040a86da4dec81f2bac31a43ab", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989c1bd63c94f70419bf21203871fda6de", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980d3b8796f4eb8bfe2e9a97daca25c084", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98820101060ca38c00ea5fea528437b2a3", "path": "../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e986b15278ec0b60be898181883f530fb77", "path": "../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/ios/camera_avfoundation/Sources/camera_avfoundation/include/CameraPlugin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98e3d35eae90e6b0b66262a975b1b68a50", "path": "../../../../../../../.pub-cache/hosted/pub.dev/camera_avfoundation-0.9.17+5/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a26710b12adb8d96f2c0bfd96f210513", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98124143d5af5f4bb0e518d29995aff016", "path": "camera_avfoundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984d3907b6c9410c624f24277ba7bfb60a", "path": "camera_avfoundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e983c7e523a73542ec71f4fda5a56c1d1a8", "path": "camera_avfoundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c4a91ce3ff8dd86013ece013f68c2103", "path": "camera_avfoundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9857928b53fa76a93ebbb8617557403a5e", "path": "camera_avfoundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e982eff8229378cc0a9e0c9780bf6bf0017", "path": "camera_avfoundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e983b58f72fe030b23b460f24ec73443c5a", "path": "ResourceBundle-camera_avfoundation_privacy-camera_avfoundation-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9839ca6c3cc5a87017dfff26ab856a4640", "name": "Support Files", "path": "../../../../Pods/Target Support Files/camera_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aa76273df55be9f7ae7bde885cf31a1b", "name": "camera_avfoundation", "path": "../.symlinks/plugins/camera_avfoundation/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989374f506e8af78d03b9b92fc985237f4", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/ios/Classes/FPPDeviceInfoPlusPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98afde915ed110570885cd3251a6f68ac2", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/ios/Classes/FPPDeviceInfoPlusPlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9806f99d1c4589e8370316a3743f646c9b", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986c35bc05570298ec5e8cda4288913d9c", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ebf3fd98018944a88bb3f7436621bd76", "name": "device_info_plus", "path": "device_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982e66e0e5c20c62ee4e043162b97dd8cd", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987bc491b0983eeed6c72356fcde25a87e", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b67eeae9c865e73fa2b8b0e0c1ad4481", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986f3897ce353654f9ffa21c69cfca9d51", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988c832e34be29a3525f1edc9c1431cc3e", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c3201a63c69ea43aa7d05d89079de5df", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98e052ef496e864a1389e39f484ab88cdf", "path": "../../../../../../../.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/ios/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ee2232090be47d7933f4129c067c8b34", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987dbd488ba5ce0c23d1f8e578f50cbb39", "name": "device_info_plus", "path": "device_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988eccd705a4c43b97640e77a18a471c5a", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986702b8d680891a7523383dfded395298", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989973091250cf1fe1529393334ed32713", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981836598027479022645500520aa6e51b", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987380ceba27af55b5e2136430a8cd8a94", "name": "loom_dynamics", "path": "../loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9826cd7389fec564748b0909ee2d7a6c87", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988b8e9efbcc7331237c19dcb3e85559f7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c47f65430d4607bd0c056a2b4ae0539e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983964f7c049d546f09ede549b51822d2d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dec75a2c4383ba8196a25b7e203915f9", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e988b50667f54c3fc01040188b368f57f27", "path": "../../../../../../../.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/ios/device_info_plus.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e987904fd00ac840411f77b06f1c0c43074", "path": "../../../../../../../.pub-cache/hosted/pub.dev/device_info_plus-10.1.2/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989c756e483ca6ea3e7b2751dbd70bbba3", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9815f1dc9f2442a64005f0f6f14fa3c33d", "path": "device_info_plus.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980a86f467321ff8e5e6fdab6df42bc672", "path": "device_info_plus-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98eecf045c45fffbb23a1d4845c9a378bd", "path": "device_info_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98760a43844eef3804b3d2743af30a5a2b", "path": "device_info_plus-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987263a58ec533bc725515ea7650569e46", "path": "device_info_plus-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981e0d7a45f4009158be94c8114579a1bf", "path": "device_info_plus.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9865f67b13c75a927c02e57073b93315d5", "path": "device_info_plus.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e988404470d5049128b26d9c3616b2feef9", "path": "ResourceBundle-device_info_plus_privacy-device_info_plus-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d4224fe7a620b5bbffa498a8d9b151fb", "name": "Support Files", "path": "../../../../Pods/Target Support Files/device_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98be6f03aae7890d98d576f2e9ced4c26f", "name": "device_info_plus", "path": "../.symlinks/plugins/device_info_plus/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a42dbc923d1c98e7959e984bdae59556", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/fc_native_video_thumbnail-0.16.1/darwin/Classes/FCImage.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c94aff93a3c6c7713d8cab413812cf9e", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/fc_native_video_thumbnail-0.16.1/darwin/Classes/FcNativeVideoThumbnailPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9800feae5c3707f7f9bcb8024967efa411", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ff80861025d55bd999cb317d334c715e", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982ef412b01909d6fa0d179113d86a47e9", "name": "fc_native_video_thumbnail", "path": "fc_native_video_thumbnail", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c31f1258756e23226d945060ac3ea1b2", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9827af36ac2713bc81332ca268654094df", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98da0fd3c2325b8707bbb60f42fd67e9bd", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a26f253f1e6a873464c068c84864870c", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9848c11f11209818a219f553cefe5665e1", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dfee016ac518061d592d97f3494c2c9f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980cc009977a5b8c9e65b403348069b38e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986b94cfa64609e4f36b7ece31e48ccc0f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d47436fa0c2230547bbfb03178bc1af2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985fc0f087c8ff7d7ab680dbc032ce7edf", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98507192711a5e13b2a509fa1ddb862857", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/fc_native_video_thumbnail-0.16.1/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e988d36241d68f0974a81f2dc5654aa35a9", "path": "../../../../../../../.pub-cache/hosted/pub.dev/fc_native_video_thumbnail-0.16.1/darwin/fc_native_video_thumbnail.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e989cc2f7ff74d44a9435d9b6eada0194e9", "path": "../../../../../../../.pub-cache/hosted/pub.dev/fc_native_video_thumbnail-0.16.1/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98762eb0740c55f87281c57fa05077d621", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98715758d222032f8a74a019c623cd6628", "path": "fc_native_video_thumbnail.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cbd5816217fb9d67657b540575db7693", "path": "fc_native_video_thumbnail-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e981c237b25a8671f06bd5c39315101bf7f", "path": "fc_native_video_thumbnail-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98af96cd0d349d4c186f0451a47579a30b", "path": "fc_native_video_thumbnail-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985e5a52ef965fa0040b0f254cb1ab0b15", "path": "fc_native_video_thumbnail-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e985176f4740fcaeed4795356542d79e5c7", "path": "fc_native_video_thumbnail.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e984377d493253f3c48bb89835e089e9a78", "path": "fc_native_video_thumbnail.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c44a2e073abf7f4459e199642815e6e6", "name": "Support Files", "path": "../../../../Pods/Target Support Files/fc_native_video_thumbnail", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987d17fdef83efa08f28560247258f01e6", "name": "fc_native_video_thumbnail", "path": "../.symlinks/plugins/fc_native_video_thumbnail/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98acbc3fad29b89e5b443a6ddcb6b18c3f", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.4/ios/Classes/FileInfo.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981710893562344c457c8c0bcc8c195bb7", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.4/ios/Classes/FileInfo.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989c3cea28ee2416265dfafc95cef7c5e7", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.4/ios/Classes/FilePickerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f8584d03741a9108aa67df559778c125", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.4/ios/Classes/FilePickerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9898a730a92eedf95c519c9448cd121ae4", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.4/ios/Classes/FileUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d9dee60940d731b8f8d2cca7558df170", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.4/ios/Classes/FileUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a502d981c90e251233e0457b0e8154ee", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.4/ios/Classes/ImageUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98000978bc92fbf3879c6e8c3f6aae19e7", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.4/ios/Classes/ImageUtils.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c02aa990683ed03cc997e8d30c92f533", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98a9341f4e2b887f5272ad936b1aca60cb", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.4/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98663d81a6ecb73eb43a7497fb9d518fd9", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980991fd115b5a8472d924d940240b86d0", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9843254dcbd7e86f4210fd39fb2d44eca7", "name": "file_picker", "path": "file_picker", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984d457cfc022d66074318cbf8b679870d", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9894178f601ec3182f6c7473518054c970", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98009eeec6e9f4170ef3e6197394bd08fc", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f7370a409c952b63a9a2875659c18c42", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ec6ecd78ff17af3e2911d91340ed565f", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981d5fff45481fad13455f16a7e9d9e71d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e2f9731404d7fd4e22aab0a185a3fc6f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cad610fab46846f41e1334f6ca41d8fd", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984c2f8e353d3ff9b6e2a2afd43997e25d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f02e1fe1546cf842d7fd69382333a8c8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98574fbf0a6e3c5309f3d814f7b49ff7e7", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.4/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e988d756cf77466ec0c96d6e13732efa8c1", "path": "../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.4/ios/file_picker.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98023b881420a8e3b61e5455fbbf4a52dd", "path": "../../../../../../../.pub-cache/hosted/pub.dev/file_picker-8.1.4/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98869e21f6973e6aab74b2e7c1c60b328c", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9873beaffedf2578131347db377ec42f7b", "path": "file_picker.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9810affdf35a3d58fbc5299d34125aaedb", "path": "file_picker-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e986f51e84ea29482f6b8b98c5fc9f3019c", "path": "file_picker-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98de83ed74b92b26f58530eca3ee40ce06", "path": "file_picker-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988c36df8590cab67fe59886cc9d2e6978", "path": "file_picker-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986eb05f2aac8a6f7660ed87bc3e19803b", "path": "file_picker.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9842228a32fca5c42ff8651e8c49a44c79", "path": "file_picker.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e981a0cd8065d65d6dac4ded3d8619202b5", "path": "ResourceBundle-file_picker_ios_privacy-file_picker-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98937d1653d5a34c82a693e23834379e42", "name": "Support Files", "path": "../../../../Pods/Target Support Files/file_picker", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980bdfb606b60a0866aaa475cb23c15ecb", "name": "file_picker", "path": "../.symlinks/plugins/file_picker/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e983ba17e95c2f64228647237a23e9517c2", "path": "Flutter.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980575b3003b95108a10ef165bb7b97255", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ff65965c673e62111b9f683179fd5d05", "path": "Flutter.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9853082559f5357b4d443a36ce9b62db66", "path": "Flutter.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a13074e2ccd658941f5fd62573bef794", "name": "Support Files", "path": "../Pods/Target Support Files/Flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e8425e352e8834123f4a0bc0b72ecd4c", "name": "Flutter", "path": "../Flutter", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98334f4fa960400ce2b9eb6b65882c72a3", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/CompressFileHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9847fe96ff2ea497c8642140f9e5a6c6b0", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/CompressFileHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98706aa1a4330d29c67781ec2841f1a6ff", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/CompressHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b02dae0fe7de4d5ba534872ce75177b9", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/CompressHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9888fa23de262b3bdb7f777b2785d58d7c", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/CompressListHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9899fcf7957a9cafb89d79123b011356d6", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/CompressListHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a3568904b30a164cb8875affdd705d19", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/ImageCompressPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986029fb4bff00faa0249e136bca0c710f", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/ImageCompressPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9825f0585f3bdd2ebb0a1b40a0d39e16c8", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/UIImage+scale.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981c8f8e8534f1748682a58b5c0f2644e7", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/UIImage+scale.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98243aa128eeefb7079516f306320236d3", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/NSDictionary+SY.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b81819434d0142e4b06802e9bae0f873", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/NSDictionary+SY.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9846cb8f66ac0661744c82d7689309c2cb", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadata.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980d7919e6b149bd00798d0aa8934f5a7f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadata.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98459c0fe6881d5408abae57f7b5f1570e", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadata8BIM.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dd5cda514001a8b8cc2c8d5b123b7096", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadata8BIM.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98172e78f7bf5b9f6b79ac877ba57fcd1a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataBase.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985d3f660b20c0093df5e87c7558f1929f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataBase.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987417081fef67404e63381a124c0de673", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataCIFF.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dc8c170e2ec9caea36de6a6e2b2972f4", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataCIFF.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98deb52748be64193c80ed77fee0270ffa", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataDNG.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981fb02335b88c27386d7b224718272ce2", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataDNG.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98299295309d9104e46ff2d1c645f08cf6", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataExif.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986de39c62ecbcc9540df0bb2aad48664c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataExif.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980bae6b269d836750f9c9b5926ba1753a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataExifAux.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f873c5ceec6328c39131e10dfddbdb54", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataExifAux.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981e5b02eb7ad779f596bdcb625eaf2304", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataGIF.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9815528084c57184c27a06cff20e5c1c48", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataGIF.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f0620bd4227bbf82c22daab1653c4233", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataGPS.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98018031da29bfe2fd028fbc77b2c205a0", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataGPS.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9834807d513ff64c794fdfd0510d510488", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataIPTC.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d5b82f74982377ed6729a36abd777f58", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataIPTC.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98995f9ab6e5ff99189bde982a73e71fdc", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataIPTCContactInfo.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ccd0a14193f4b78cfa332de0ffda49fe", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataIPTCContactInfo.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9831c3438d0575f73e493b7c7cd55c9c03", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataJFIF.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98813834327218e70b0ad466f6c1f893a5", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataJFIF.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988c036f6dc9be82a28469813294d6e0ce", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataMakerCanon.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9802c4cdd43fc74abb8763c9363e54cd98", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataMakerCanon.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c336fd052a28a3428621a1f0f3a79e66", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataMakerFuji.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e9ae972ef42d62a1bc97ed4037fd4c44", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataMakerFuji.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b83d78bdfe41af884eba90fead59776a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataMakerMinolta.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98839bc60a9949bf37c49443b6b9f18971", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataMakerMinolta.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e21e90b2914859e2afad6c2929f99c9a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataMakerNikon.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b22f9aa6277d6c39ee78d4aff3980470", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataMakerNikon.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a599b088437830fe082b5bb2c4f199b2", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataMakerOlympus.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988d471d77d22211d7b1378b9fe0b2e54b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataMakerOlympus.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987411a80ddf50a516c3d34535cc0a4418", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataMakerPentax.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982c9c785bc7be1ed6300b00832e3d1aa8", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataMakerPentax.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9830630fca22762ab96f90b32f34c23f17", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataPNG.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989ded8f9134353f07cca4233b7ab28ea7", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataPNG.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bb30c4984346685dadcf191f286f7baf", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataRaw.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f049affc2c28b7c2f7b1b136b1176796", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataRaw.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9834f222bcca437a79edb4526c50c062f8", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataTIFF.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ac1d2b71f024e0474f2e313dc153e73e", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/Classes/SYPictureMetadata/SYMetadataTIFF.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984b85e562cd386ad9b089ccbeda3a05ac", "name": "SYPictureMetadata", "path": "SYPictureMetadata", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fd41467a3809d9e27ef87f9843ddef8e", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f8e96b607d50adbf99e8e0eaf4f31cff", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ef77b1654e32c112a2d9675a054a999a", "name": "flutter_image_compress_common", "path": "flutter_image_compress_common", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9889079f8f205f3c05adf17add3727b209", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983b64de4f8f5dc19830c6bc8fb9a936ef", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b1d09a99b065747255c8b6ffc64060c1", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988a0f7f565bc9f477b4401a3e23d12d57", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9865a0af3b8c5e14dde4b4c80012dfd2c5", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a6941cd3ae36bef829b88f2664dbdae0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f747367f5e48c4221b8e03210914ec08", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9843678de07cc58953ff770aab42643724", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ca08821c56e7b32ecff257a2fab43e95", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e4ffddb540ba9fe0334a3422f18af715", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ad02b0c137d0c51d5bba8961ad7de033", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98b13f57e43f532fea69987cb2f44c1aa1", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/ios/flutter_image_compress_common.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e983f4c54794ced2fcb7118f610c70afd52", "path": "../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.5/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9870eeac37f9e548242dc31fdbf74da8c7", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e985955ec875bd7e979cdb8b6becf4c3820", "path": "flutter_image_compress_common.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ddcceba7be5886691283edfdc7b21521", "path": "flutter_image_compress_common-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e984abbcc7feb54bb7f9382f9348fa73086", "path": "flutter_image_compress_common-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f63105a970b2ea78f2447eec7d762707", "path": "flutter_image_compress_common-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98092f6e2f6efb38e285f90b0cdfd53bac", "path": "flutter_image_compress_common-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98c9bece455b0327e6e82431550587b5f4", "path": "flutter_image_compress_common.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98529d8744adfac39fb72499c2e61ebbd9", "path": "flutter_image_compress_common.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98614a394d17bbd266fb2334576a2da372", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_image_compress_common", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d3fea88e3e095efc57e179bccf680720", "name": "flutter_image_compress_common", "path": "../.symlinks/plugins/flutter_image_compress_common/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982f0b12eff5d7cd08eb426c9ae1217ee3", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/fluttertoast-8.2.10/ios/Classes/FluttertoastPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a67d2c753f054ee516f46e9b1a53616f", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/fluttertoast-8.2.10/ios/Classes/FluttertoastPlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98968f5135e95310c9602f7c4ed589b383", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e983b9f37ce760219962148d27806fd0398", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/fluttertoast-8.2.10/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ba4482a11e72012bf288ce4a4f2081cc", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985bfd2d87c9a08accb10b6282a1ec8740", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e94fccc2459491f876258d316f45ccb8", "name": "fluttertoast", "path": "fluttertoast", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98047ad9fff6adcae63a42222fea0bbc22", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982c3be02b4bab011f0ae998bb3f67646d", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a3f34f2b1e6257a5b7a874d6fb890942", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98de37f3eb09cc60bed88c4176a0baa9fc", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9839d800e392197b808c7d231bc2e1ced8", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a1456927f973557ca39c5e485c83b8d0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985c416375044ede79eef7aed3af1f68af", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989fef48e0dced2787fa918b646d8ec5b5", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a50b9786a574d8cbf8ddf510965b6b75", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98992a584ce2e28647b0a7d3c5782d7bae", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ce2432a58040a91365dd1e3aeb746480", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/fluttertoast-8.2.10/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98373b969917141106e151fdd1e0762fd7", "path": "../../../../../../../.pub-cache/hosted/pub.dev/fluttertoast-8.2.10/ios/fluttertoast.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9847e32cbb63c3f8376e24967d2617146f", "path": "../../../../../../../.pub-cache/hosted/pub.dev/fluttertoast-8.2.10/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987ca9c7be91ddfe14642866c8d45ec83d", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9834d698f7bf7dd65dc896d7383323548f", "path": "fluttertoast.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983f1c55e4ab46117fb05c9c330db81196", "path": "fluttertoast-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9883757a4fa22fe887b963acf91733121c", "path": "fluttertoast-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fc549914b92c0c05f73f4bc5c29062d7", "path": "fluttertoast-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d32422c4fdae71d7d3f8d601b5bc1e2f", "path": "fluttertoast-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e985528a8349be1371a125de5c8533e96ed", "path": "fluttertoast.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9806365463b66fe8827166870764c1e981", "path": "fluttertoast.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98c3e916e396718cafdda3d88884b6ab9a", "path": "ResourceBundle-fluttertoast_privacy-fluttertoast-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c24043dca8170b3c021e982ce8e29bf8", "name": "Support Files", "path": "../../../../Pods/Target Support Files/fluttertoast", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984d715c11b4f4c570acdc0f826c227880", "name": "fluttertoast", "path": "../.symlinks/plugins/fluttertoast/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b5f673ef8495903447859d025d059d6c", "path": "../../../../../../../../.pub-cache/git/image_gallery_saver-8c6480bf3a07834df89525e7ac6e0196ec701f73/ios/Classes/ImageGallerySaverPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d8c711c4d070fdd8504d66711244e30e", "path": "../../../../../../../../.pub-cache/git/image_gallery_saver-8c6480bf3a07834df89525e7ac6e0196ec701f73/ios/Classes/ImageGallerySaverPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9870b0f5d1e0de45b7bc5083bcb739f2b6", "path": "../../../../../../../../.pub-cache/git/image_gallery_saver-8c6480bf3a07834df89525e7ac6e0196ec701f73/ios/Classes/SwiftImageGallerySaverPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e22002517e45e6fea0dbfc6b43319edd", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f85e48bc6e9641c7d0a39e33c908bdab", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981b9b7230253524fb8c2d01b504589487", "name": "image_gallery_saver", "path": "image_gallery_saver", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988c530ba9181ad214858a80ddfdf1a8de", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e545ae564b41eaccb494fdf46eeeb0cb", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c2924c712ecde26206395ac3474bfd3e", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984ab55e6f71116cb325296da4fdb09af5", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9818b0ecb87d362503f50aa0f8caef35e6", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98da1a20c4d32657bca44eb1a6c79e19e8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98134790127100dd084de3ba90d1fa079e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988995d8df8706749dd443167b444e9da8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9815cc4ebdaea6215abfce46550bfb9e74", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98114a1198d8b542a1c02fb63d61ab1dce", "name": "..", "path": "../../../../../../../.pub-cache/git/image_gallery_saver-8c6480bf3a07834df89525e7ac6e0196ec701f73/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9894c22671d5527e41a60e893dd7f0b51e", "path": "../../../../../../../.pub-cache/git/image_gallery_saver-8c6480bf3a07834df89525e7ac6e0196ec701f73/ios/image_gallery_saver.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9831728773a86e00b774763a0605f839e8", "path": "../../../../../../../.pub-cache/git/image_gallery_saver-8c6480bf3a07834df89525e7ac6e0196ec701f73/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b04e473e979d24ae3e594e09982ed671", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e984a969d2b20c4436d347f6ae0e5eeec49", "path": "image_gallery_saver.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ef842dde94a92f4f7d6aa7358174e8f4", "path": "image_gallery_saver-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98fc94867bbbe135690de952838d3666e5", "path": "image_gallery_saver-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9860a0295b1489191d9c55401ae8c76c13", "path": "image_gallery_saver-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9880ecf2fc39ec693e7648b7f414f12199", "path": "image_gallery_saver-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9880d66081dee0ef339968546d709b3b2f", "path": "image_gallery_saver.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981b6fc7605195a355b76b279ceb644426", "path": "image_gallery_saver.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985c75da299f02a33e8fdbcce941dcdd9a", "name": "Support Files", "path": "../../../../Pods/Target Support Files/image_gallery_saver", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cc666468648d30f57d3bd01ec0e5a569", "name": "image_gallery_saver", "path": "../.symlinks/plugins/image_gallery_saver/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989f16ef0a1a4188608b0e6fc55bdec2db", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/image_gallery_saver_plus-3.0.5/ios/Classes/ImageGallerySaverPlusPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a0659ccf06deca4fa4a0acb92eff9298", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c73e07eb54059b051054f38d324e5f1e", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d74b3c6035c393adb3ae97e377213ab3", "name": "image_gallery_saver_plus", "path": "image_gallery_saver_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dbfb9fa2c1da5dfa6c04b3dd572e0383", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bb9bfd1600eff35e3883dcfb8c7e9711", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985bb5a255c4d421a481d572a739b350a9", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9879d6d770d018aab423185f6a9255e598", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c7770f69f761854fbe812f8030fe560a", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9853b2a3ef84e63b666a2f3c34bd2bb4fd", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f6b230e3180328f72cd076e5064ab284", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a0a7d73c5075acf3c814520e4d924781", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dc3796a6d6dc352c7e7d7e2c7181b5ed", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ec083476c78ba41058cd5a02416a58ca", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98214712d2ef984b7be6a152eab9bd6e1d", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/image_gallery_saver_plus-3.0.5/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e988a149dcb256d9d3dc7c8aecdb22b40c0", "path": "../../../../../../../.pub-cache/hosted/pub.dev/image_gallery_saver_plus-3.0.5/ios/image_gallery_saver_plus.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e985eb111c8d96358510c2a9caf94ffab34", "path": "../../../../../../../.pub-cache/hosted/pub.dev/image_gallery_saver_plus-3.0.5/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98805eb6e071af93f379b03cbbe9c89519", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9897c6fb661b559942cb4bd7117a531738", "path": "image_gallery_saver_plus.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987ad73bf11e508cd9516ec0b67373b422", "path": "image_gallery_saver_plus-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98cf4787b909dcdb89df0dcfbb206fffb6", "path": "image_gallery_saver_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985c60e831256f6c10aa6b9d4c532f42d6", "path": "image_gallery_saver_plus-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9879543625b6d2770c7374f2f65db57811", "path": "image_gallery_saver_plus-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d6dabe2953997fbfc30da822394234db", "path": "image_gallery_saver_plus.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b99e2b27b881979334adce59186afbf3", "path": "image_gallery_saver_plus.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986be2b22fc9e2a240ede53c44a2f1fe1d", "name": "Support Files", "path": "../../../../Pods/Target Support Files/image_gallery_saver_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986fba718766d0bf4c36e0074d1aef7da2", "name": "image_gallery_saver_plus", "path": "../.symlinks/plugins/image_gallery_saver_plus/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98ea4b7bd653df62c1006cb81ce6c97e33", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e80f73ff1a57afc718f5d9fe84379036", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98befd036f35b4f8b0ddda1ee8e36e2e25", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9834da67bde5e44b5907224de01c5c93c4", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9819dfda751922139ef23e9de674cf8b35", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98953398a1bbe72eec088b1550369655c5", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981de2855fd747a3869331d8fa6dec85f6", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98495e5c1b5b7e88571b282a0b247c4419", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9891be1139708886a1b60db1f72688e7a1", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c1d678cc521a1308a5f6a23b2f58b73e", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983d1208b5d2476a3565a4387fe9019a53", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983dcb662eafd35e76e213df9fb7d7cd96", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98306dd6468f901f11ca9b7f7601afd05f", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bf1504b14f5da3b8ea6629de8318e9ea", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerImageUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c1eda46463e0bee26670b13a0c9be7c1", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerMetaDataUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ef98fe405d6aff54bcbf657987112a6c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerPhotoAssetUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987055a09483ba73a7ae0597e9e8f51089", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980898062de47c67ecc0fffd04842a04c3", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/FLTPHPickerSaveImageToPathOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987957e902c17c26ec617f6fb6c5a9281e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98be8713489e6ed086f4448c6d4dbed6da", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98069cea6dfda2fe3ec11e20c0eec27698", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerImageUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988c00ebfbce041967e9cb8e58c2428b50", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerMetaDataUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fa7d3e5811d1c1d105fe98d0a78f8be3", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPhotoAssetUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983c4303cb8de021f4a6d77f59f37ba245", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e6b49d8f7f04107a0d2b9d13c7fb9fab", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPlugin_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9842e0009ce9bba8f648db69af4f0f6e53", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTPHPickerSaveImageToPathOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ec0c523726cd12ce2f0522331f3252ce", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/messages.g.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98584d8140449ac99959f00e19008050b9", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987b7e0339623767b9b280bd9cd5c005ae", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9830be6592643623d8a67d07438e0b4212", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9846e08912072d4e03896defb6e2bb8b9a", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981787615c429f3fb47ea457bb7a131e22", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ddba843fc21acae579aa14e3790535c2", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eaef6624d8df7ad7b4a9bace2de58f3e", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a76f3b1f45ab4a9743a12ff8080d8eb9", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a2367a231ab0952428e2898c12a8e03f", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98603675e4dc48461bd9fb5e750a13dfc3", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d1017bbbcbfb217047bc36d516115516", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b1ead788f42a8fa7a3b2e3f90b0d6e97", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987e4ebb9c9213a7de5db710d9b0bc48c6", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987a1863a751efe0b9b0a8b4e8ec44e229", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980119e58727da2cb664ee90b6c2a1dddb", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a128c1be2a66ede120c3c9456865e617", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98241e91ff3c6a9d7f6f1f8831c9f8abcb", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985751e605819872cb2d7fe184bdd93555", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988bd255c283e765e6d3e018c3471ea931", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987257d88851a1a53c8215128cfeec8da7", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e987c2f3a5a41ff188ca714b02c61127331", "path": "../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98864498cae88883ce67633a57f8e6e763", "path": "../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/ios/image_picker_ios/Sources/image_picker_ios/include/ImagePickerPlugin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98cf4805ce2a441e4064afa4a27acd7235", "path": "../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+1/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9806c8b83fa311912e6c67a78decc631ea", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e986af44566fc90c710c3d3621da990aea4", "path": "image_picker_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98456b3a8eea63b5fb3331ee50168ffb0e", "path": "image_picker_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9829a9a90365a4122ff8d9f2d4913842db", "path": "image_picker_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ea510ccc95932cbd386bf138ad1867d5", "path": "image_picker_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98104302c4e45fd652cb47f576ea417dd4", "path": "image_picker_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98488f0f1e6241d7085b776f80a301a2f6", "path": "image_picker_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98f4fe302e533f5d7af0db7a1d90e9fb83", "path": "ResourceBundle-image_picker_ios_privacy-image_picker_ios-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983597c1f803296c0d8abfb0cbef375956", "name": "Support Files", "path": "../../../../Pods/Target Support Files/image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980b3459aafa99b539c103ced4cd61c7b0", "name": "image_picker_ios", "path": "../.symlinks/plugins/image_picker_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bf30be0bb88968f8ff5d2ae69916ce98", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/AudioPlayer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98026cc4493cd3288ba5e81d419be00f1f", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/AudioPlayer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fbd11ae0f83f22f32a3314f7ed352326", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/AudioSource.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986732e413fae810ab7f9bbdd809f03188", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/AudioSource.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ffc70ccca8c3df3a18e555dcccd81f61", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/BetterEventChannel.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9860d870228871367a726ae4bde3920454", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/BetterEventChannel.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c5ef3b1736ffca83dfa38df8443ce938", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/ClippingAudioSource.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98415ba6f19e908092e1627fe51f248a26", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/ClippingAudioSource.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e2009aad1e659d4ed2cc7bdd25f73e73", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/ConcatenatingAudioSource.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987db6161c030dc90023dbff20316fe050", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/ConcatenatingAudioSource.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9837c4daf2c04f87f2aa4ebea82d2be4be", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/IndexedAudioSource.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fb2efe9c23c5f901d59eca164e30d82a", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/IndexedAudioSource.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989351afa88c770ff42e5523f7cd284c7e", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/IndexedPlayerItem.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f4a7439ca9c338a761243fb7d9b54709", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/IndexedPlayerItem.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9819e46df489cf023354c3f9a8b38b5d87", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/JustAudioPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981313a3234a5df869a78f134eef79de79", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/JustAudioPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983fcd7ab8415d4794846859ab8adefef4", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/LoadControl.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b78670ccce078a27ff3451c009101fcc", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/LoadControl.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9849be19a0aeb8d67541a5784bbd11e879", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/LoopingAudioSource.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cbdf9dc5c893c509ee329dbb4bb01ee5", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/LoopingAudioSource.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b1a73c536fb73af279b0caa2c817fd64", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/UriAudioSource.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b23e09aebe233b22b2a3d85ca6c997c0", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/Classes/UriAudioSource.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ad611ca0139d29b9b69d233a595abfcd", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b7a35c859d993ac16a8b0e0655fcdacd", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9857bb520535870903ad871ae51595e9a1", "name": "just_audio", "path": "just_audio", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d9ac5295f373008c4afb6705f46d0bd2", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981f6ece29835b02839da0005fa9d441e9", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98388da6056f8a600ee01002b85f9653bc", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f7ed8c8050d118002e5d59517e0cfdfb", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984f5c25b7eaa392455bd081224a1399ea", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c52b05033a4244c22cb37f5a5bc936a0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9815c53d1bc95672ccf479fe23221d79e2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9839b994b484af095138ebc3d0fda53b5b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ca7ae187bb6d98e5a5357da8b747e9e6", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9839a34b03ad4f43d18f6ea134059b830a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984e6f20fa697893e5fe438b4e8f2ef560", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e981070ddc96cf4b33ae5c7a20d96b3c55d", "path": "../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/ios/just_audio.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e982dfb6da828c8cf14dc68280da09a95a1", "path": "../../../../../../../.pub-cache/hosted/pub.dev/just_audio-0.9.42/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98eedc9229139ce63db0a2134a547553a2", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9840ae833f5f3d2a88c56b0770797cbff5", "path": "just_audio.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9837fe4ff29e9a7a0440f21041a7216953", "path": "just_audio-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e986098f21c61224718626fab805f9d9777", "path": "just_audio-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98123bc2f097e2e36d0a9e8a7cbd93389e", "path": "just_audio-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984acee17bd749e48975e2c6a22688d93f", "path": "just_audio-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981ba4d86b81a9852b142a8bd9f6874b13", "path": "just_audio.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98896c988a3de55fc53bb6b3a9cb9cce07", "path": "just_audio.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c8ab5a7677f2cf5e220dc8a1f8ae190c", "name": "Support Files", "path": "../../../../Pods/Target Support Files/just_audio", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9875e2332b33fec0787b71cdb9ce5c468a", "name": "just_audio", "path": "../.symlinks/plugins/just_audio/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9888df0055f55b41ce6c636063e1dde4c9", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/media_kit_libs_ios_video-1.1.4/ios/Classes/MediaKitLibsIosVideoPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98953285e833dd5023a1d93ec06404b3ef", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98daea235c61d1d6d69932fb4fd9612ead", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9858aaf2d794b27c4300651358ff924be7", "name": "media_kit_libs_ios_video", "path": "media_kit_libs_ios_video", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e64b2f1c95a21eb32828ebd5c885bc19", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c81976083bcff3fdba216efa5b0d6ea4", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987694ddedbedb0826f9194a3ad20c268e", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988e6ad4e9bdb1c649b868f31820625bf9", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98903479cac324a9599040199771abd32b", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988cd9539aa9914e3244ac1b05b3d0dc01", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d46fb94116e53b50ce8b6f66c81f13c2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982787d7904459eb48fe7e4cc0554e91ea", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e9bab0f6f0634b807aa34831e8ca9c93", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985d09a824416b43acca2f531f5dd964d3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ab78ba3159665bf67d52954432942eba", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/media_kit_libs_ios_video-1.1.4/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e98ddaf36b5a1432a20f2c77952b9867b11", "path": "../../../../../../../.pub-cache/hosted/pub.dev/media_kit_libs_ios_video-1.1.4/ios/Frameworks/Ass.xcframework", "sourceTree": "<group>", "type": "file"}, {"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e9871252ef8e21338f99a1a8b25e106313c", "path": "../../../../../../../.pub-cache/hosted/pub.dev/media_kit_libs_ios_video-1.1.4/ios/Frameworks/Avcodec.xcframework", "sourceTree": "<group>", "type": "file"}, {"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e987c52854e8810d4c731928ff4f26b37ea", "path": "../../../../../../../.pub-cache/hosted/pub.dev/media_kit_libs_ios_video-1.1.4/ios/Frameworks/Avfilter.xcframework", "sourceTree": "<group>", "type": "file"}, {"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e98f6954d0245d891a816dc79dbf6b64811", "path": "../../../../../../../.pub-cache/hosted/pub.dev/media_kit_libs_ios_video-1.1.4/ios/Frameworks/Avformat.xcframework", "sourceTree": "<group>", "type": "file"}, {"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e98dc3224bcca19b76a486e5a46d47eae09", "path": "../../../../../../../.pub-cache/hosted/pub.dev/media_kit_libs_ios_video-1.1.4/ios/Frameworks/Avutil.xcframework", "sourceTree": "<group>", "type": "file"}, {"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e9872d57e8fbf3d189b333ef89ab47a79d2", "path": "../../../../../../../.pub-cache/hosted/pub.dev/media_kit_libs_ios_video-1.1.4/ios/Frameworks/Dav1d.xcframework", "sourceTree": "<group>", "type": "file"}, {"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e9851c03a6888681d89d33fbb8d50a8c5c7", "path": "../../../../../../../.pub-cache/hosted/pub.dev/media_kit_libs_ios_video-1.1.4/ios/Frameworks/Freetype.xcframework", "sourceTree": "<group>", "type": "file"}, {"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e98b986358007b9d0dc901d8cd8d91bfb9a", "path": "../../../../../../../.pub-cache/hosted/pub.dev/media_kit_libs_ios_video-1.1.4/ios/Frameworks/Fribidi.xcframework", "sourceTree": "<group>", "type": "file"}, {"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e982d274c0cb9836773d3a13cbccc4348fd", "path": "../../../../../../../.pub-cache/hosted/pub.dev/media_kit_libs_ios_video-1.1.4/ios/Frameworks/Harfbuzz.xcframework", "sourceTree": "<group>", "type": "file"}, {"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e98045a0b49c8133553ef1d320b2a56d149", "path": "../../../../../../../.pub-cache/hosted/pub.dev/media_kit_libs_ios_video-1.1.4/ios/Frameworks/Mbedcrypto.xcframework", "sourceTree": "<group>", "type": "file"}, {"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e98a2f614f0f8a7a422865f2e1d6c17bcb0", "path": "../../../../../../../.pub-cache/hosted/pub.dev/media_kit_libs_ios_video-1.1.4/ios/Frameworks/Mbedtls.xcframework", "sourceTree": "<group>", "type": "file"}, {"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e987fcc5f505615356ea3184a9e205b16c9", "path": "../../../../../../../.pub-cache/hosted/pub.dev/media_kit_libs_ios_video-1.1.4/ios/Frameworks/Mbedx509.xcframework", "sourceTree": "<group>", "type": "file"}, {"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e9861f7bee01ce76237fc29d4989bf74341", "path": "../../../../../../../.pub-cache/hosted/pub.dev/media_kit_libs_ios_video-1.1.4/ios/Frameworks/Mpv.xcframework", "sourceTree": "<group>", "type": "file"}, {"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e98779ff14fb719efa3c48ae3c133076fed", "path": "../../../../../../../.pub-cache/hosted/pub.dev/media_kit_libs_ios_video-1.1.4/ios/Frameworks/Png16.xcframework", "sourceTree": "<group>", "type": "file"}, {"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e98fa6afdff14a0ec848162e9f009be6ad1", "path": "../../../../../../../.pub-cache/hosted/pub.dev/media_kit_libs_ios_video-1.1.4/ios/Frameworks/Swresample.xcframework", "sourceTree": "<group>", "type": "file"}, {"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e982aaf20ac2b43526df1e3fd4b10712115", "path": "../../../../../../../.pub-cache/hosted/pub.dev/media_kit_libs_ios_video-1.1.4/ios/Frameworks/Swscale.xcframework", "sourceTree": "<group>", "type": "file"}, {"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e98c9bb5a09cb208d8bd3c8131692a30712", "path": "../../../../../../../.pub-cache/hosted/pub.dev/media_kit_libs_ios_video-1.1.4/ios/Frameworks/Uchardet.xcframework", "sourceTree": "<group>", "type": "file"}, {"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e98341e15e4a6291cb5c075a59ad220c53b", "path": "../../../../../../../.pub-cache/hosted/pub.dev/media_kit_libs_ios_video-1.1.4/ios/Frameworks/Xml2.xcframework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cd72ad0c842597baee9e1c18dd8cb60d", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9892115e062ca9a24e613a580b9ca1fd89", "path": "../../../../../../../.pub-cache/hosted/pub.dev/media_kit_libs_ios_video-1.1.4/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e980d80d751d5052a56ed8dd355e9e418d1", "path": "../../../../../../../.pub-cache/hosted/pub.dev/media_kit_libs_ios_video-1.1.4/ios/media_kit_libs_ios_video.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98900d7f55f8a9aa2a8d5071e7e66e79ff", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98113a664fbfd59f80a85fe27e158150c4", "path": "media_kit_libs_ios_video.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9836d1866eb7d71642f9ba5c3cd47bbff2", "path": "media_kit_libs_ios_video-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e983c3197d02d015a3b0eb2ae1570b5a309", "path": "media_kit_libs_ios_video-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fa4c41178e4bdcbd597ae7845d59726c", "path": "media_kit_libs_ios_video-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9890952d512d25b1c9522bfb4c72e5287c", "path": "media_kit_libs_ios_video-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e98194451a76da96b9bd874a3f11d7fe6de", "path": "media_kit_libs_ios_video-xcframeworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ee0c5ab8a3ec1728d377a4bc01bae4e5", "path": "media_kit_libs_ios_video.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d79319acd0a76fd426bf8ce11e58f9b4", "path": "media_kit_libs_ios_video.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982bf50b28f9ca457263374f44a6c70ac3", "name": "Support Files", "path": "../../../../Pods/Target Support Files/media_kit_libs_ios_video", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986989c8ff80adcef2538ad9a233aeb305", "name": "media_kit_libs_ios_video", "path": "../.symlinks/plugins/media_kit_libs_ios_video/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.cpp.cpp", "guid": "bfdfe7dc352907fc980b868725387e9836026a202ab4d13e4fcb65b2931161dd", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/media_kit_native_event_loop-1.0.9/ios/Classes/media_kit_native_event_loop.cc", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98c05df9e54a3a3a33c5aa99b61bedcc9d", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/media_kit_native_event_loop-1.0.9/ios/Classes/signal_recovery.c", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9810fd5092141a48241cb6ca06ec92ca58", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dcdc1f262b56d22ba3738c98c9da9a1a", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98641ef111c7a6bc67a7948817f8caaf22", "name": "media_kit_native_event_loop", "path": "media_kit_native_event_loop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f42b0564464132bd2f79194794e14aca", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d3c65c610c49c2052adc26ee7f0219ba", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9897a6e407e8404e400ce2abb8e65316bd", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a49dffb8d906fec83351e939c8a091ba", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984481d39f49900697ff75873ae5af411d", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fd12dba9e99a06742f7b2f8851c6705e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c8a1cf34d5934db0e497cf8f6d43b05a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e979bc995cc1a0439d40dee7dfac93d4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982717e4f118ec3b9e98bbbf42c8d31597", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b277f6fb54f59df6fda204e760f7a8f9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f56fd5c69496bebbd769eb83d50c7e0b", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/media_kit_native_event_loop-1.0.9/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e982f44f4d817fa462c70c98f775e8074b9", "path": "../../../../../../../.pub-cache/hosted/pub.dev/media_kit_native_event_loop-1.0.9/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98f420ad47792a7e9c3ef3aa7b0a10162d", "path": "../../../../../../../.pub-cache/hosted/pub.dev/media_kit_native_event_loop-1.0.9/ios/media_kit_native_event_loop.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c9261469d28f1292b2629800ec0a433f", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98bee55426b8fffc03525ae1e0dc49fb35", "path": "media_kit_native_event_loop.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9844343d844165ef1b571e511606f0a509", "path": "media_kit_native_event_loop-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e985e36835048f2ee72b709ca92b8cad3d9", "path": "media_kit_native_event_loop-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ba5cb17136bcda61b329adc159cbf9fa", "path": "media_kit_native_event_loop-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9880c2bfbf1c22a82ac9ad93e679de54a3", "path": "media_kit_native_event_loop-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a6d31a5442b2408b2695adbefd392f2a", "path": "media_kit_native_event_loop.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e985f150b35091c9ecabaff76e66293e0b6", "path": "media_kit_native_event_loop.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e3dfa772aeca5ba8ed587b697c201b5d", "name": "Support Files", "path": "../../../../Pods/Target Support Files/media_kit_native_event_loop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98607445c91dbc49c18ebc674813214afc", "name": "media_kit_native_event_loop", "path": "../.symlinks/plugins/media_kit_native_event_loop/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9848882290d98bb28fef2b0acad0f08c66", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/media_kit_video-1.2.5/ios/Classes/plugin/TextureHW.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ce9fb2689753541b15d413ea6acd0048", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/media_kit_video-1.2.5/ios/Classes/plugin/common/MediaKitVideoPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e0cdcf322420d7d4019bbad033d84cbc", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/media_kit_video-1.2.5/ios/Classes/plugin/common/MPVHelpers.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980a1e3aa4a1b78e95b5f5ac9d2c9ff832", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/media_kit_video-1.2.5/ios/Classes/plugin/common/MPVVideoOutParams.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b3af108506105050fd903bb94697cb73", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/media_kit_video-1.2.5/ios/Classes/plugin/common/ResizableTextureProtocol.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9830ef9ae1100a320cca3cc456707296c1", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/media_kit_video-1.2.5/ios/Classes/plugin/common/SafeResizableTexture.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984913dcd5db9b5e97de4775534631da9b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/media_kit_video-1.2.5/ios/Classes/plugin/common/SwappableObjectManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9898c5cf2562fe3b6e45b83b41791dafd3", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/media_kit_video-1.2.5/ios/Classes/plugin/common/TextureSW.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f9d1e4437f970d2bee42a0d8fe170be7", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/media_kit_video-1.2.5/ios/Classes/plugin/common/TextureSWContext.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ed13556fa0c97618e0b35ed967ae6b0e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/media_kit_video-1.2.5/ios/Classes/plugin/common/UtilsProtocol.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986d142298a106acc76be401604405b960", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/media_kit_video-1.2.5/ios/Classes/plugin/common/VideoOutput.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a53365354ccfe7c5a6c6a2a7a70d8c53", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/media_kit_video-1.2.5/ios/Classes/plugin/common/VideoOutputConfiguration.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980b8f048d2c4725dfae00781edd2cac8c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/media_kit_video-1.2.5/ios/Classes/plugin/common/VideoOutputManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98041163b0ecb66286116c4fdfcb000c60", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/media_kit_video-1.2.5/ios/Classes/plugin/common/Worker.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9827cbbcd9a1a148332f64e8223a86c05e", "name": "common", "path": "common", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98dbf7699dda65791871b81a0b67f534db", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/media_kit_video-1.2.5/ios/Classes/plugin/gles/OpenGLESHelpers.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984ddb84c3ee63270dd6e2a4185faaa859", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/media_kit_video-1.2.5/ios/Classes/plugin/gles/TextureGLESContext.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d0feb043fc8ff38caab40c11593abf13", "name": "gles", "path": "gles", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98db5599f18a69f33c0bde8207f22e815c", "name": "plugin", "path": "plugin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cb93bf99ec2df7e6cfed768bfed55ef9", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9816b4be00a1dadecd9e17e5e260645007", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/media_kit_video-1.2.5/ios/Headers/mpv/client.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987ca6f888e4055a81307e5ccac0ec5647", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/media_kit_video-1.2.5/ios/Headers/mpv/render.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983927f4d59224090cb175eaf1b26990b1", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/media_kit_video-1.2.5/ios/Headers/mpv/render_gl.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b744ece7907bff7ffcf5e57d2cdf1e9b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/media_kit_video-1.2.5/ios/Headers/mpv/stream_cb.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a2bee50d72fdbb9591f17dc19d8252eb", "name": "mpv", "path": "mpv", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e1c906d1c9175977c43afd5a742ebcb7", "name": "Headers", "path": "Headers", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985545a6c63856287bc36adf2877461d8f", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c49d0f8c648b6ffe761c7ae7c2ea1e4f", "name": "media_kit_video", "path": "media_kit_video", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986fab19c232004e9bb99bcaecf3c3588f", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981a09bc86d55090f66dbe10a8d86a1715", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c44782088f5243ce8928f820e5b3ea64", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989415ae8857ef68ced21b110a48d772ea", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c8b9ca8d31e0a2823d944ab8adda6539", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986c00f6fc5f8f2d8d432b1022bff2aa22", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a6dedabbbaecbb698715b3e7b8877cfa", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98460c4776c80a71143b4c3da7af51734b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98157d7bae8c83954d9a6aaac6616dc890", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98897bbeead6817c99707ae2c3bfc19a5d", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/media_kit_video-1.2.5", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e981169c409194d9f920b367addbcc9f358", "path": "../../../../../../../.pub-cache/hosted/pub.dev/media_kit_video-1.2.5/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e981c2cdc41571cd277a697672d17c326a9", "path": "../../../../../../../.pub-cache/hosted/pub.dev/media_kit_video-1.2.5/ios/media_kit_video.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fd0c03739af3ed0aa9d8bf95bfd73eba", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98e18449ed16de185d08baf3475db29b70", "path": "media_kit_video.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a5f32975d70fb0839dc6e1edb333f6a6", "path": "media_kit_video-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e984562cd6d175b82a0fca03acd3f910635", "path": "media_kit_video-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984f5ce5d6f6f187a3de2dd3cd171c9a34", "path": "media_kit_video-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ca6876c54bdcb97099d0dcbcdfa869b1", "path": "media_kit_video-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a1f414670bc404e772d262eb2b183769", "path": "media_kit_video.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e987bf5641bb74aba7f56ccb7c9f9841c5a", "path": "media_kit_video.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987c310f2235e0e6e397c2daf6e5d70020", "name": "Support Files", "path": "../../../../Pods/Target Support Files/media_kit_video", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9897b248f343ce33e2bfdb58f3082085a1", "name": "media_kit_video", "path": "../.symlinks/plugins/media_kit_video/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f831228570d46e01b56f1e4ae7b0af98", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/open_file_ios-1.0.3/ios/Classes/OpenFilePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98261debd36637793177907d68677bf7ee", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/open_file_ios-1.0.3/ios/Classes/OpenFilePlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988f1357435fb28b3d6584de0470f5c7cb", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98068a039843a6069949ea5a64223f6269", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988e540c5fa11364b838715b59c412240d", "name": "open_file_ios", "path": "open_file_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e56e65927809610c1399b17755a78df3", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986aeae171129e41ea53fd01bfa462feaa", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ac9883db189a985d93a739b0f326b223", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9844f2ff0bd767e00fb3366282b86c3d54", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98643ef3d69ff20020f6ddf2c8bb06d47d", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984bbadcd99c39f31e61a5a46bf31084b7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989e6106c25e9bc9e1f29a1e17ce07bb54", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d5c4786810c9144418d7c303d7683a0e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985608dfdea2608be63b5b2cc9a688b616", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9836c50285ec9326b15248c5a4eafa4007", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988edc4cfa59977745e415e70850beeafb", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/open_file_ios-1.0.3/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e985da4b9ee3c4fd9f006747b1873755c78", "path": "../../../../../../../.pub-cache/hosted/pub.dev/open_file_ios-1.0.3/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98df6a34aaef12830995e1df25b1cb39a2", "path": "../../../../../../../.pub-cache/hosted/pub.dev/open_file_ios-1.0.3/ios/open_file_ios.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984b16f2ea60233c9c9d79595e2f8d671c", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e985d7b4821c384a1599631a5556533d239", "path": "open_file_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f4e5d86cce107a0413bf9db3f786a0f0", "path": "open_file_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e983d8e824a7bc58c74739169ef36a71362", "path": "open_file_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983f1f9e9154f826bc09e9ce135a9ca9e6", "path": "open_file_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9887df3ca8e41e343543fe267cc0cdc52c", "path": "open_file_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981acff5cd4b85ee5e9ff0562eaf61904c", "path": "open_file_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9837416928533ae4a004361a15401d897d", "path": "open_file_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9853e456a249f28cea6538746f879d35f5", "name": "Support Files", "path": "../../../../Pods/Target Support Files/open_file_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c1db09e93430b93af40220c5b87ac12d", "name": "open_file_ios", "path": "../.symlinks/plugins/open_file_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c257d4b6ad9d40e3c45fe75347bddeee", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/openinstall_flutter_plugin-2.5.2/ios/Classes/OpeninstallFlutterPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9854c21eaf4be3d2117b6522074e8425b4", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/openinstall_flutter_plugin-2.5.2/ios/Classes/OpeninstallFlutterPlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982f3c1020f95390e7df4000e46003fa30", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b4d4d80ca9aebe5be0787689cfae50d2", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985e87bb9dd1688868544b178e8d727cbf", "name": "openinstall_flutter_plugin", "path": "openinstall_flutter_plugin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c2a762630ac90ad6c76d6a5a9b2696ae", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ae2c2a72da5573d2560b7eccad732e6c", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f2e36f9ffaaad51b509283a373a6722a", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98542614114dfbad4d4a5150924fc209fa", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984585762bc9c058d43aa7e9687bffe225", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982a56e3831b6b8858eab1e05e1408ace4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ee70af79f518bceb514a0c183691f661", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9854199675699db65015fbf5d43fd84e08", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dac4523ffe8a72b4a0a0f6c842a0e6a9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cbdbf538c4f6c07041b0099accad4c89", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9801b2414ec2c744c32d8f1ced1fd1b6cd", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/openinstall_flutter_plugin-2.5.2/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e982d1ba9693cf9136183eb61efb646ccbf", "path": "../../../../../../../.pub-cache/hosted/pub.dev/openinstall_flutter_plugin-2.5.2/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98aac7b3760ca91ac114d24451b10a7165", "path": "../../../../../../../.pub-cache/hosted/pub.dev/openinstall_flutter_plugin-2.5.2/ios/openinstall_flutter_plugin.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9813e9ba8bd77f8870b653a57bed7ecaae", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e985b68dc4b6ff3c754318beb296873e21a", "path": "openinstall_flutter_plugin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9879389dd6016e7cb420c92472ee5fe153", "path": "openinstall_flutter_plugin-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98103f8f8169ef87cb921826b84536e59d", "path": "openinstall_flutter_plugin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989162f20eebb6df60df8f7ba13696a80a", "path": "openinstall_flutter_plugin-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98610a7f5d0152b67d76fcb1693483e57a", "path": "openinstall_flutter_plugin-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ea43a2916442d54d05f83cc0f939ee1e", "path": "openinstall_flutter_plugin.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98e918ed91f9724b7aa50b53b35a457490", "path": "openinstall_flutter_plugin.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987c4997b84fbfabd1fced9583167ffbe6", "name": "Support Files", "path": "../../../../Pods/Target Support Files/openinstall_flutter_plugin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985e1b3a63ca00531a85cf1759b2d136fd", "name": "openinstall_flutter_plugin", "path": "../.symlinks/plugins/openinstall_flutter_plugin/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d0fbe099e5ad56d1fae300d9507c69dc", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/package_info_plus-8.1.2/ios/package_info_plus/Sources/package_info_plus/FPPPackageInfoPlusPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98546005f6a7d0ff727d9e06e17027aa69", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/package_info_plus-8.1.2/ios/package_info_plus/Sources/package_info_plus/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98773bbc3b579730e1d55636b383782c46", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/package_info_plus-8.1.2/ios/package_info_plus/Sources/package_info_plus/include/package_info_plus/FPPPackageInfoPlusPlugin.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98244ec7ad0a87489822fde63982fdc0c5", "name": "package_info_plus", "path": "package_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98773587c8f2675d7f3891826a6ca43ec8", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d9063b4cc117a5964bac709487e0c9b3", "name": "package_info_plus", "path": "package_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983764ca8b0c1011a96f123eb063f6841b", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98983168e1a6f4a83c3a11b2ad7c5ec380", "name": "package_info_plus", "path": "package_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98761fe7ca77fdbf2ff48e4ec0587efa00", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983b0f7aea8eec554552deba1aef24d151", "name": "package_info_plus", "path": "package_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989a250bb3e1d06c7be73e9bb83ad2ced7", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9810eb8b8df3bb32c8d88a150d13edcb87", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9812c656b19852ec267b44f3fd254817c6", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ca75f1e634e3fc209df61661928132ae", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9895ed61f68210493495046241ab2b8666", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989ca22db26f9e54ec89f6eeeb47de9fca", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d7b4049ed88316145b594554e42947ad", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e5db0280cd19bcf02a848cc82f306885", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b3543cdbe4e28422f105fbc409469a0c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98530f0a0b263ba9a38a22fe6121399c13", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98846b69c4535c6fe5e8f64730c001c78f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988b1ff1323aa9e06c02cfa755f4123d38", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982216b335cfeae009bc771233e5417733", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/package_info_plus-8.1.2/ios/package_info_plus/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9872d287dfb5431a723e9e772eb3018fcd", "path": "../../../../../../../.pub-cache/hosted/pub.dev/package_info_plus-8.1.2/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98b1d8405a1861aa59098975dd8a012670", "path": "../../../../../../../.pub-cache/hosted/pub.dev/package_info_plus-8.1.2/ios/package_info_plus.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983b81697adee4ce1445f45d23d36e2ad6", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e989bfec96ea33f4a8229761a43414bdf33", "path": "package_info_plus.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98320ffa5313d67def7917ca53b39d0c6d", "path": "package_info_plus-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e987c44184b124d925a86aa10c6e0136631", "path": "package_info_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989428248f3aa96cd68d0d450a4d94866e", "path": "package_info_plus-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e96d66c70e787daea7f6d6601797c52d", "path": "package_info_plus-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98c6e4c94fe5439194b78a1fb69e66fc91", "path": "package_info_plus.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e988d46c55dc313e8a19974f07d52b4e92e", "path": "package_info_plus.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98e4826c20ee66abcc737a494e8dff1581", "path": "ResourceBundle-package_info_plus_privacy-package_info_plus-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9856399969e6672335f5799b493ad44935", "name": "Support Files", "path": "../../../../Pods/Target Support Files/package_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987d3b44c8494720f7890d8790d6b5052e", "name": "package_info_plus", "path": "../.symlinks/plugins/package_info_plus/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f632303835bbce90aa61bc947ead9fb3", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/pasteboard-0.2.0/ios/Classes/PasteboardPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b9e84b6271328cd07e5798c12eff80b8", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/pasteboard-0.2.0/ios/Classes/PasteboardPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d9fd2c8cbbcd81e4db7a655f4b45769a", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/pasteboard-0.2.0/ios/Classes/SwiftPasteboardPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98983af5872f2321521c6f311f992339f2", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980746a3a1f8c0c31c0f03b3581b521f1d", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98644ff9c9e66bd11cee90869a2588709e", "name": "pasteboard", "path": "pasteboard", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986f1cb08d627d8e31a7a8713bdaea4642", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983966477314ac49ec258a5b512ac283d9", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9833b7df84f6991be1a766d85815322bcc", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982c68e3319a5447ed9fc958e41a6c88c8", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980ad2ec0b7d85b22c94b1f777058475a9", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98988f6dd7899f8aff3dba042ca9f7d2ea", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98469a9c26a69dc94dae30d6ec3377241e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981544b7d7d613ee015c0b533ab350d629", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985d0db55b9b562115b5cce1dda0e9ce12", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988f5f9c8b01b21a305448df5366494b6e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ad62787f4f736181cc3e75218e82b5fe", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/pasteboard-0.2.0/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98de9fb9162592376e955ee75ba5f3f80c", "path": "../../../../../../../.pub-cache/hosted/pub.dev/pasteboard-0.2.0/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e987d62efbcd5637b04e9168d130eb8bccd", "path": "../../../../../../../.pub-cache/hosted/pub.dev/pasteboard-0.2.0/ios/pasteboard.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a48213df0c9b5579580edea9ff1bc666", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98b82a4efec984d1a9e5510cb891fdfe3a", "path": "pasteboard.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ce84ba581481baa074a49071ed83823d", "path": "pasteboard-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e983a5c6c325ed205ac1132b8512fb6cf3d", "path": "pasteboard-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98395321abb74d6f396232537f4c606766", "path": "pasteboard-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fe10dea653581f3a0f945f3197df3d79", "path": "pasteboard-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b3aab41061e94afecbebe5ea33737cc1", "path": "pasteboard.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98943ebe45c2b28f419a78d2dfb56049ce", "path": "pasteboard.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989cb45d8eb2b2a009ca1582a61d17d71d", "name": "Support Files", "path": "../../../../Pods/Target Support Files/pasteboard", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e5f61cc54a13bec34184f2ea11ff047d", "name": "pasteboard", "path": "../.symlinks/plugins/pasteboard/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98f7a8cc36b4dd8b4d78409bb3e8c0948c", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9880e8aa36e4b037b5b3195083cf9b9dbd", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9891d1577ef2b50d40eecd3c1b29d55bc0", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988ecd82a1e918c2628ff0006177c73c7a", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983422d03a40df385e3bbfdba25f2b43ed", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9879919557a6ceeb71ac9ca825ecf1bd59", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9845eddb0a8c7fb8cb9f3011d9351b4fb9", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98426c4b8d154a817f0887b0f697edc174", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f91c5af1bbcec324f3f6ed69b8bbc6ec", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98266401650914d92f8a1c6cb49986497e", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987ca9ca02a9694cdc3a987c7e33f801b0", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98db82168cd136b3b83fc9a363d1fdad5c", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98819b583783e9f895998f6ecb5b076a4c", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f5440c8cec2a886891b5ec86eecba382", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cb8f24c6304bf1e60ecf46930e13c09f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/PathProviderPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9808c02fb8179f8edd0263b02d9a0e684a", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988e083a455c7cec8e36c3ff1aef9fd85f", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984733abc38c23a518947270431b2ffa8d", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989922e3634b3dbd403b4428fec7de164f", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9891efbf7885749ca7aa9a203a2c5277f9", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98996fbc78343f6db8257f7eddee306c37", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9823a8745b4c9146e974dd4a3a12df503d", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9869f387239165a25146a374c74227b9ce", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9880a49e914ecaf292c09426eaa84627a0", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cf8cd3ee0ef6872cf0cda90da165a5dc", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989a0968fb7c2464e20526fe55934f7728", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98587a335a18501efcaf84a6265dd6df5b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e46ed68b6c959dfd84025899bb286379", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988a1eab31407a3d1a2d0211b9037f50e4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9829ea5967c042746d9d22a7ea30f74e0d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984643abf96ac2179300dfc227b244c343", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986b73ee2ae51db0a8bd9af4453fae056d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cc15f660732ba984e4d2e23844d0fe60", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98f9cac37117c93f1ea4d1ba3ce64cccd5", "path": "../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98161963a5e2a59653d94d7548b683ab96", "path": "../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9836ded8844b5d06b34898d6713bdcc353", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e984c542de19532fdaeeae2db93b7aa5430", "path": "path_provider_foundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980c1935b8d68f6676f6a5d4c09d5aacfd", "path": "path_provider_foundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98ed11facfedb46cf03542a299a1405a4e", "path": "path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9847d93da3c8101ac70a4b54929cc1b543", "path": "path_provider_foundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983ee9898b86b80efc317d4748a424d8b5", "path": "path_provider_foundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9888c16ebaa4bd2706103c80f73f0d3d35", "path": "path_provider_foundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e989548668e92ea188565f50d6225e4043e", "path": "path_provider_foundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e981e8a715b48105e4c3d57cc8ae234807b", "path": "ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e2e8ec2749dc750866b45f9730ba0422", "name": "Support Files", "path": "../../../../Pods/Target Support Files/path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9839cac69f57bbe3ada7fcd56fdd874128", "name": "path_provider_foundation", "path": "../.symlinks/plugins/path_provider_foundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985c8421b5423f4a92f639d51c8f2de93b", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/PermissionHandlerEnums.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fd3f20096b6d4c0de429c9cb13962e55", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/PermissionHandlerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98529b4b1638379cc629704f4c813c4a58", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/PermissionHandlerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ebb8c37f58a04dad1ef32959af3e9719", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/PermissionManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a927f633e95d4a09dbd6cdcdccf2bc6b", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/PermissionManager.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983b296a2dfc8bce568a15f27de6df6617", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/AppTrackingTransparencyPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989da2e9599a4243f335210c7f3b26667c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/AppTrackingTransparencyPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ad70baac1fdfde0520be15dfd8951725", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/AssistantPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989c2bbbbd8d5561f19d85611a85b6c2b2", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/AssistantPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987009678c468edb1cdc33a91cc97e041d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/AudioVideoPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981872e0a9a654d158c8440721cc0f5a16", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/AudioVideoPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98478d5876a325f0a1a747c0cc01070596", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/BackgroundRefreshStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a4e02020ea0d9a6c8def9a2ab5235816", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/BackgroundRefreshStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989a5d1433335d5fdbc5eb28ea44f1159c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/BluetoothPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988fea24a01a6b63e71cce546aa3623917", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/BluetoothPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a1eacedad68cadca141a20f40b008225", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/ContactPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989061a636489e5c0fd818287f8bca0eda", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/ContactPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98efb14b13c4f7d7d443b6d8b6aca5ad16", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/CriticalAlertsPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ddf0b0677e6d7db15994fd0676f1f7e0", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/CriticalAlertsPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982a1e60b34c75488d93c368800b3ae49c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/EventPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980ec0cbb70087ebaa4f94b498348251e8", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/EventPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98349d37f6ceba36d21404aa5476a03e01", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/LocationPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a72a4c429044dba44e5f026d07f89720", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/LocationPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f6d4b9ba8965e81675c9ef4a892dc82c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/MediaLibraryPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98512737a5448e3159670bb8c28380d63b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/MediaLibraryPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98878e07060fdb018f08bac102bdcb1ecf", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/NotificationPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cf84bfb245b15a5a959c2c2c3d969805", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/NotificationPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9896092b178723e304351383f095b66579", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/PermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9847fc4f725bedf155863530de63fa7afb", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/PhonePermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985ebdaee286a04e9b5b9dd2f5aa42f416", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/PhonePermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989f24162be53c74b3115977c2aaf17ab9", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/PhotoPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9865e703b35b245f8d38e232e1bb4aaa94", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/PhotoPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a2dcc7208a6d49be07aa99d721383cab", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/SensorPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c7bc60b4eec2bb95f69869e626f7d33e", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/SensorPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989b2bb87b22c7c3d2ec03e24a545370fb", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/SpeechPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9832734980920c93089c05f61f97f91ea8", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/SpeechPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982dd408288d880a4c9e3b890b90884499", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/StoragePermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986d798bc8243b144be38334ee704d98b7", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/StoragePermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982beb92c50435da2039e3e5e711dff173", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/UnknownPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986ac7f00efe070fca7443de48c2318cb7", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/strategies/UnknownPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98876d6f4dcc4fc031ef2e424fe7ff8a7e", "name": "strategies", "path": "strategies", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983c221452a6a7ba015516d4589e80778f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/util/Codec.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9831e567900ffc3ad0fd1255a8c584368c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Classes/util/Codec.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a00085d65197f6cd200a95246be4c3b4", "name": "util", "path": "util", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98df1b6e93b2bbf0995e869ce149e7cb26", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98a685d631f5c32bc04edfaba83ac6772a", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980d33981c10babc610f2b2fef00cab27b", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b1a4004fdab5190bca044d599e7d576d", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987910ac30eb5018e75756389d54c0adce", "name": "permission_handler_apple", "path": "permission_handler_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bf9780682b2662f7d6b32441554ecae3", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980552afa0e0c8cbf09389f89a58dc8121", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9875eceb78d928cc7379c9b2d940e381a4", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98340193307af3b0d19a4ed7a26034f0a6", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98193daff7f107e3b03d247f7c5af2a6dc", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982a43fcbb27fb08bd73efc8ebd53fde85", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989395179a94d4211cb15ead635fd2bcea", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9847207f771c7fa247c7fb62f2380cf75d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98475a296d19f3dc7b7d0a45cf4b9487f2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a706289f1c9b3856d0f898e307cfc4e4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ff6ad20a1f53c3b1b910e924a8152236", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9869b61b0d0b9557a30d47ad060d421a4d", "path": "../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98c5efd3f549c5676cacbc0b71d3651cc3", "path": "../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/ios/permission_handler_apple.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9879d540435a6a548569e22881e0a5147b", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9808cdd67ef664cbff781cefcba0c69a82", "path": "permission_handler_apple.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98817c5e5c879208c0e4041f9f7f7bacc5", "path": "permission_handler_apple-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98f306322ad549a301f2ddff5e727c4f7f", "path": "permission_handler_apple-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986ab5d674917b905d82c8de466fd0e857", "path": "permission_handler_apple-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983085667c3acefa2fc7d377dd781ea7a2", "path": "permission_handler_apple-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9842b7a14b2573fbc760906ff84bdcd58f", "path": "permission_handler_apple.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d72d473d926dcc5fd6b3904d0521980a", "path": "permission_handler_apple.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b6c1ac11e192e19881607e4d62f4532a", "path": "ResourceBundle-permission_handler_apple_privacy-permission_handler_apple-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9851f363513cc3462811c62c6e63fc2b5d", "name": "Support Files", "path": "../../../../Pods/Target Support Files/permission_handler_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d620e67a9701295af3f38687843f1bd1", "name": "permission_handler_apple", "path": "../.symlinks/plugins/permission_handler_apple/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fd95f024e00d57244a031d9f62ed6a80", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/PhotoManagerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d6bb91726537e72566ded2b302a9a125", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/PhotoManagerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a41facae666f727a75d0d6bb7a0e3c92", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/PMConverter.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f360fc54f1e74bc61fc33051700e36df", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/PMConverter.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d1210e7eb583e632f81ac574885332fe", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/PMImport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984d9aa1fbe678b5bd4a42fd12a97db7db", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/PMNotificationManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cb00174d39c26bd5e422f0fc89d61f1c", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/PMNotificationManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9887a5c295d1193e1f1fe0109bc9d0c979", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/PMPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d9b8fa5209826c2c111a6d400aa20d46", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/PMPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d6024ebbdefb15e9364370e604e8799d", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/PMProgressHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9861f05404869dcef42680719cb5d93a65", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/PMProgressHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989e8e2fb50727a6c8cc41eca0cc679c64", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/ResultHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9857f20a6ab81757c1a1cdbf0248aade60", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/ResultHandler.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f242fbe293727019b27c6b2db29167b4", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/AssetEntity.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981da39883f767cc4c64d6df30d9ed6d7f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/AssetEntity.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dadefe572184bccaee4ecac939311618", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/NSString+PM_COMMON.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b6b0401eb2166614724ad3faca103446", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/NSString+PM_COMMON.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988bc33691d1d7f3922395a96e293bd081", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/PHAsset+PM_COMMON.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98129a9f334cb2d47449db3395ff7d7785", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/PHAsset+PM_COMMON.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989ba2265d4bf9b14189dfdf105fa8c8ad", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/PHAssetCollection+PM_COMMON.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d3dea1bf1c8b30706a2f72d4e6800c45", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/PHAssetCollection+PM_COMMON.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982cd4f23c3943575cd9a82b07cb21b9fc", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/PHAssetResource+PM_COMMON.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f7d8e501b4df5b769eb81379c84c81c2", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/PHAssetResource+PM_COMMON.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d546a26feff37940f5e8a2bc00356668", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/PMAssetPathEntity.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f004de0919f14100b591e5d72d438654", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/PMAssetPathEntity.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98480133d3adf9d6be56cfecbd32daf5ca", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/PMBaseFilter.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982d293f20da779570a1373c666f65db85", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/PMCacheContainer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984de0dc8d824d4393140b059971a2c967", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/PMCacheContainer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9872403aaaa0a64a149c7faf9c5bfe382f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/PMConvertProtocol.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98aaf1de819198964e68363df3aadd3ee7", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/PMConvertUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d1c589e4ceba4dacc10ad55bc9204375", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/PMConvertUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9849cca95266f1c0666bc8614335131d67", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/PMFileHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bdecb5def78f87f6f8734e840bf1d0ae", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/PMFileHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ca6694a74debeb436879529d1cd32d98", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/PMFilterOption.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c3a55edba48957e905b2752d60f58208", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/PMFilterOption.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c47defc4814f47655ce671fa1aee8b1a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/PMFolderUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989b5d5ec2bf2e447a69426fd5ed2a341f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/PMFolderUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986c51b693a6aa37b460cb6f7362ae6420", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/PMImageUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b53a1fa9d6c39a30a8e9c19fd3409d9a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/PMImageUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980b6b2c156424671ed197ab9ee47a75dd", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/PMLogUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9804e17e0792ee4d796d30e884cfb0b509", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/PMLogUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cec00c203ecaf5f441e109a45b4c30e5", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/PMManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988315bf7092b346de4f999f474f19c48e", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/PMManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98046b60067b6d734688546cd98836268c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/PMMD5Utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985b997d8bd38a628b506edde56a1dea68", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/PMMD5Utils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b19a364417ffbbc48cef30cfc989ffd6", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/PMPathFilterOption.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d61fde4cea407574a036a93940c0e14c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/PMPathFilterOption.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988c98208b26fec38401685509428d91c0", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/PMProgressHandlerProtocol.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fa1fd5b263127463d6b0124698e4af97", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/PMRequestTypeUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987ef1f74dde328820289adb687340d0da", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/PMRequestTypeUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980abcff148271f3f566741433d6334f21", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/PMResultHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bb73762cf923313a756b06eb3070fbb7", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/PMThumbLoadOption.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987c84694e99d7300d1cd595c9c8f45fa7", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/PMThumbLoadOption.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98758aaca41e18f5cc642a184489c16d90", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/Reply.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9832803e476ff240cff5ec7b4dafe9df19", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Classes/core/Reply.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d2da4c0e03e34071eb4dd280cbd72d8e", "name": "core", "path": "core", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98374812982039eee4432d8e155a826bdb", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e989857c71b75295a6acc43b0bf9e18f23e", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9811f1f1679149ff3a1b1613b681d53df2", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f7121477bd85e961e99cb4142f0273c3", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fa0cbc7ecb1692b74babee4489685e98", "name": "photo_manager", "path": "photo_manager", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984a7e82442a591c1973fb382c95402479", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984ad65d7e5301f7593896ee7a28a00376", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984d4c4fcdab8a3e501cf45ba833f31948", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9888d63f6178203f5b3f594b7a55b67466", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b3f90bea17a2906dfdd214e52a875fe3", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988afc98867aabd9f4018c84e5ef67fa9f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985efdd4c0030febd8926d719550a5c932", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985370dc56c387fd0551131e499e495a09", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9886e5916a39d0fad8df89958246819249", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9801417b459d2897d6692d5d89ada00862", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982536266251dd979716573876d42f6edc", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e987d618dd238c95c7eb1a21446d15e448f", "path": "../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98a1ff5fa3bb7f9331c3ff340f3460a614", "path": "../../../../../../../.pub-cache/hosted/pub.dev/photo_manager-3.6.3/ios/photo_manager.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980c6dcc7c034b1a9b31fb39295259d9e8", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98713d6b20349ee1dd54fe62affd24e357", "path": "photo_manager.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98401dbc5c15a2ae4ba9e9a092b5a2c8c7", "path": "photo_manager-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98befbb8a63f2ffd0cf629cf611c998ea9", "path": "photo_manager-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bebdabd61fa949b6bdfa20c5c82f0eb4", "path": "photo_manager-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f86f0ed48aa211ecf9bc4d6389e15640", "path": "photo_manager-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a73c5cd0aa0640824cca6909848b8887", "path": "photo_manager.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e983f4fad178154e66376af7e1e74569e11", "path": "photo_manager.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9833bda7a324face9e0d89378a796006fd", "path": "ResourceBundle-photo_manager_privacy-photo_manager-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98da5484595e68cd68b9f5c905cf80c895", "name": "Support Files", "path": "../../../../Pods/Target Support Files/photo_manager", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980bb40c0693d191b59bbd79130552885d", "name": "photo_manager", "path": "../.symlinks/plugins/photo_manager/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9885f5e115d11f4d3a857e145a3e5ce291", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/pointer_interceptor_ios-0.10.1/ios/pointer_interceptor_ios/Sources/pointer_interceptor_ios/PointerInterceptorFactory.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a6e3c419692355a0df543674aa62aa34", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/pointer_interceptor_ios-0.10.1/ios/pointer_interceptor_ios/Sources/pointer_interceptor_ios/PointerInterceptorIosPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986b0d4673736295c8ab803b7d89b3deb9", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/pointer_interceptor_ios-0.10.1/ios/pointer_interceptor_ios/Sources/pointer_interceptor_ios/PointerInterceptorView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9829099414643a7aa882aef26e9444771e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/pointer_interceptor_ios-0.10.1/ios/pointer_interceptor_ios/Sources/pointer_interceptor_ios/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985884a8b9d4094f99258db05e0dc6aa35", "name": "pointer_interceptor_ios", "path": "pointer_interceptor_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98de47e71b162a2904ceff0424f49dab80", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981618c17e8149908131e1b6ba19ae4307", "name": "pointer_interceptor_ios", "path": "pointer_interceptor_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e68905d2159dd42b9d9802777838c80b", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c9b9a7e420a2d408dcae1bdd7972619f", "name": "pointer_interceptor_ios", "path": "pointer_interceptor_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987a14064db7d703f07c4ade0c345f2768", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98192ff2688391763ee28bbd83b4e3deb7", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98881daf19e11206ca6163d562a8c1f3f4", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c7805c53fbbc73e39509986e5df6dce4", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98993b87b52b47c17327d2f20dee58deea", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e533a01ebc0b77b3f211985243c425b2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982f031e54e870396a8489a274d78a70df", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989c7af397d6cab0c1b109db4a161075d8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987812566693930d757df664b227594dac", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988a38447a049b5ea8968ca20d9a5f9a4c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988f6c92344bc91466ffe5d886b02d4267", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989a551b412898b1578eb214021673f6fe", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9854887b580ccdca2a3e66997bc8ca5316", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/pointer_interceptor_ios-0.10.1/ios/pointer_interceptor_ios/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98e9aea00f64a425d14c22d15fa38846d5", "path": "../../../../../../../.pub-cache/hosted/pub.dev/pointer_interceptor_ios-0.10.1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98e47a25c53ab382c27830a6cad6f97915", "path": "../../../../../../../.pub-cache/hosted/pub.dev/pointer_interceptor_ios-0.10.1/ios/pointer_interceptor_ios.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9869e8c4cafbbf833904d945538e314580", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e987e894f97b45dba683d5f76755d8212f4", "path": "pointer_interceptor_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98125ededa7106d7da94b69babf2a4dffa", "path": "pointer_interceptor_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98f677407500d2e05908d9cc552062782e", "path": "pointer_interceptor_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9864092b7bd0157c4a9cc2f4f00534d2ba", "path": "pointer_interceptor_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ae390eabc34ec4fe2173582a9b42746a", "path": "pointer_interceptor_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e982640f6a808cdaefacdfb7e3f74b0289d", "path": "pointer_interceptor_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98bbe8f26e39ec8f93954299f2c0fd2daf", "path": "pointer_interceptor_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98ebe1610318133203e67d17f42bcc8cab", "path": "ResourceBundle-pointer_interceptor_ios_privacy-pointer_interceptor_ios-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985000a0f800680bea9965c49cb0519ba7", "name": "Support Files", "path": "../../../../Pods/Target Support Files/pointer_interceptor_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9899d00b373661e56d789f4ace7cfee126", "name": "pointer_interceptor_ios", "path": "../.symlinks/plugins/pointer_interceptor_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c7f8c99ff1c887a0d01a6e102bd4b410", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/record_darwin-1.2.2/ios/Classes/RecordConfig.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98de1895447f4a395994b21a7ee15092a3", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/record_darwin-1.2.2/ios/Classes/Recorder.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98170299928c1ac3fb0c405e5ac85fa084", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/record_darwin-1.2.2/ios/Classes/RecorderFormat.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e16183f2a797fcf22da6f88cf67cdb93", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/record_darwin-1.2.2/ios/Classes/RecorderIOS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b54265a9f4546471f9e1087caa24b81e", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/record_darwin-1.2.2/ios/Classes/RecordPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987043a5dd2ec4f77fc3d08203c0ee2940", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/record_darwin-1.2.2/ios/Classes/RecordPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984e2d5410e616f6829e525c47b3f744ac", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/record_darwin-1.2.2/ios/Classes/SwiftRecordPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d58fab2913d9ff2103fd95d67fb1e38d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/record_darwin-1.2.2/ios/Classes/delegate/RecorderDelegateProtocols.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983644dec29ae7200caab249b054049980", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/record_darwin-1.2.2/ios/Classes/delegate/RecorderFileDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b70c32b5d6d704f101c7050e1c95fca2", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/record_darwin-1.2.2/ios/Classes/delegate/RecorderStreamDelegate.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a537b9eb411f8c40b8c59e7e7c31dd42", "name": "delegate", "path": "delegate", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a87221eba816241c349d7ef2725be4a8", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98c06fd2fbfb958fb687a19e35b5eaafa2", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/record_darwin-1.2.2/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c68ea74ff720bd9f1cf3da2fcbc5192f", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ad20599b5d15bf2c12f843b04fa158b9", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986502f746c4e6a0a92bf13f7012d8c6fa", "name": "record_darwin", "path": "record_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a857c32a0ebac86507fa9364c4436ff4", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982fd1cfde785f8c44aff16fa938d197ae", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ba717d3843f8095688eeb2ed84c478e2", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988b9a05b947aa10b3037de1eaac71e517", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a22bb4db33dc49976cd5d9fc44f1eed9", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98770ad0d2a5bd4692d40a7e37793eb92c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9839bf9a21d7e6aeb803dd9267e9bd97da", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d63e06cebc4df943ee0b62abdb58ef09", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ee9bbd6f94bcd6dfdfe3221dc1f7d9aa", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cc979627173bbb8956c35e78ea1a1993", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9802bf10dffb6dbe167c8251564fb51385", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/record_darwin-1.2.2/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98f97f975e73d24dfa2520d2bc182f9fe1", "path": "../../../../../../../.pub-cache/hosted/pub.dev/record_darwin-1.2.2/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9877d832b360798b230eb53764cea86977", "path": "../../../../../../../.pub-cache/hosted/pub.dev/record_darwin-1.2.2/ios/record_darwin.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988006c6a17a882a01d0401ff1d3079da7", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98454b66b66027d50c8e1a65e0996b350e", "path": "record_darwin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9827df993835fbc3c9a0ea03011c421766", "path": "record_darwin-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98613d776f4443bba9ee4646baec964587", "path": "record_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a9adc87bb244992f702b4384b27dc7f9", "path": "record_darwin-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98af5ddcafe345ae2eba9392276b42fab7", "path": "record_darwin-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98c050137a3ef9aacfe710c7906dee7317", "path": "record_darwin.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ee24dcbc18d512e0554511d7af53f15d", "path": "record_darwin.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e980e3e455eb473323a673574008e26237d", "path": "ResourceBundle-record_darwin_privacy-record_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985716e955c68cb942e8b561c5ff3237f3", "name": "Support Files", "path": "../../../../Pods/Target Support Files/record_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98da95ce2712d8813ec14af9107c0a80e3", "name": "record_darwin", "path": "../.symlinks/plugins/record_darwin/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98202fff50c3aa00aab675d64bb1058981", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/screen_brightness_ios-0.1.0/ios/Classes/ScreenBrightnessIosPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9802b587efda582062dc977d56370f0e6a", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/screen_brightness_ios-0.1.0/ios/Classes/ScreenBrightnessIosPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98842b342081dc5796056855a966c4138d", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/screen_brightness_ios-0.1.0/ios/Classes/SwiftScreenBrightnessIosPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98137214128018b33308a0dd8ac3fac6f5", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/screen_brightness_ios-0.1.0/ios/Classes/StreamHandler/BaseStreamHandler.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983d580ce0127773e9ebed3ca8daacd2b9", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/screen_brightness_ios-0.1.0/ios/Classes/StreamHandler/CurrentBrightnessChangeStreamHandler.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e32b880dc6bf01a014d8d6780c7c9b3d", "name": "Stream<PERSON><PERSON><PERSON>", "path": "Stream<PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9843a7e10dbb931508039a4f877ed75f92", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f62d1bec80df58386235ce771edd8f27", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9814ada067a5004f78a2bebdb77b4ac890", "name": "screen_brightness_ios", "path": "screen_brightness_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9812c45071e9ed56db31077dd10ed96fc5", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98696d2c686ae3b69fd12eea1376061ace", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987ea11533f42211e8258e06fa602d1e20", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98051dff7f6341d010698a1037c00ed5eb", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f28d0459401e527c7c7638b22ab35a5c", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d01e71823326fc01401923ff8ecaaa7f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988217e6893cf59b966645d0f9b4ceaa09", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988ec52471d09e43f4fef8f45906fb7bf1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987117708055d1c4e12fd65de4ffb55ec8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9811f085fc49326af8dde73569683b0696", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9832e8c1e483eabd3a1a4f2d3ba9b4c3a4", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/screen_brightness_ios-0.1.0/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e988284b35ad8f4aaee2b8f0c9fd50a210c", "path": "../../../../../../../.pub-cache/hosted/pub.dev/screen_brightness_ios-0.1.0/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98cc344a61985b55f0a0adb2e54c9dc799", "path": "../../../../../../../.pub-cache/hosted/pub.dev/screen_brightness_ios-0.1.0/ios/screen_brightness_ios.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98effa14a01166e0cf824f76f3763543d3", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e984ee1813167e73dbb39be908ca3c6ff3a", "path": "screen_brightness_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fcab7eb2155896117b77d5b9363ba6de", "path": "screen_brightness_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98a8a4627e7446db1a00f214398ea0e79c", "path": "screen_brightness_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f61b170ec990b380217340225e0e2fe3", "path": "screen_brightness_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e669ec76f16c776bb1fe870af991997b", "path": "screen_brightness_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9892b46d77d48918540b631b8b12233de1", "path": "screen_brightness_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d4aacc5704cf7e1589eedd39f68f1505", "path": "screen_brightness_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983c2213ee9df7919527a91459317dff85", "name": "Support Files", "path": "../../../../Pods/Target Support Files/screen_brightness_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ebe477e2e0cac9dee464ed93a9f32317", "name": "screen_brightness_ios", "path": "../.symlinks/plugins/screen_brightness_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98380a2dd713718acd8412936f7cae3de4", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sensors_plus-6.1.1/ios/sensors_plus/Sources/sensors_plus/FPPSensorsPlusPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98829004ce552b9809eb27ffaf989f2da8", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sensors_plus-6.1.1/ios/sensors_plus/Sources/sensors_plus/FPPStreamHandlerPlus.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e989524f1de4e73221fa24a6436b1f31ce1", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sensors_plus-6.1.1/ios/sensors_plus/Sources/sensors_plus/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980de92c4ddc969a82b8b50170c86d1429", "name": "sensors_plus", "path": "sensors_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988b016df4032972adff666cb24d8c813f", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982bfb8e598b6d14819048963620ef4b1e", "name": "sensors_plus", "path": "sensors_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b1dffcbe19d4d393d4906e069c576ce7", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98760c1ed6a83284f5686d8fd0891d8cb1", "name": "sensors_plus", "path": "sensors_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984bf1acc209f5159d6236e3acbbbbeb6e", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d6c5006517e499452c73e4823cd2e3b5", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eab839992161c778fe5c960edbf9b6f5", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98579b482e87f0a0acc6fcd71744ef2a65", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c36e09cc204bc7a728cee67ea382fd94", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d3cd17aa9d2c1710618fca625250526c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c574cffc87d361298f7e85958fcd2fb4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98090825e257cca8e192812102d4d98d05", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9855e91d40445e84cee21c2583760876d0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988b1345348f1e1d75261271d6cd0659f7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980d11b6a230d828dce37bd24b138845c5", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b15e6cb051dde09bbde50e4025dee6fa", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d2ea148ee10234541e4db124cc63b44f", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/sensors_plus-6.1.1/ios/sensors_plus/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9824d18e06448b55220708f89382d35796", "path": "../../../../../../../.pub-cache/hosted/pub.dev/sensors_plus-6.1.1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98772afc82cdc9c3b0175b218d0881ff72", "path": "../../../../../../../.pub-cache/hosted/pub.dev/sensors_plus-6.1.1/ios/sensors_plus.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9877e90d32a6965f0650fef993d05d1b1a", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e983410d910992ed2f7e099b593076c6fd5", "path": "ResourceBundle-sensors_plus_privacy-sensors_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98822da31f7af4feba7b3f76d9a17c7f3d", "path": "sensors_plus.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e2db66509bf22f49c880f908987bf0b3", "path": "sensors_plus-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98ad8b80775a79a57d09cd79dd980bb7aa", "path": "sensors_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f07f531981a25976a206917b6c1b2c86", "path": "sensors_plus-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9826d5c08c53c1d25dc83f663bef67a476", "path": "sensors_plus-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98c691c9a3b4f3693a17d770840ef5a2c2", "path": "sensors_plus.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981339b00b6a5c6814887ef863675943fb", "path": "sensors_plus.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985d49f3fff81d6ff5b5c60bc71e9ceb3a", "name": "Support Files", "path": "../../../../Pods/Target Support Files/sensors_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a81433ec7cc705acd60f6268e1f0f515", "name": "sensors_plus", "path": "../.symlinks/plugins/sensors_plus/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98bc444a3978d9455e91608a4e5c0c7080", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9841252f4f7fa7339d7f88c570be5342b1", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9828cda12f710f96f9dba76d70accf0636", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dc197a96a8ae1ec6781c974fd1419206", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c5bfd388346ff6b7f80bdafbf84b969d", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986487d7abe54c4a307555343e68b215cf", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9863f874740a88c9336f611b3666d59d46", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c71f66571b373e322f08ee6fce52dbeb", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98484bfe5d65ba833acdc56d8df95bd992", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c6de83381012914fb86d516902f34581", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d4dad0241e264cf0676ce0e55a3d3b99", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e514c766f0b6e90647d2b78bbe40d2c0", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f5bd09f8a74410546a0f92ed4e9e8fff", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d906091a9c3fc820957d15d7777cc342", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98652fc33d3fdaa056ec99cf672a825bf1", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/SharedPreferencesPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e6ae4f6d0492865a25e93ac700412c20", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9897ddf5efaea27b670d57bb60b9a8da01", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e29706fe6558fa47920e1e7e46c1c2fd", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986a26ce22649332d31d79c707e9761205", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fcea06278cee12ceade1386296eddc58", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d4d9e6fe06a538368007c4bac06f850a", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983a78c37e32dfb41da2dda55b5be1126d", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98776e3bab5fb5d667273ffbe3611f30b8", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987a69cac17fc7a97fa0dcd88c65ca86c3", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c2de456f564c2b16a1c0492cc105f890", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981542f5a848118922a4b05e23dd41d0c2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981dbe519bd9a52bcd64e172dde3e046dc", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9818c93335df246ef42eb2ac7e3bb71552", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98041042638fef6f53434b27f4468954b2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98204dbd524d9bc8fc739544e6abcd90bb", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b1c7386b53f6bf9c3cc54a14455b3e50", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9867d810ee0a0bd7e57d27e683af41ba4d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987e86d436321970d9ab99d35545cf20b9", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98a970ca9b4a42ae9cf8d182f8eaa27fbe", "path": "../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9848db723f483431a2da0d507f52ee337f", "path": "../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982b3a972cede2101d12ff4f2914d44ba0", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98d661488fe1cd0f7216e0927e093b1402", "path": "ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98cafb30a22483e8e4d87dd13aeca80bca", "path": "shared_preferences_foundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9844a086d74729c6977ecf65e01ba160a3", "path": "shared_preferences_foundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e980284d682b600f266b57051faed8d04e7", "path": "shared_preferences_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988b9ce3443290c82b8061bfd8ff15d584", "path": "shared_preferences_foundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982a1b563b039af1446185982ee36d4c98", "path": "shared_preferences_foundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98e00a3b7efc5049a6dc2ebc42c3395b02", "path": "shared_preferences_foundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98be1d5f5f7eb16ce0fd9646685cc40758", "path": "shared_preferences_foundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d0a28b789c82cfc008b22ed84dfaee17", "name": "Support Files", "path": "../../../../Pods/Target Support Files/shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a67c35e0752350cdeb842e924cfe5f77", "name": "shared_preferences_foundation", "path": "../.symlinks/plugins/shared_preferences_foundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e987263bed9ed8d215a1479f6867e8618da", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98dd81da5100b3b07bb8bb91b247b4c019", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9871148c805ba05cc1a2fe3847a4f8ae76", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98170eaa452fabe02db922bc719c0e02e0", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a4c3ef46ec664ac96e689c4900d78eab", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a24b58a55338c69f977f4db3268a24d4", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ceb439c13b15d8da62efa9270fb3820f", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fb843af8659be42288d6c113ec29ba32", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9870571f780533d44d091828e016a0653f", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985492a1492cd837c217d098a81eab5cbc", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9808df65641aea670d2da9826208c6fde1", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d9816afb9ff04ef3ecdb346996a7b21f", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98efde710343196ae9c63b2fd3fac66565", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980ac0f414c0f4b8e9ced92f5246e2a501", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteCursor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ae838417621791eb33baf2a56711738b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteCursor.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9829a0207f2244b1a22c6054e52012591a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabase.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983b08f503077d4333d902c56c932c67e1", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabase.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980d0e0527446da7d06641d2e845853c27", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98552c4d3f8c66075378d80eaecf0bdbca", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseAdditions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9861d7ac86db7e5366b82fa6dcd2e8d659", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseQueue.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983464e880def62cd5b24d5328466fdd3f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseQueue.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988e2456c28764735c298279a77228f8c5", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDB.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987f3ee025882d65063478f173a46442a2", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinImport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986f9d5a6bfdbe7d6a903175bedaea8782", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinResultSet.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9838092982cb6e5d14f7c32dc4b2f66cf0", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinResultSet.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d54f03dd0b6b411afe6ce7218536f964", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDatabase.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988ca48d26af48b127ba9214a0827f73ae", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDatabase.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ea6b97a674d90b944102498fd7eb7515", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteImport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cdfa7023f0252a528dd185a834441cba", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ff6e4907ad997dd332d10eae73df974e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984042735bab5a5b02d532c7f7524aa0e4", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqflitePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98da646a0d7e2451d0ba0817e10ca9e624", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/SqflitePlugin.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d68bd90d7a72895ba419fbc6e5e2bc9a", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/include/sqflite_darwin/SqfliteImportPublic.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989e1b3a3db3e9e2b1eeafcbe7cd7180cf", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources/sqflite_darwin/include/sqflite_darwin/SqflitePluginPublic.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d510338708c51e464e984f38519a305f", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9879a958c82f02a98408e9cd2f6a3b0220", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982a7f57d58d6bc3a500a1bb396b17bd01", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d88ce57d0f253826f3942d54d46f7a8c", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a0d3952ab9aff01fd58d89acade1cc58", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985092ee9b5cc9a6551d3c8f7b39ef2607", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cf5bfff147931948536efafa7346a1b3", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98960a44eb4ddd18c044d3cbad0e45d16d", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9818cc1aaa17a15d7bc32817fc3ce7037e", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a4d9d2d70123ed809e30f0f107a1b6bb", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9872e4efffcea8890e5a07efb6a3ab135f", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980c4d80845a4dd7e664f74292d4083174", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980506b7c6172248ffeaacf58e9cea6f69", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ad30e57673b9dee444b813fce4ad5b3c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989ec0f32da01f9e400accf59ff224e8ae", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b652b3fef22d3aa9bea7cf48f9b99d06", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9856ca717d622d17561c9be5a4bfda6fb7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b6438ec8f930fa03eeeeceda0a21d520", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9821d6308553573938156eea580f109772", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986b382b0353703cd44155a53013878f80", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98679ee07fb44ffe014c8c0f883a6d6570", "path": "../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "bfdfe7dc352907fc980b868725387e981950e091fb6c97bf4d3ffa8b7a5e0149", "path": "../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/README.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e983d0c018628dc1e4a572564fa9e3ad010", "path": "../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/darwin/sqflite_darwin.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9835719b8c4fcb5f1ae5b79ed5bcd5486b", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9864aeaa515330dcb7cbde33d155077e1f", "path": "ResourceBundle-sqflite_darwin_privacy-sqflite_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98481e5d9b575a36e26540d6bc25d0df6c", "path": "sqflite_darwin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9830d551875b1962bf06de69ae43fa0cc1", "path": "sqflite_darwin-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98544caa9840c60e1fe03c3005894d0f67", "path": "sqflite_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982f7a128c6e322939c80c599051346c33", "path": "sqflite_darwin-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fed30a68b511bd6748901b432cb74c8f", "path": "sqflite_darwin-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98fa40327309c9da25078a2a6a9a30206b", "path": "sqflite_darwin.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e982a1d90b67e029324791e3441dbef970b", "path": "sqflite_darwin.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98625fdb47350f5e4797581bc4adbb556a", "name": "Support Files", "path": "../../../../Pods/Target Support Files/sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d41fcc51bd576143cf09d0255d049f4b", "name": "sqflite_darwin", "path": "../.symlinks/plugins/sqflite_darwin/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9862915b59a65c88809a8affd5f718298e", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/TencentCloudChatSdkPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983c8e8331ab4b8adc87d8fcfa98be7038", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/entity/FollowInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9883e4f388e760a688f1d1171be86a2145", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/entity/FollowOperationResult.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e32add709c2fff3a0f194fba1f4eee8c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/entity/FollowTypeCheckResult.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e06ca55505d73b43e7c945c845d36092", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/entity/FriendCheckResultEntity.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a2d5e2acbab786a90e1a028608b83ff9", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/entity/FriendInfoResultEntity.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98972d9e4e4ad39522b22723b7ee08b988", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/entity/FriendOperationResultEntity.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ffa44c660b3a25840362b325f48b353c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/entity/GroupInfoEntity.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980e60edab10165605d9726422869c8c70", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/entity/GroupMemberOperationResultEntity.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9875b0498c7427b3e2fbd9ee8a44b09c20", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/entity/OfficialAccountInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e84a9ffc089898e7ce2ce29b01ecc0fb", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/entity/OfficialAccountInfoResult.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9837a7086b4f81a0ed099ab36a8da40a8a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/entity/TIMGroupMemberInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c4d97f4af80bb371661763c39a1c08b2", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/entity/UserInfoResult.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e2ce8ce7ab5c6b84084588ffcc249833", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/entity/V2ConversationEntity.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989eeb4b65f4ad3a70bc18839e3c3e69d7", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/entity/V2ConversationResultEntity.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983b7fc865846aa78322f0a59bb5b4818a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/entity/V2FriendApplicationEntity.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986ee2645f929aed9849b7792427b9f254", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/entity/V2FriendApplicationResultEntity.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987b64cdb732ee8fe28e1624c01ef8163c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/entity/V2FriendInfoEntity.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e8ee5d60e187c477dc0720795fc3b135", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/entity/V2FriendInfoResultEntity.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98acb1db9923d76e97288089f91dc34824", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/entity/V2GroupApplicationEntity.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e0658ec2255e146125b242aa827d7854", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/entity/V2GroupApplicationResultEntity.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e04d9d1889466b3c6fa73b6d3857fde0", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/entity/V2GroupAtInfoEntity.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e85c149a5b3b824f1514271fc3dd0a2b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/entity/V2GroupChangeInfoEntity.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98611bbd7f31722bcf30c3ee7b05dd68c2", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/entity/V2GroupInfoEntity.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981f36de6c4dfbd7e8f77c160b38339995", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/entity/V2GroupMemberChangeInfoEntity.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984b27bbcd6661d34111051a81503210da", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/entity/V2GroupMemberFullInfoEntity.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98aee24f33f2aae8a346355332fee54a4b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/entity/V2GroupMemberInfoEntity.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98eeb1352cfcb343cbaf0d5efd72882c15", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/entity/V2MessageEntity.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e1161dee93d0fd6081a0ab1fc138a6b2", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/entity/V2MessageReceiptEntity.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a5b408252b7e138898128abd1fb5a4c4", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/entity/V2TimPermissionGroupInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982f470eb7ca038b6e61edd5b77e2365bd", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/entity/V2TimPermissionGroupInfoResult.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987a96256c1d3f21d229495333e72a9b0c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/entity/V2TimPermissionGroupOperationResult.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ef7c9dddbe5bb495b05f60d1bf427afa", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/entity/V2TIMSignalingInfoEntity.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b7cf7b998d635753490167b8b71a611d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/entity/V2TIMTopicInfoEntity.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ee0153ab20d9b826a3c3b17e67c65dc1", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/entity/V2TimTopicOperationResult.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a321e0bfbee07a9325b7b9e99525f8d3", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/entity/V2TimTopicPermissionResult.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c4cd251c2faa76d698c917ba54f942a9", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/entity/V2UserFullInfoEntity.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986998228b7602776370dec01b347950af", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/entity/V2UserInfoEntity.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9850d664d19f98426489a572621a270414", "name": "entity", "path": "entity", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d68bb13ed90570abf2224ef64a7ffe50", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/enums/ListenerType.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987b024e9acb5b8aa843ce73cc590c1571", "name": "enums", "path": "enums", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a5ef6d3d6340ab91dfb8182b13878d82", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/listener/AdvancedMsgListener.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987dbe8b43adeb1b18cb53bce13785569a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/listener/APNSListener.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982853fe099ee18cc0765b885996a32b72", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/listener/CommunityListener.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983d3c625217da425b02b31ded64efa4d6", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/listener/ConversationListener.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d99bc96510d796edd245440758a7b7c6", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/listener/FriendshipListener.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98178af52f6f70b560bc27456b44502ff5", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/listener/GroupListener.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986913ec2930da6eeae8f2c3f4cc6d2186", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/listener/LogListener.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ad27ad7c7360b9272bb841ae661389d8", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/listener/SDKListener.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9860fb974810f88ca7ce0e4ac06c9b044f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/listener/SignalingListener.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9835d7ef45b6d4c92ddeeb9379e36901d9", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/listener/SimpleMsgListener.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9869a863167016ee85dd3bcbd8cc77d59b", "name": "listener", "path": "listener", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989ca819e3d441832a5da05477659cbf0c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/manager/CommunityManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9821b1f2a72ec989b97165c5bffc65f1df", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/manager/ConversationManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98624e51350295cd0d1b834a5f60390564", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/manager/FriendManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a9fbc229c1789b4376f9cb85b94b8271", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/manager/GroupManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9864c9be3f7084903fe47eddda67104c38", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/manager/MessageManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98126ab231080cb8608230a4260f608e36", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/manager/SDKManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ec4b4500d6008fe3f054cf0e0b954df5", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/manager/SignalingManager.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9834e6fa850aed70e6aa77e0accab05ba3", "name": "manager", "path": "manager", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987a16e08b2733a222fc31db8943f6f061", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/utils/CommonUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b8fcabd420e00c9805fc437539881d14", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/utils/JsonUtil.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a1485881a555a27ab5a1bd671bb12820", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/utils/TencentImUtil.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c4e99058b7a418beb81d02ab320b82f2", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/Classes/utils/ThreadManager.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e4e24e5f6d7f6cda3efc48c8d0b47eea", "name": "utils", "path": "utils", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f33b55e9b3db81b9fc2ab69f69aa1839", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fd74bc346304b8936d374284d3ab621a", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aced18f35a7f3003d3feb6c7b862f342", "name": "tencent_cloud_chat_sdk", "path": "tencent_cloud_chat_sdk", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dcb33a435163713586dac150e2d15f58", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ca6167e000398a87a4cd0556f5d254b3", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f6621e417b2676b9613df8ca87c407eb", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c3bd1136a42a501e7ee53e2146c75b33", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9871efa56d7b4504b1eea246e838b099c6", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985990f98aa94e85a1a13e37ee5836600f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984cec3213fb1a0c59c1f6e648f8485a4a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98119f94d0729d128d5a1b600dc820f877", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b255b91f9d2e30a178b2c46e62406a9d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983619af4e9fde16f5fda6d4d44c58b3a1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9827af9fadcd56fe96735422c28ffaeb63", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98a423dfd8b41e4a6c73915f69651c8a50", "path": "../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9883bfbe8d6229e1f8f4708708bb11640b", "path": "../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_chat_sdk-8.3.6498+1/ios/tencent_cloud_chat_sdk.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985701178c51358cf8761b47f83e4b662a", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98d5b6f0670e2896f9bd7fa6790517951d", "path": "tencent_cloud_chat_sdk.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987fd197c85712a8d50bd3597c8508595b", "path": "tencent_cloud_chat_sdk-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98ff2df7bc4ad5b599bde92c7c55765e95", "path": "tencent_cloud_chat_sdk-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bb79889070388f8f9068aa15010d8e7d", "path": "tencent_cloud_chat_sdk-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9888fc0a6e1ef7f9b4256a8ac9d0ee6c4d", "path": "tencent_cloud_chat_sdk-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98da9e59a2d1db95208045528360585942", "path": "tencent_cloud_chat_sdk.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98910fa1ac98aa04a00e5452fb03cee8db", "path": "tencent_cloud_chat_sdk.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98865222cb402255bc3d8980af9b791080", "name": "Support Files", "path": "../../../../Pods/Target Support Files/tencent_cloud_chat_sdk", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9894cf24994df8941841811e22800cca09", "name": "tencent_cloud_chat_sdk", "path": "../.symlinks/plugins/tencent_cloud_chat_sdk/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983663ec2ed50529f6e1b9993078e7c31c", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_uikit_core-1.7.3/ios/Classes/MethodUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98361892a7aff65ce494c00ec36acdf0bb", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_uikit_core-1.7.3/ios/Classes/SwiftTencentCloudUikitCorePlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98303acfa7c3a83e14291d9c674309b4a2", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_uikit_core-1.7.3/ios/Classes/TencentCloudUikitCorePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987f70aeb9d9f05e6e4336c88d38a4fb24", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_uikit_core-1.7.3/ios/Classes/TencentCloudUikitCorePlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fc28a45c2db0660fd0931c76aa924600", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_uikit_core-1.7.3/ios/Classes/Toast.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9801b050d33de76de339fb2d2f2b7ca682", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_uikit_core-1.7.3/ios/Classes/TUICoreDefine.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9822edbf35e940dee761bee09e5b2a3af1", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9893e19eaa2ffa98fb947223babe5770a7", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980f76d9032c49f2912c2d310b216f2c96", "name": "tencent_cloud_uikit_core", "path": "tencent_cloud_uikit_core", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e8e067d0dfb7f1809925849ead7d0040", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985fdaeb1bc0e1ad45274d8d5c36e9b004", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cf3ffcaf18aad34e9b5a0bee41b3a9c0", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9889c880ec332fa5306364450f31a90efc", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98adc98e8cc1682a724c14ac42128f67d6", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98452588c98bb96d3dd8072af485494b99", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d6e529ff7a93dc141adda2bd85a8fbf0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f90ab9d5bfb7a019a8d36de28bde73e9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a522572c36d3466dc0b926139f65d3e3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f81de4b51c1ea498a95b17219dc08515", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987a719eadffc73bff315492004ef39ba0", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_uikit_core-1.7.3/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98627bb916d8134f08c3bc037b4f5bcbce", "path": "../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_uikit_core-1.7.3/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9839be5bdf03cb9d60960dcde1799a86e6", "path": "../../../../../../../.pub-cache/hosted/pub.dev/tencent_cloud_uikit_core-1.7.3/ios/tencent_cloud_uikit_core.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980068348620e125c45d7585466645aa82", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98fc4c19d7f2848d09578cf2db5a17c8c6", "path": "tencent_cloud_uikit_core.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ee60abd6e14b0b63be5324a062180382", "path": "tencent_cloud_uikit_core-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98438c9ed994d7c6652fbe0592241ae717", "path": "tencent_cloud_uikit_core-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9832f91bd6e9c3556674f883a956815985", "path": "tencent_cloud_uikit_core-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983837820e796a20d73959b9dbe9e6b7a0", "path": "tencent_cloud_uikit_core-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9853f2d1042827fa2318fabf54b24e1388", "path": "tencent_cloud_uikit_core.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98225d9181d0974dc071560859559b35f6", "path": "tencent_cloud_uikit_core.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f28b0f3ac5e607083fd10a2c39b5f14b", "name": "Support Files", "path": "../../../../Pods/Target Support Files/tencent_cloud_uikit_core", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f6b54db189c993a5927caad148856a51", "name": "tencent_cloud_uikit_core", "path": "../.symlinks/plugins/tencent_cloud_uikit_core/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e981913817a5524fb1243580015ee214236", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/ios/url_launcher_ios/Sources/url_launcher_ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d22001c8ce6de45658003a5454b6d384", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986eb0e6f38a20afed9a5a77abb58a0230", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a93b50e06e0b7c5e77db4a5812182942", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98011b87d8db4df2567ea1e3cee0859771", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a49852db04a69c88a7318b9e990b8b8f", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a82f889bc044922e35984b7334433d4e", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9869a26ccab4e125acb371f78af1d429e0", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c6e15ee80ad1550c5eb03f5e647e1696", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d36de3208934d47fd8ee5edf08ab8e09", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9852bb942de22e671a422567135e7c80b9", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981abc0486467d2949a828888bfab5f3d2", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d73d461dacfb93d00dd0c1d2ebde1512", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988dfb1b80067978fd37fbc6d43528147e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/ios/url_launcher_ios/Sources/url_launcher_ios/Launcher.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987572b4d8fb8ddd23a57711b751952cbc", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/ios/url_launcher_ios/Sources/url_launcher_ios/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9828490b70ad652a84746b6659bc457969", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/ios/url_launcher_ios/Sources/url_launcher_ios/URLLauncherPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98543e8ecdb79afe3460f449054d4e1614", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/ios/url_launcher_ios/Sources/url_launcher_ios/URLLaunchSession.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b91d9d9a7ce865965ff12d3da96c11a8", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9856ab0591963de0ba5478fb508d057325", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f5296d7c6363d2409c9655d0db0f2320", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a7b066fcb7fa0dc68b29a90a247130fb", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98455ecbf381adac52c65ef5b6e06c826b", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b3074013535ebaf551d2a8a1e431c03d", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986590676e20bef3e72eaf8dc910a7c6d9", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b9c8e269ae6c5ae981d3bdda6c6f231a", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981dfbed3987ed7a710238a89b357600d1", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a3828a09591fcf6a2170fe3e18c01d96", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9831169ede8d778c633fea13cf35faa08e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984865285cda703676a37aa2684c6c1594", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e58bca08241aafb5ef9acbc6a4be5f16", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9824b5b7342f4915832c99ce2d39c28e4d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98993221351bf742177c9bd4a302cd944c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9833d97ec5947fcb54561d3e60fc30145e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9807c51707c2a8be79d55d9359c585d31f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981b439628f06cd1326cb98fdddeb48479", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/ios/url_launcher_ios/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e983e9f387d200f2b9c2c77a3a738fbf8a9", "path": "../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98dc29429fdf57cba5e6707eed292e5f58", "path": "../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/ios/url_launcher_ios.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c077c70742f1812c5188ca53f06e9dfc", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e988267e4ea4a171a1d85f7e1ab82d08bc9", "path": "ResourceBundle-url_launcher_ios_privacy-url_launcher_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98df2a472c84c77fbad4c2e8055921299e", "path": "url_launcher_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984dd6bc1ee82e3bc786d954bfdaefb8b6", "path": "url_launcher_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98e8a92bf9a0ceb8aa7b23162821d9b591", "path": "url_launcher_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9808b598667f9e1ca38115258367b0fc9e", "path": "url_launcher_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984c94b053c8488d2ae4a185cacda1930c", "path": "url_launcher_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981a65912c3d6a68c3c00829e1b147aeb0", "path": "url_launcher_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e983ca321d994d54e60f14db99823557d9c", "path": "url_launcher_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98217247eee724a87013e2e119ff2bac55", "name": "Support Files", "path": "../../../../Pods/Target Support Files/url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980762df512a4d432ac103004c86a41d18", "name": "url_launcher_ios", "path": "../.symlinks/plugins/url_launcher_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98e028c1ec5173719c396b10e66dfb8287", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.5/darwin/video_player_avfoundation/Sources/video_player_avfoundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f183d100f9e8d669e034b8cd991c24b9", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f14a0e2c67a75739c6a017ebfc0239d6", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a7ae9976dfce193274432822d38ae3da", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9860c43212dda01ac7a1d9801185d026a7", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987e0beea0ecfd610a1467ffb3b1f69c5b", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c84ec6426619d02ec837c6720d02e141", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989e2d4f97ec5c3f983ed88f95b2c675f3", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9820cf491969efc28f13599a87eb8c292c", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9837d8097b4814edc04a98bcd7c463f699", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985fc8a8e8b64c623ca96d05cdf5dd914c", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e9adefb0537eb7337e4450fa09d6f442", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c5598d4b71389aefa00ceae6a2b4f0be", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e4d851664e1d610c57d0306a0c85a00c", "name": "..", "path": ".pub-cache", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fac5dde7cfcfcd3aeafa778b87a7169a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.5/darwin/video_player_avfoundation/Sources/video_player_avfoundation/AVAssetTrackUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bfd77a2686b7d0d4c0a6d494bf073ce8", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.5/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPAVFactory.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c99f681eecd95ad3966b7082e11273ef", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.5/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPFrameUpdater.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9823dc2dfe21e9fc2f9d3527e5b5dc0ba5", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.5/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPVideoPlayer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987244d9bd626a8e25ed8855daaf2f3b49", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.5/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPVideoPlayerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9838f9bdb1b5cf1c928e98e03333554928", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.5/darwin/video_player_avfoundation/Sources/video_player_avfoundation/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9870606e42d2d044301005e8ac3c6d2111", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.5/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/AVAssetTrackUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9895106ecd1cc14aa94d1f238d7ab44090", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.5/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPAVFactory.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f23f67e15801b929bea2e903f72c3f06", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.5/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPDisplayLink.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e1e079e4f73141e9834e77fadaeff71d", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.5/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPFrameUpdater.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dc53a346925f94390863eaee3a8f151f", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.5/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPVideoPlayer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ae3014df383e2d0559efdcbeb1364fac", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.5/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPVideoPlayer_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988962980bc0fad829cdeba197f1837cec", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.5/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPVideoPlayerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985ac24f781dd5b50abe0fb1ef526d60db", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.5/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPVideoPlayerPlugin_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98196f10f4efe97d9b481c574a01388288", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.5/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/messages.g.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c0b88a3f62b4e6daee945cd2096b432b", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98853fadadff4341bd9a270892d1ea5b5f", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984f8cf507905aa4697608eebc0f240f95", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b161b70eeb362fb6f943884cc02d7bfd", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.5/darwin/video_player_avfoundation/Sources/video_player_avfoundation_ios/FVPDisplayLink.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9892cb36f77c92c2657da9cff7a77c6397", "name": "video_player_avfoundation_ios", "path": "video_player_avfoundation_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c6281616e2af6eedad511d272fbc12c3", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fdfef5218a299cc0850faac4211f8853", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980f0163707f04bf06ebdabd02e827abb8", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988a143029ed633acfe8cdffc8d444b8b5", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986ea225a32f48a7be79c28785e6b676c5", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d62929c56f34c52bc011eb578aa7ddcd", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9824c8483c765f5b5845f2ba6230b915bf", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986f6a590fa524a7bdebffa673bc0954a5", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988602f9f61e6324e4bc533d6da9aa4977", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e6ee5b80f87d5314078dbd407c26d62e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d46530389c47e69d18c34047bc78a421", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985322ed8cf92a1421acc334844f4f47d8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980c1ff6edfc1d6251340bb8ab8b42d932", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9804a59d600960d0a1c7edc76a8857a875", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f85058edc4228ca8ab457e10c705289c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9898b5ed823c3a47ee9b42077e163c2fd1", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.5/darwin/video_player_avfoundation", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e982cc947534cee03868c9041dc73182aad", "path": "../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.5/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e989fb74d2afe30f638ff75e27894660b9d", "path": "../../../../../../../.pub-cache/hosted/pub.dev/video_player_avfoundation-2.6.5/darwin/video_player_avfoundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98de4bf2ac8127557e2c3d0216e811253f", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98e7e730446c55051707f5d05a047209da", "path": "ResourceBundle-video_player_avfoundation_privacy-video_player_avfoundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e984f8597ae128ffe513f6ce4f8092f259f", "path": "video_player_avfoundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9862d5be4c8188e6469157958f23ae4643", "path": "video_player_avfoundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e985e5b2b40e745596d0bdc15efa85ceedd", "path": "video_player_avfoundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98947c1f2bf91a6afeec1e2b8b3badea66", "path": "video_player_avfoundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a26774f4b915025226d61b3fce4b4387", "path": "video_player_avfoundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ec6396a14f0d9ee508c9eb27443eec67", "path": "video_player_avfoundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9820cb7685d8ef5d77b3e47e080dd157df", "path": "video_player_avfoundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98771635c3ab6f429bb0a8218f58df1d27", "name": "Support Files", "path": "../../../../Pods/Target Support Files/video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ddb63de057465524578760fc047413ba", "name": "video_player_avfoundation", "path": "../.symlinks/plugins/video_player_avfoundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d092f3bf16f34d02e564d4ca062b0ce8", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/volume_controller-2.0.8/ios/Classes/SwiftVolumeControllerPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986b86e41ef27b9e793970ed60cfb67a95", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/volume_controller-2.0.8/ios/Classes/VolumeControllerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e13e6a6c3a55dd724381b05d9ec617e7", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/volume_controller-2.0.8/ios/Classes/VolumeControllerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e6d16a367685cdd6b204ffd415ec9fb8", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/volume_controller-2.0.8/ios/Classes/VolumeObserver.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989ed2e3660e69a9eebbff8d3b404ca24f", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bf95d8e0b18f6fb6859b83d2ba21f99a", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ff8e397b6d68df000be02c1c1c5d47e3", "name": "volume_controller", "path": "volume_controller", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988cddccf101dfd3f65ea8be88aac0ed08", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989a4ef342ae887ec3bcd61fdf8b03d2b3", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d8fbeb14e177afe7e28d9a211c2edcb4", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98de8ae444f7f77bfe76e139c974ad0493", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b45cfeb32d61619a32e3eb17755aa6fb", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985d31484adb469066085f925661f18ab3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9859d32d5eeacf1c7413bee51ed5b8feb0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9856099e5a40cc99af13977e86a4c6edf6", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98df1e183dc25d3f91af0c2d051b10cd65", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980e27a77748dcc71175835958b3c96f7f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b3d337071705dc21b6c9eb31bb92f055", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/volume_controller-2.0.8/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e981bb0eae340df79300dd7519327cbfc98", "path": "../../../../../../../.pub-cache/hosted/pub.dev/volume_controller-2.0.8/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98be7950d98b5b41b78d374d6918760270", "path": "../../../../../../../.pub-cache/hosted/pub.dev/volume_controller-2.0.8/ios/volume_controller.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981fd0c071936299e1e03c301d29679ee1", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e988226e02e31363fcc71c570fac1776000", "path": "volume_controller.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980f1867bd97c4fe6aaf409bc6f845b458", "path": "volume_controller-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e986587c4f090d97c6f775949f15b3408f6", "path": "volume_controller-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98409863a47c00902604ebf66fe4dacfcf", "path": "volume_controller-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986e665029f3172af43b389cd8fd964e8a", "path": "volume_controller-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98fb72211fd0c83019e6ed1a8bc5b0adce", "path": "volume_controller.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e987844d8eaeb4ccb1b550f025064d732b2", "path": "volume_controller.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981fc5323116baf4e775c61fbf9bd7484a", "name": "Support Files", "path": "../../../../Pods/Target Support Files/volume_controller", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9838d0d9af2dd88ce034c8f9c7562a1110", "name": "volume_controller", "path": "../.symlinks/plugins/volume_controller/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9871e0135aa361430f31a383c903d86d48", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.10/ios/Classes/messages.g.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b040c3e84df5165f1e6d90dedf0e1261", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.10/ios/Classes/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c83153379431a8a62fa6a146aa94975b", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.10/ios/Classes/UIApplication+idleTimerLock.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e05f9c7732660ea06f793230f2227c2a", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.10/ios/Classes/UIApplication+idleTimerLock.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c5c592df3a448fd38df95d548ae809c2", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.10/ios/Classes/WakelockPlusPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d2273924690ede098e4eaccf41eeda4d", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.10/ios/Classes/WakelockPlusPlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cdb5cecb9fcbb7648f451f968c4668cb", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98f288e29916ad0bc36d9c13144cd6e7dd", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.10/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989824591a221bc887def603e5e11102f2", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980908ca452223b5403163e8401e4e4a85", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c4d62502deeb82666182f82c4b4c8e15", "name": "wakelock_plus", "path": "wakelock_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9883895802124e04d782389cf4b3fbbcb1", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986114330d29f64b4376f7af94ede61d92", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983a58b71239a10fd6ce63408fddc30414", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98440a9a56530449e998575b2cadaa04de", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98237eb83e03387248e26bc353899f56ff", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fae7fdbd7f9082f6a10dcfee65578531", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e4c0d0dc6b55ccfc12d5f13585873cfd", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b2e457c04493cecad2f71865e953015d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989d73141fdb7186e1f6686ca8b7c1ca17", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989b9efd57b2c293a063f8ff45da33a27d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a8acd9a7a736201a4f6a904a79092d84", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.10/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98a0386a94135ff9acf5f6c903491cae7a", "path": "../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.10/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9854768b4e1479078deb27c5d3e68e6250", "path": "../../../../../../../.pub-cache/hosted/pub.dev/wakelock_plus-1.2.10/ios/wakelock_plus.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98365576040a1349b1d557ed3573cc9aac", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e984ea6eb62c8ba70a4f4afed5ff935377c", "path": "ResourceBundle-wakelock_plus_privacy-wakelock_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98955e9709eb63b9316e75b0ba5acf449b", "path": "wakelock_plus.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987fda45004a2875cf48adefe25ce0b177", "path": "wakelock_plus-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b60f31a7843c14c8f026b50c102ef27d", "path": "wakelock_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98da1b64e427126ec7db654a773dfd1bfe", "path": "wakelock_plus-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9843603bdb42f52019a3bafccfd795286c", "path": "wakelock_plus-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98abf39d384ba2d6c63c29edcee48706a0", "path": "wakelock_plus.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d8cc3060ab6de5ee315f43cb3a4b45a1", "path": "wakelock_plus.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e155aa62cea853e04d709eabc8536d3d", "name": "Support Files", "path": "../../../../Pods/Target Support Files/wakelock_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982d5e4958196a1a300aa61a07c6d37f74", "name": "wakelock_plus", "path": "../.symlinks/plugins/wakelock_plus/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98c999bb6084b1fd8fe0378d9860c8d82c", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988a70d8e0f83b04fd351bedc8a91883a1", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980a5b7b676447c7002b7241ea07d04167", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980b2b2904dd93dcaa8d588cb5db0dcd8d", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987edf3974a26d0c5abb528541faeebe06", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986ca7e438a8df1342fb52ec9149b34022", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9811f0dfb1da7c8c412aacbefbd778e886", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985336fc45839d9ff16d3a400174bb56b2", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988a627a424744f24ecb780c7ef6f52230", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e7878bb7ecdec1ad062a7ba6e1043b2b", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a02d6b337844c67bde8bd27514f90541", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e1e8d5f950f4778b28548b8d1dc8b940", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98755c6d06f1772707834b14300e47bb3e", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f01b0107474bbdb9d9e3849730c0c346", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FLTWebViewFlutterPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98650585101292d84a5af6e7c05a3b1eeb", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFDataConverters.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a1d8c880f59850486befbc6d1ea596c9", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFGeneratedWebKitApis.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9894153c47e44b06e7d1321c18c58ebbe5", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFHTTPCookieStoreHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f2a69492be173aaccc5e40071de6a0a0", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFInstanceManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9893c63ee9aab708dd69a3ab55582a3c36", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFNavigationDelegateHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f287cf392156bfef0ab439be8314ebba", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFObjectHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a4adf1b76e362228f4a05ad97d3a4426", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFPreferencesHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985689c7d7b7af7ef8463ef619855aa13a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFScriptMessageHandlerHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e1e476297c1e62693fab8559f3fec2ba", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFScrollViewDelegateHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cefedbbfce9c7bc9ee4314a30c53440b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFScrollViewHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ef14fa21b4a4ddb3c300dd682e669f7b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFUIDelegateHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a49fbfbd6445e06e997fe767b4518f09", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFUIViewHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988ddcba485d08db4ba8e64f1d87cffbda", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFURLAuthenticationChallengeHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9898dda593f9c9d7d0b0ba05961ce8cc21", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFURLCredentialHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f2b04c59b3ea272b9bfe155f4b785d37", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFURLHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988806c9d5b66984174527567ec8e9ce6b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFURLProtectionSpaceHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dc00274b56435c2e49acc11e64911a75", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFUserContentControllerHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980250c1b222b1157aa1c7167b4fdc4263", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFWebsiteDataStoreHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cb7395fe46e4575b771c4a80534445ae", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFWebViewConfigurationHostApi.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bab3a7d9b12924812f10d74eeac10e2f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFWebViewFlutterWKWebViewExternalAPI.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980c49dd5b65b1ef1b3052ba143cebc919", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FWFWebViewHostApi.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9857de5e4ae232a87f9a9efaaf85c7e898", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985261dfb09c3c1e9c9d64c703300829f2", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FLTWebViewFlutterPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bca4b8bf31df731a60f8fd22787f620a", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFDataConverters.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985bd7e4c8d46b032268f0d9f0645d2022", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFGeneratedWebKitApis.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e1da6793a42159c415981423d6a6b858", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFHTTPCookieStoreHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9860b9238e1296c81674a726228bd55058", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFInstanceManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ac0b14c978ea8aa287c93fdd56ced0bb", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFInstanceManager_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9846d08031b2a6c3859adbdac1d6b4722b", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFNavigationDelegateHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e0450d4e3b67081e949853d515653b37", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFObjectHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988bee58d39bcfa5ff2a78ee7df85edead", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFPreferencesHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9866c98a1b4cf58f8e26ab75b90cf7daca", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFScriptMessageHandlerHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98696364c0782ff1c3e480d7a4947a5ce7", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFScrollViewDelegateHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9803e7c989172096a0e24cea6a899f8f29", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFScrollViewHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e377e2b0f7ef2da3a6a4134a550e9827", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFUIDelegateHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985805df3e15595d16b8435e88ba1a1aba", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFUIViewHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981933cde9414b7608f3aa75716a011f01", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFURLAuthenticationChallengeHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989f649da5d6316dd402d60a4695469f12", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFURLCredentialHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989822193cdd2093fb3a211621ac854ff9", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFURLHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98edc8769740bfd449f261d5f0396929c4", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFURLProtectionSpaceHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d3e33dd60b7918c785c1781488b32210", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFUserContentControllerHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e343d2319b609eb889a9368a571d842b", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFWebsiteDataStoreHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9854bef40ce6966fca939c41c570f28867", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFWebViewConfigurationHostApi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ee9ef21fe46a8761d4d0888431d2226b", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFWebViewFlutterWKWebViewExternalAPI.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a79942a7eaa7a39f8ca20bdea98cfec8", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/webview_flutter_wkwebview/FWFWebViewHostApi.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fb7f883c2b424201d2397ea88e1586b5", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ff39c85d338ed212dc6dc5d88df53b5a", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aae7c87c31761c40fed68c2ea6111c54", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9857c1d98933ffa265b14dd4fd87bcb2be", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98db29f3baf4eed94253a9796b1dfbb5da", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9851aaa580e0f7e0b7a52bd06d38d3d6cc", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988ac3cd534f08fe11547aacc261f923da", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bab07c5d7522621b66a4c09e94ee8d04", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c3dcba6adf1c2aca56403b63a961b311", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982e0586fdc48e083bf77b0d1365653daf", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984868b131020b2f8ca82bb5f98db8d3f0", "name": "game-store", "path": "game-store", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9840021f4be96778d8fc9d4e00d20d495a", "name": "loom_dynamics", "path": "loom_dynamics", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ce3e20111139f1e04b26fae1240c9312", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98695296d8469ed542378824593ad0679d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983af933a9ff5eefd1ffe1bbc34be91806", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98775209b705c550c35c563feffdc99663", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f9cb546e6b27774c66f087cc558e6aba", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ad62cf08698bc01a71fd9b6ef4cc4a82", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986daa0dff592f92c63e6abedca3b567ef", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9857d4331fb9f46b30745b427d49c4321d", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98132b9008cde3f47629ef595228144c63", "path": "../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/FlutterWebView.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98f624fa695f4807efe3f5cf7cb3d63c6f", "path": "../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9897f99a4989f2df746d1c1d72f7681a00", "path": "../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.16.3/darwin/webview_flutter_wkwebview.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98be8b9cf59553ed86aea3815d1243c2ce", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98dcf01540a48ace7a54fc2c1751517e80", "path": "ResourceBundle-webview_flutter_wkwebview_privacy-webview_flutter_wkwebview-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98597e936d536201f0702a0e02f32ed2c4", "path": "webview_flutter_wkwebview.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a471fd95c5cf01c7032d99c44325c0ea", "path": "webview_flutter_wkwebview-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989607af5127413075ba2aea73b6572183", "path": "webview_flutter_wkwebview-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983b1740ed9e8db3dd7bdbca9eb4c97be7", "path": "webview_flutter_wkwebview-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a17dc2eb3498f2047cf5a68a69901297", "path": "webview_flutter_wkwebview.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b8bbd36bcc2aac4df71ed3bd233350ac", "path": "webview_flutter_wkwebview.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986763a90bc11e3b192e15269a192c5070", "name": "Support Files", "path": "../../../../Pods/Target Support Files/webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981926ebf659ef61ed7cdec8da56561b9f", "name": "webview_flutter_wkwebview", "path": "../.symlinks/plugins/webview_flutter_wkwebview/darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985acdf7341492dec92b102b70d3012380", "name": "Development Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98b32d5f9da18547a5dda681aff174df7a", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/AVFoundation.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e9841e20065171a32ae1bef9c372ce4df16", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/AVKit.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98144836b1cb38b7590a760794efd54afa", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/CoreGraphics.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e987e9cbf3be042958b470cf98aebd11b1b", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e9895bd7a2c7c3b2177eb482f1ebfef1b46", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/ImageIO.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98fedf9725cd154a83c38e230bc7083152", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Photos.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98d971dd0125307f6dda33d52f810c5c9f", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/PhotosUI.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98dfa0019e3af6f92000000c37fd8de72a", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/QuartzCore.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98bdbb9256ad1e1b8554abcf310b1bb320", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/UIKit.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9824214e4d9ae43c2ad6b0c68b0b10dad0", "name": "iOS", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986f57df5597ed36b645cb934c885be56d", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c081f59eecbf816e0abb56a315439e0f", "path": "Sources/DKImagePickerController/View/Cell/DKAssetGroupCellItemProtocol.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9875472e7230d5ab842404b67d474cff74", "path": "Sources/DKImagePickerController/View/Cell/DKAssetGroupDetailBaseCell.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984a60a87facf84ff7ced10e262530c01f", "path": "Sources/DKImagePickerController/View/Cell/DKAssetGroupDetailCameraCell.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980a78fd66f95296c5ed5c745e641670f8", "path": "Sources/DKImagePickerController/View/Cell/DKAssetGroupDetailImageCell.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98039575db2db4f9076a0adcdfea37c442", "path": "Sources/DKImagePickerController/View/DKAssetGroupDetailVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98669c825749fc8c94afd47cb01cb31132", "path": "Sources/DKImagePickerController/View/Cell/DKAssetGroupDetailVideoCell.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98611c748e68c167c55c0bfbe94ec681af", "path": "Sources/DKImagePickerController/View/DKAssetGroupGridLayout.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fecab2c8bac83e57d91d7a8b0b469b99", "path": "Sources/DKImagePickerController/View/DKAssetGroupListVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98265ec52e5ef8744b4dab502d40a43107", "path": "Sources/DKImagePickerController/DKImageAssetExporter.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9887a2ca272c757220b1ffd5654f6682bd", "path": "Sources/DKImagePickerController/DKImageExtensionController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e71f9862288cac0c8e391a7ffa099fb3", "path": "Sources/DKImagePickerController/DKImagePickerController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e39d907258878ad12103268c4be5529e", "path": "Sources/DKImagePickerController/DKImagePickerControllerBaseUIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ad0e36a8d6d3ff5e44ff6c791d5ea15c", "path": "Sources/DKImagePickerController/View/DKPermissionView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9827c6ef8d45238e526def4fb229977b90", "path": "Sources/DKImagePickerController/DKPopoverViewController.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f0deb319cf227d84a21c7db5b76dff8e", "name": "Core", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b13736048a8c4e2815538657881876f3", "path": "Sources/DKImageDataManager/Model/DKAsset.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9856256454a26373d4d87a48250aa9c9a3", "path": "Sources/DKImageDataManager/Model/DKAsset+Export.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9856d70a7537d14fe7c9e4ca55a240e0f1", "path": "Sources/DKImageDataManager/Model/DKAsset+Fetch.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986ab6cedf3d9e0ead14f645f3df7f37e4", "path": "Sources/DKImageDataManager/Model/DKAssetGroup.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c10c40f2a2bfb90b139e8f5f320a34ef", "path": "Sources/DKImageDataManager/DKImageBaseManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987f57ec7afb960b218ec51dcd09078b8c", "path": "Sources/DKImageDataManager/DKImageDataManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cff0fa4e0d6568e09182605f18850080", "path": "Sources/DKImageDataManager/DKImageGroupDataManager.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b8f620b25cb323b1fd4ef3516e1c0ac9", "name": "ImageDataManager", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984d18ca96f8da41885ff44e8d02e25b8b", "path": "Sources/Extensions/DKImageExtensionGallery.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98830dfae16cba600bb35fd7efbb57c070", "name": "PhotoGallery", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b210d0116e823e3798ac884a2c41b50b", "path": "Sources/DKImagePickerController/Resource/DKImagePickerControllerResource.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e980686d118707d8299df6dffae4caacf4e", "path": "Sources/DKImagePickerController/Resource/Resources/ar.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e982712d4d18a0abba44d6ea55797cb438c", "path": "Sources/DKImagePickerController/Resource/Resources/Base.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9812e7c5582b032c29e37836e747846dd3", "path": "Sources/DKImagePickerController/Resource/Resources/da.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98434f98cc6df61c36412b7959bfae8979", "path": "Sources/DKImagePickerController/Resource/Resources/de.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e980b9956dc79d04129d7526696ad702ccd", "path": "Sources/DKImagePickerController/Resource/Resources/en.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9829dc91bf9d79a9a5ca676060a4c80943", "path": "Sources/DKImagePickerController/Resource/Resources/es.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98defb89c798f3f4fcd48c228c7d30700f", "path": "Sources/DKImagePickerController/Resource/Resources/fr.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9853fd2f3ddec47da4ee3861171e6d158b", "path": "Sources/DKImagePickerController/Resource/Resources/hu.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder.assetcatalog", "guid": "bfdfe7dc352907fc980b868725387e9894e1ee88f5d1a3ffb5fc88dd676c3583", "path": "Sources/DKImagePickerController/Resource/Resources/Images.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98c68d4ae273bbbf9df39f047b07f8501e", "path": "Sources/DKImagePickerController/Resource/Resources/it.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e988abca06125c6256ace4b1a2d6d5cc28f", "path": "Sources/DKImagePickerController/Resource/Resources/ja.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9817245996844d6ba6e8415689264c88ce", "path": "Sources/DKImagePickerController/Resource/Resources/ko.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9833b59933fcf795e203faac9787323996", "path": "Sources/DKImagePickerController/Resource/Resources/nb-NO.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e987af1be29dda07e70a9b3f1b8b756d54c", "path": "Sources/DKImagePickerController/Resource/Resources/nl.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98284410daa27b4374b3afa4d747f38651", "path": "Sources/DKImagePickerController/Resource/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98bec840292f492792e3df7ccfef62820e", "path": "Sources/DKImagePickerController/Resource/Resources/pt_BR.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98625dd579dce9a80a7e934d4d4f655a13", "path": "Sources/DKImagePickerController/Resource/Resources/ru.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9879f29c8e1e92119fb6c8566649dad700", "path": "Sources/DKImagePickerController/Resource/Resources/tr.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e987eaf7ad54caf2489a113d0b28a2af2b7", "path": "Sources/DKImagePickerController/Resource/Resources/ur.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e988b290473a08df7525cec014a2d6bfde5", "path": "Sources/DKImagePickerController/Resource/Resources/vi.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98e77b5175d30e9e3b91f28e1be62d9620", "path": "Sources/DKImagePickerController/Resource/Resources/zh-Hans.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9890812322f167ede3018fd82109bc49f0", "path": "Sources/DKImagePickerController/Resource/Resources/zh-Hant.lproj", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d8150b964b381ac2798134502efe9c41", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983b25c4017d00d8d6ef2d799eba0d4489", "name": "Resource", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9815ceeeed02aff5810f2ca743b14a3ab8", "path": "DKImagePickerController.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98898c5ddf66b9f9c37dd95e9be517bf4f", "path": "DKImagePickerController-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98109d7db6debd049071d8b301c2867dea", "path": "DKImagePickerController-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dc36343127b148247e6925bb81ee16a5", "path": "DKImagePickerController-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9828d8885f7bd5af4f834672087018817d", "path": "DKImagePickerController-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a28a39e65619cc3d65963fb70172bebe", "path": "DKImagePickerController.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e989e2065456da4d2d1e256d0a1a676bf70", "path": "DKImagePickerController.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98525689218cae8e92688e9282a5a5a601", "path": "ResourceBundle-DKImagePickerController-DKImagePickerController-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fad4e0497e1d50bfd4fb3b5d0c762a0e", "name": "Support Files", "path": "../Target Support Files/DKImagePickerController", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a655ff80996427d91c3b8bf2b52ca355", "name": "DKImagePickerController", "path": "DKImagePickerController", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c3a0f8e362e7df87a615afefa17bfe3b", "path": "DKPhotoGallery/DKPhotoGallery.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981c00effa8742a8906534c83d8e3b769d", "path": "DKPhotoGallery/DKPhotoGalleryContentVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980e88fe61036c45dcb445c291e640fec1", "path": "DKPhotoGallery/Transition/DKPhotoGalleryInteractiveTransition.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fb1a8d96caead5a6391486ba59e717b8", "path": "DKPhotoGallery/DKPhotoGalleryScrollView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98416c6078290ce2de93e5de0aad66a94c", "path": "DKPhotoGallery/Transition/DKPhotoGalleryTransitionController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984d12067c046d61354a7914b322457b61", "path": "DKPhotoGallery/Transition/DKPhotoGalleryTransitionDismiss.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9842db70d6fae285fc3bccc6608e07c0c2", "path": "DKPhotoGallery/Transition/DKPhotoGalleryTransitionPresent.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9808dacdcad49ad172f0858ad969c8d8f0", "path": "DKPhotoGallery/DKPhotoIncrementalIndicator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981f3fb757dc23d9132e7d79c3ce036069", "path": "DKPhotoGallery/DKPhotoPreviewFactory.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984b2868324a1d6eba9fcc134d9d5b81f2", "name": "Core", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a53e4689fe9dc1e06227c89531763365", "path": "DKPhotoGallery/DKPhotoGalleryItem.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fde58307fd5cc7bfa82c81dff1a089bc", "name": "Model", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980d0747fe23a2c1e098e58369d38b7ffd", "path": "DKPhotoGallery/Preview/PDFPreview/DKPDFView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a5030770a7aac1faa187dea29d79c8e9", "path": "DKPhotoGallery/Preview/ImagePreview/DKPhotoBaseImagePreviewVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981a1447db3abd2d533bc817c3e35a2d62", "path": "DKPhotoGallery/Preview/DKPhotoBasePreviewVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986f7902bec966b826ed6d8e2790bd1b4e", "path": "DKPhotoGallery/Preview/DKPhotoContentAnimationView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9804b73121fa3310ca43e7246918a20fd0", "path": "DKPhotoGallery/Preview/ImagePreview/DKPhotoImageDownloader.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fb30b063209e24d73fbad6734dde6ddb", "path": "DKPhotoGallery/Preview/ImagePreview/DKPhotoImagePreviewVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98df6ec6541ca56619166052eb4997ff19", "path": "DKPhotoGallery/Preview/ImagePreview/DKPhotoImageUtility.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9889fbd2aa0f10750b2126a4e4723cb0fe", "path": "DKPhotoGallery/Preview/ImagePreview/DKPhotoImageView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ec29a4232687f0d31efef515709604a0", "path": "DKPhotoGallery/Preview/PDFPreview/DKPhotoPDFPreviewVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989834f4544b3146192f2de6c6e1920e31", "path": "DKPhotoGallery/Preview/PlayerPreview/DKPhotoPlayerPreviewVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984bbd92f448797fcbaebad68bcf2079a9", "path": "DKPhotoGallery/Preview/DKPhotoProgressIndicator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a7d8b7b7415619511c4e2fb44a9d7a2c", "path": "DKPhotoGallery/Preview/DKPhotoProgressIndicatorProtocol.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a12abbf0a88f77db3bd24a367242ef26", "path": "DKPhotoGallery/Preview/QRCode/DKPhotoQRCodeResultVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98de30a7bda31af7ec4ecc6a946981c1c5", "path": "DKPhotoGallery/Preview/QRCode/DKPhotoWebVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982f127f4273e5dc6354229323f0810af8", "path": "DKPhotoGallery/Preview/PlayerPreview/DKPlayerView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9803e915ade9b9a9b843f057606b035319", "name": "Preview", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987173fb2adca07a52c734af332974e094", "path": "DKPhotoGallery/Resource/DKPhotoGalleryResource.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e986f5d3f83d0117b856259c67fd88a341f", "path": "DKPhotoGallery/Resource/Resources/Base.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98ca4ed12715d1b3d9fb875b09222693be", "path": "DKPhotoGallery/Resource/Resources/en.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder.assetcatalog", "guid": "bfdfe7dc352907fc980b868725387e982a1d6f10823d5aa0010b7d84b099a5f7", "path": "DKPhotoGallery/Resource/Resources/Images.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e988ca30e0f0d0c5615c740e0f1d694a203", "path": "DKPhotoGallery/Resource/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98520b24c80a82a707e082779a352afe36", "path": "DKPhotoGallery/Resource/Resources/zh-Hans.lproj", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9880953a0a994f804bf50ac979e3a4352d", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983c42b717a0f935b0516e1de5ce22b49f", "name": "Resource", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9879d5e429f0f124502e95c7aa6fb0729c", "path": "DKPhotoGallery.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985cafea1e201ff53c058f0ad74a95afdd", "path": "DKPhotoGallery-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b95107d55d5e9e1e581897c5b8c1eaf6", "path": "DKPhotoGallery-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982b779666ae8cf4049fa009ca07a55cff", "path": "DKPhotoGallery-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f76ae245e8109d578585488529d425be", "path": "DKPhotoGallery-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e984c506e4c7c5bfdb77021de62ba724fee", "path": "DKPhotoGallery.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9845469c5f81bc9942499591b0946fc69b", "path": "DKPhotoGallery.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98fd0d2cd9196afa01eaf1f8e22a02c147", "path": "ResourceBundle-DKPhotoGallery-DKPhotoGallery-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c50fb293d95f412ec4bcb8e9aa5334b6", "name": "Support Files", "path": "../Target Support Files/DKPhotoGallery", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e769058bc42296ac37a394c86e3c50dd", "name": "DKPhotoGallery", "path": "DKPhotoGallery", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983782c0980b6993c46f768d11d504e2f0", "path": "Sources/Hydra/Commons.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9821224594e6d9b1021fcb8b6728f116ff", "path": "Sources/Hydra/Context.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98acf566e77c562a1126d23a393b685691", "path": "Sources/Hydra/DispatchTimerWrapper.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c40fbde14cf1783d3f9dece5ef967eff", "path": "Sources/Hydra/Promise.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982b7038bbec31b926690c344cde4a0abb", "path": "Sources/Hydra/Promise+All.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d9a9f902e2eb8aebbdbf0151bcf871b5", "path": "Sources/Hydra/Promise+Always.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985b7fbbeccc6a74734a7b296302965e2b", "path": "Sources/Hydra/Promise+Any.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c62d4fd97cce5fe123a3d70b5ffd7f72", "path": "Sources/Hydra/Promise+Async.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983b34b7a5841e77ceb5e7d1ed18c34e9b", "path": "Sources/Hydra/Promise+Await.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981dde3fee0abd865d6323696800b35f1b", "path": "Sources/Hydra/Promise+Cancel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984ce219745e7152bcf03ac0bf59b8dce6", "path": "Sources/Hydra/Promise+Catch.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9864903d90612164b9138a9271f504857b", "path": "Sources/Hydra/Promise+Defer.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b22e871921940fd51051ec8dd53e2b07", "path": "Sources/Hydra/Promise+Map.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98dc23eb6f7c357ec340555430b4e128fb", "path": "Sources/Hydra/Promise+Observer.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985aa30e8be0560787b204752c16b76fe5", "path": "Sources/Hydra/Promise+Pass.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9841109d36cc3b3745b04ab55bcb52f7d3", "path": "Sources/Hydra/Promise+Recover.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985d1a55e659c152d88df4e88309d19c13", "path": "Sources/Hydra/Promise+Reduce.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98dbd9cb6f3e1473837cdd4b5b98d1bccf", "path": "Sources/Hydra/Promise+Retry.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e0ddef3517ad8035118fa25802d9e6be", "path": "Sources/Hydra/Promise+RetryWhen.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9899d3b32f897161ae618e4c17bab883de", "path": "Sources/Hydra/Promise+State.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fc5544465ef05cadc29081a403913d0c", "path": "Sources/Hydra/Promise+Then.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e2fbff5d211ba5b2bdeace6ba16ec39c", "path": "Sources/Hydra/Promise+Timeout.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983f38702fa4ce4960399ca0d9a05f7ff5", "path": "Sources/Hydra/Promise+Validate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980ecc485da2d18b0a7d5ddf9853036286", "path": "Sources/Hydra/Promise+Zip.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9813c933b26ec7e6bd4d3b5b70055b7871", "path": "HydraAsync.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98051e06e8e8dacf0ddc128800c6e14bb9", "path": "HydraAsync-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9856a1720b67979c87cb3b4b6e04963f76", "path": "HydraAsync-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9846c7226f90c83c855b2654ff400a04b6", "path": "HydraAsync-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987b6dfdecd6941887239a2b9b49c711b8", "path": "HydraAsync-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98e5ae0f909404a135607be394e691b816", "path": "HydraAsync.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98c9f0b1b24a42ae697c483e7334edd6ed", "path": "HydraAsync.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fc966a4d264b22ba5904c51d6fb1742e", "name": "Support Files", "path": "../Target Support Files/HydraAsync", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98769056f12f5f731ad49f6f686513c62c", "name": "HydraAsync", "path": "HydraAsync", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981b0ba4fe7658216cd603efc471a3d12b", "path": "Classes/OpeninstallData.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98314156aa8c4424c77aee1d93a0383424", "path": "Classes/OpenInstallSDK.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "archive.ar", "guid": "bfdfe7dc352907fc980b868725387e987e32907c2be92f9250598fe2adf075bd", "path": "frameworks/libOpenInstallSDK.a", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984a86a670b4c040bd100468db2bcbf0cd", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98afe60850ca7c308a62abd068e8e3f0aa", "path": "Classes/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fe5dceb2ca8d18dbbce5e7f4b7505ece", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9836f0fc43ea40568c6b0a2b88cafd7ca8", "path": "libOpenInstallSDK.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e982772ad49251ea401ff3e855ccdcdd6ec", "path": "libOpenInstallSDK.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98dd46b87091dd6bac9de9e74dbf956a1a", "path": "ResourceBundle-OPPrivacy-libOpenInstallSDK-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9865c948ed0aa5eb4707e6f9390a1d7d82", "name": "Support Files", "path": "../Target Support Files/libOpenInstallSDK", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981b222ced466a7987713ca8da3d899200", "name": "libOpenInstallSDK", "path": "libOpenInstallSDK", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e984622add08b58169341b24f64e71f35fb", "path": "src/demux/anim_decode.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98c2cff4ec9e7a50cab386ab00cbbbfe92", "path": "src/demux/demux.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c4496a5a4da60923a4b6e07670b28a5d", "path": "src/webp/demux.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d2457d382f2c28f2264b3568e0330373", "name": "demux", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98ca39a2e55d476bb113de2aae624cde93", "path": "src/mux/anim_encode.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d0b1baede0adc878959ec225ca013bd4", "path": "src/mux/animi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cc40e6a8be7f079b2802dc0cf381303a", "path": "src/webp/mux.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98b001d3034d6639b9883a2d24fe6f069f", "path": "src/mux/muxedit.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98430460208b2d5c4c776c54dbd002bf4d", "path": "src/mux/muxi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98dd1b64a9391e1265eef8c9b7965891a2", "path": "src/mux/muxinternal.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e983901c86a05ad49c9559a5d1a033ccc1b", "path": "src/mux/muxread.c", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9821e7db215eed9ac1b88f28123aa95b22", "name": "mux", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9888aacb3490c1958369b545cff51efaa5", "path": "sharpyuv/sharpyuv.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981b47eca4c7bee247daf0234814a3e2e6", "path": "sharpyuv/sharpyuv.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98e413bb9d2ebaf93199d4526094f42c0e", "path": "sharpyuv/sharpyuv_cpu.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988442c2635ea3feaeb61683c334d74b6b", "path": "sharpyuv/sharpyuv_cpu.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e989d3b5a396f6b34e0c0bef273a11dd1f5", "path": "sharpyuv/sharpyuv_csp.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ebaf7c0e977779ce196fa65c2547111a", "path": "sharpyuv/sharpyuv_csp.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9864c15aec6e7d2849f5bafee2e292f57b", "path": "sharpyuv/sharpyuv_dsp.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e04dbb07fd534dc595d9faca7511ab66", "path": "sharpyuv/sharpyuv_dsp.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9846203ba15c5b7910325755f3a13481aa", "path": "sharpyuv/sharpyuv_gamma.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98550d1f43ee632f848bab40b159ea625e", "path": "sharpyuv/sharpyuv_gamma.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98639cc804a3ddb6f4da980af486936ddd", "path": "sharpyuv/sharpyuv_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98a3e9774058a5765285bd60fe5fea0410", "path": "sharpyuv/sharpyuv_sse2.c", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9858d8b5736251b9ddd63b6e5791b160b4", "name": "<PERSON><PERSON><PERSON>", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9894938aff468bfa395f882d5ed0766839", "path": "libwebp.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9838557bdf9071e7655e57b9cd7d11a870", "path": "libwebp-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e982f995eec1352368f12ddb0b33580936d", "path": "libwebp-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9805421ae0118bcd24078e1895983919d6", "path": "libwebp-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98725d34c59b5fbb9f4d038907eacd8f72", "path": "libwebp-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98976306d0c8fd3a863e33c204eb05b159", "path": "libwebp.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e985d06c1379c37bcff592429b67ad98e52", "path": "libwebp.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9805a4c73d368ccfd0c715b0287b20321c", "name": "Support Files", "path": "../Target Support Files/libwebp", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98729363778d83d48851a6d5fa1d97d00c", "path": "src/dec/alpha_dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98b2b47e2f2b00f1aaa3b2cdd525e959a4", "path": "src/enc/alpha_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98e6a2c15660eee8c2cc13f760e44df2ed", "path": "src/dsp/alpha_processing.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98c75a63efa42433a76f83a2830b00291e", "path": "src/dsp/alpha_processing_mips_dsp_r2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98a553643f577d61c6df4dc56092346713", "path": "src/dsp/alpha_processing_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e988b5e6e5063ae612208a2603f1b3e9982", "path": "src/dsp/alpha_processing_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98bed720b954afbebaa79a134a60ba94ed", "path": "src/dsp/alpha_processing_sse41.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985b0a2fd5252799b27e295be92544222f", "path": "src/dec/alphai_dec.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e989c7caad79b1cdd2d183de71420aad393", "path": "src/enc/analysis_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e982eb49f9f9a9ef270e2ce41be5e56cc9d", "path": "src/enc/backward_references_cost_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e981b43981536105ff4bc0f1cf0cdf8cb97", "path": "src/enc/backward_references_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98346be588391b64a96cb778bb268863ba", "path": "src/enc/backward_references_enc.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98237ad868292be2e810ef23b960945ba1", "path": "src/utils/bit_reader_inl_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9892ff597803fc83e41b009c9206e96eb3", "path": "src/utils/bit_reader_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9870ed1b0c14e8e5ef5c1df8ed4812c72f", "path": "src/utils/bit_reader_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9823a72ef9b904c93cd16df3c43afa066c", "path": "src/utils/bit_writer_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981812907d7591eebc96e12c80aea926a5", "path": "src/utils/bit_writer_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e982a86370cbd0251014ff0751af27760d7", "path": "src/dec/buffer_dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9825d02888b5c7d5450d4892f76799dc5d", "path": "src/utils/color_cache_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986b2c9a3d91d91f9dddb1e86965e30f98", "path": "src/utils/color_cache_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985ae78595789f013d1fb1298e34a187ad", "path": "src/dec/common_dec.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985137d9e68a3d86dd757604b5d483bd98", "path": "src/dsp/common_sse2.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9826fab5600432194f657ef25d9400c729", "path": "src/dsp/common_sse41.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98c3b3c63679c50e0aa6e68868bf678997", "path": "src/enc/config_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98d053891b0d804305242c0016fa3bb7a6", "path": "src/dsp/cost.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98cf63b8e1a470ba4d58a6c00cb7675b6b", "path": "src/enc/cost_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b4a0e792d9a7835e0f8c9f95971aabcf", "path": "src/enc/cost_enc.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e987f1ac278fcea87008e40a9d1fd5d8a23", "path": "src/dsp/cost_mips32.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98ed886cf3444cdb57e043b7fd460aea8a", "path": "src/dsp/cost_mips_dsp_r2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98310c50708e63f17c9a7611a89b92dcfd", "path": "src/dsp/cost_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e984160561717c7d36bd65a878f27acc0fe", "path": "src/dsp/cost_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98ecac79840fc4ef0e1dce7bd48aa1265c", "path": "src/dsp/cpu.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985cdc8d3989037cf32508e7ee7f6fa42b", "path": "src/dsp/cpu.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98064614a39e2a96d44166915b8d168642", "path": "src/dsp/dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98bef208bd421c5c4cc67ed72ace5ead5d", "path": "src/dsp/dec_clip_tables.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98ae131d1ddcdb75b75ffb3458e8e690fd", "path": "src/dsp/dec_mips32.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98b8fef43a1a7eab837109434e677ab99f", "path": "src/dsp/dec_mips_dsp_r2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9867b199b32191addbf87d9a79251a34a2", "path": "src/dsp/dec_msa.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e983d17feec112d5a4bccc6f901f8270627", "path": "src/dsp/dec_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98c3990294d0d2da5687996687d7e16708", "path": "src/dsp/dec_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9839dd3db5f34f63e7ae9677751c8d659a", "path": "src/dsp/dec_sse41.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fe53d49b9f714c1d5e09fe3c5ebfaf4d", "path": "src/webp/decode.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986d81c53f0c35c69011216db24a86e302", "path": "src/dsp/dsp.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9854789f3097643abb6a2a89bc9266c723", "path": "src/dsp/enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e981aebd1f664364fb33bc3a87df6aadeaf", "path": "src/dsp/enc_mips32.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9855933f6a02b82b8e304ea19c78a59198", "path": "src/dsp/enc_mips_dsp_r2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98436d51eaaf26a5319f5ffdfcc33d4de1", "path": "src/dsp/enc_msa.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98ea842f1753f99512aa1c9d6d85bfdcbb", "path": "src/dsp/enc_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98fcc22dd8904c797a47dd51988e1f9831", "path": "src/dsp/enc_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98ad9d3f3b69a480ed2f9e71c0ce58d4e7", "path": "src/dsp/enc_sse41.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98764532bca752c4c47a2901c8684a802a", "path": "src/webp/encode.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9814902b3c4acd432542f91ff464bd3efd", "path": "src/utils/endian_inl_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9881439a1a09804155c19d65e5ff5accf8", "path": "src/enc/filter_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e982e368e9f3acf5bfe6381830af6a198de", "path": "src/dsp/filters.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e988713af0db0486d30111de90e836e88f3", "path": "src/dsp/filters_mips_dsp_r2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98854527519e5f9405b04c4357706a8ae8", "path": "src/dsp/filters_msa.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e984233c9786c0157f0d59ef02b69adb2e3", "path": "src/dsp/filters_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9891dbbe852205bdb1289bf81e44c4a23d", "path": "src/dsp/filters_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9832563e6464712942ee61ec55e275f099", "path": "src/utils/filters_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9832b593319b438d20c2a18c0f94e32c21", "path": "src/utils/filters_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9852b80caa5af8c69fb165c7322a469b94", "path": "src/webp/format_constants.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e982a90aa35058106e5333de18589bb861f", "path": "src/dec/frame_dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9885143f3c5e6214d92e65bbaa176aacfa", "path": "src/enc/frame_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e980057acc8dd54dcf03f7409561eba8635", "path": "src/enc/histogram_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988cebbf2c8781b47fb2ef10ed72911908", "path": "src/enc/histogram_enc.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9870b78c407a2921b334de7c94a64cb53c", "path": "src/utils/huffman_encode_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fc27ea397b5e33c575d58b965c7f89fa", "path": "src/utils/huffman_encode_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98b0cd270026c861919e3df8f5fb5f46a1", "path": "src/utils/huffman_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e18396173050e362490522cfb5c3e14b", "path": "src/utils/huffman_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98aa8982a63a47905d3bf0b4ebfd7bca36", "path": "src/dec/idec_dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9807f431eace3a1f97d3403f4bd2a5273f", "path": "src/dec/io_dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9835db657d45a6cdaad55d20ab575db6fd", "path": "src/enc/iterator_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e989dc427610f5e8ec4008b2fd237746e49", "path": "src/dsp/lossless.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98abc467c40456514feb874ba4acc6b53f", "path": "src/dsp/lossless.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e9a8436b4fb37e0451ebe2869afc79c8", "path": "src/dsp/lossless_common.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98b636c6792935bd2ce13de638150c5a56", "path": "src/dsp/lossless_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e980678453bb1951ae43a097dae3714795a", "path": "src/dsp/lossless_enc_mips32.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e986ecf3126f53cec68d5b0c25bdbb430dd", "path": "src/dsp/lossless_enc_mips_dsp_r2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9878236ba8b122ea1af2e859a429d190a7", "path": "src/dsp/lossless_enc_msa.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98a730f004672d4328f2d508515bcf4dd5", "path": "src/dsp/lossless_enc_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9875c2ec7d0118b1f7d36f934b3ed55546", "path": "src/dsp/lossless_enc_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e986f64105574b63a85350c6490a2b506bd", "path": "src/dsp/lossless_enc_sse41.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e989fc2fdd8980d7a93a2b9095fb340de34", "path": "src/dsp/lossless_mips_dsp_r2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98a4bbef0eda054d5ae45d4432283e861c", "path": "src/dsp/lossless_msa.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9862868f974bf97cb8be8c3d558f037e74", "path": "src/dsp/lossless_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98c4892be89b90d4056d0e8856d62b33bb", "path": "src/dsp/lossless_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9812e524f805112808c12aa11f57d52601", "path": "src/dsp/lossless_sse41.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a09d074bcc89c13fdc4cb8e28b61a7b9", "path": "src/dsp/mips_macro.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ea98623a75e5624c05b0763b8627bf1b", "path": "src/dsp/msa_macro.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984c657e3960e7e76b30f3a7f129604cd0", "path": "src/webp/mux_types.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98a1ac01d7145602dc908626793943d5f6", "path": "src/enc/near_lossless_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9897941f1d916f4cb8be013744c4c3008f", "path": "src/dsp/neon.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e989699108a437c465b431fa891847f4773", "path": "src/enc/picture_csp_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e984036911c8e47967aea8d6b0e4bd03f6a", "path": "src/enc/picture_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98c63e111adfcfc479103444be1e72506f", "path": "src/enc/picture_psnr_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e981c6a70c9b0c8033f2eacb5e964f317e0", "path": "src/enc/picture_rescale_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98d17102c193ad9d8c5f9fbbc5fb783ccd", "path": "src/enc/picture_tools_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e980eca17409f7677b4418dace9950b2bcf", "path": "src/enc/predictor_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983e2a069123ca3f2e0545b65664eb38ac", "path": "src/dsp/quant.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98f6a9ff8c3b0a95f306c0b6efc129ecd3", "path": "src/dec/quant_dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9894f107350cb5e46187459f5a1c2717ec", "path": "src/enc/quant_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9884650e46b595d54dcd706b713aadd850", "path": "src/utils/quant_levels_dec_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9892b93920323b2155f9bee77cadf98fdb", "path": "src/utils/quant_levels_dec_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98b791b630a14f1a7ead769f1d443e82ac", "path": "src/utils/quant_levels_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d7d7c927448cae1844222ad4892b31e7", "path": "src/utils/quant_levels_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9825184695133476fc12a8c8ef4a36ab6d", "path": "src/utils/random_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981b6dd9ad579c5c6a3cc94475fee6806e", "path": "src/utils/random_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98ac7a5c4bd307f956ed057e69bd0ef433", "path": "src/dsp/rescaler.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e983102360c58ab125347d5a540ed4f7507", "path": "src/dsp/rescaler_mips32.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98d77861fd109a21dc7eefda834a7f2181", "path": "src/dsp/rescaler_mips_dsp_r2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9807854239c66e50c43e7585469371cb1a", "path": "src/dsp/rescaler_msa.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e988e96f507771e06146784c18f1851db56", "path": "src/dsp/rescaler_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e984552f62d02b5fc18f761e2e9f1f66e3d", "path": "src/dsp/rescaler_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9897aa6431daf6635bb732ea0f6b7c542d", "path": "src/utils/rescaler_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987037a762db7c31b89835fffbb7ae60e4", "path": "src/utils/rescaler_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9880c5c0d9f97a437f94bc4bdf1c26e9f5", "path": "src/dsp/ssim.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9830f9b0fd02236ac481272b1917a61849", "path": "src/dsp/ssim_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98d48df069d38722dfc9007483add92dc0", "path": "src/enc/syntax_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9805078edc946f2de089cfb23d36aad8dd", "path": "src/utils/thread_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d5f0a0bb763784bb282054776940fc51", "path": "src/utils/thread_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9806905a83c4e602f1687626c3f99da0a8", "path": "src/enc/token_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e982c79a244814e421e8175cf70838bd29b", "path": "src/dec/tree_dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98b9f3bf45cdedc2ca19d19aa6b55eac79", "path": "src/enc/tree_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f9aa496af4d3c872d83c5e8c8fa141c8", "path": "src/webp/types.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9891735f7f702c345b85a6d271a3945f87", "path": "src/dsp/upsampling.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e983f8966fea821e2535bbc421129160bef", "path": "src/dsp/upsampling_mips_dsp_r2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e986dd8d36073de628430b67b7a4f7c73be", "path": "src/dsp/upsampling_msa.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9881a833e62dd58b6349176a8f9ecb44b7", "path": "src/dsp/upsampling_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e981cb8e9140d66e0995f4f29dfd5172793", "path": "src/dsp/upsampling_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e981b2adc6af9ba6121dc62f02da24660f0", "path": "src/dsp/upsampling_sse41.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98633755b8b1ef48ff890567ea27304f2d", "path": "src/utils/utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98887443de1c3c0b81957ad1a5f8212297", "path": "src/utils/utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98e7a6259a0329d61a306e6d09e5294665", "path": "src/dec/vp8_dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988032f789e7ddf9af5a3d9c97c146262c", "path": "src/dec/vp8_dec.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a46fc74c0d3a65a876bdd99fde6e5c0c", "path": "src/dec/vp8i_dec.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9847d7159f2b5933a445950679b881c3e3", "path": "src/enc/vp8i_enc.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9830089de03a26b585b86674e9b1bfce4a", "path": "src/dec/vp8l_dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98f5922defa18ebd1bbdd14abb60b9bbd0", "path": "src/enc/vp8l_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d37817f4f1625044c5b3aa3ff4cb1dd7", "path": "src/dec/vp8li_dec.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986377f54022695e4dcee8e92855efc1a9", "path": "src/enc/vp8li_enc.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98d0ff5f8680cad6be4c18a1a2447daf97", "path": "src/dec/webp_dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98def84cc605698d87550a23b41b0f6fe0", "path": "src/enc/webp_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e1c8712f5ffcca6a1fdbc984430f4384", "path": "src/dec/webpi_dec.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98d350ba71614b2fe5a92325348206828c", "path": "src/dsp/yuv.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a61a51a22a459e264dfd257edc9e3875", "path": "src/dsp/yuv.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98d8d778986d82000f7b4c8e1368b5073b", "path": "src/dsp/yuv_mips32.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e988106f367c1ff41fae24c9e58bef9dc8d", "path": "src/dsp/yuv_mips_dsp_r2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98c60d4dd29fb2aee13fb02fb7516d95c1", "path": "src/dsp/yuv_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98597293041c9796314dae68c8cc43b6b7", "path": "src/dsp/yuv_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e981e93d0ec200b98d3cdc1be026908528a", "path": "src/dsp/yuv_sse41.c", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98367e9441ef4a39a30523f10e57adbebf", "name": "webp", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985ac18d789f952541e7531051b09c99dc", "name": "libwebp", "path": "libwebp", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988c0de256ca7e881d02d38aa3f800fa98", "path": "Mantle/include/Mantle.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985ae829aceaf51eaa11e9c7dcf6e11c02", "path": "Mantle/include/MTLJSONAdapter.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ec9f54214ed7fd0f608fee74c5d91150", "path": "Mantle/MTLJSONAdapter.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983ab103334d3a6a6a40c4efece1918a65", "path": "Mantle/include/MTLModel.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9804b45f52d65914a38d27eccf98303e52", "path": "Mantle/MTLModel.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9807f152aa4f8b44ca2f68b2b6d6d31716", "path": "Mantle/include/MTLModel+NSCoding.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987b8e034f7aa740dba6e40fd66f59d881", "path": "Mantle/MTLModel+NSCoding.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9870646ba0d954e081272d99141449a301", "path": "Mantle/MTLReflection.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d16229eb9422f38f3d0b367b68da434b", "path": "Mantle/MTLReflection.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98885ecce8a61beeeb940c392d2583bd25", "path": "Mantle/include/MTLTransformerErrorHandling.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98af6599f82322ca1fc083e23e3e27634d", "path": "Mantle/MTLTransformerErrorHandling.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dd5c1355ebb00e9627832f8fa917ee1e", "path": "Mantle/include/MTLValueTransformer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98588934e938b2ec797d8c60df10c8179c", "path": "Mantle/MTLValueTransformer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981b79bf8ead06d5025555f68b77516aec", "path": "Mantle/include/NSArray+MTLManipulationAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a783159dc0910a937fa02f2603eb87f3", "path": "Mantle/NSArray+MTLManipulationAdditions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980e2aa088a2d6212e87fce7607bcbf40b", "path": "Mantle/NSDictionary+MTLJSONKeyPath.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980830ed23affb5ac740ea11263178bdf5", "path": "Mantle/NSDictionary+MTLJSONKeyPath.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989a84233186539b8c6e988b580a510e17", "path": "Mantle/include/NSDictionary+MTLManipulationAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9843b4e79ecd0b5cf98030b8fea0ae34e1", "path": "Mantle/NSDictionary+MTLManipulationAdditions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ffcaf3c4000612a154f12f7294bfff29", "path": "Mantle/include/NSDictionary+MTLMappingAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984bad02fb5bcce5fc63a7967b13e41ae9", "path": "Mantle/NSDictionary+MTLMappingAdditions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982930150a4afc57a161b742ab3ab9bf75", "path": "Mantle/NSError+MTLModelException.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987b6493879c2a7f2c9c678c674ced64a7", "path": "Mantle/NSError+MTLModelException.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98129d5e1c86c719ae78e6920708790dc1", "path": "Mantle/include/NSObject+MTLComparisonAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e78d06c99f407b2bcb92d4ec77d7c55a", "path": "Mantle/NSObject+MTLComparisonAdditions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989c29905972e98f1df72ac2bdc83984f3", "path": "Mantle/include/NSValueTransformer+MTLInversionAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e2e63755adae1fc152a924432842c5ee", "path": "Mantle/NSValueTransformer+MTLInversionAdditions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c587b1676f5cb5532721cbc32f9770a8", "path": "Mantle/include/NSValueTransformer+MTLPredefinedTransformerAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987efc1de9776a557b296b65e39198dec7", "path": "Mantle/NSValueTransformer+MTLPredefinedTransformerAdditions.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98672509428d71bb8592ca6c29018bc406", "path": "Mantle/extobjc/include/MTLEXTKeyPathCoding.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ce04ab2aaa4f4da7e57f6d2a0aba2070", "path": "Mantle/extobjc/include/MTLEXTRuntimeExtensions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988fd7e4df5bfa07b6c4693812f82dec2d", "path": "Mantle/extobjc/MTLEXTRuntimeExtensions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983682e6ae5eaa6162887dbdd23554cf55", "path": "Mantle/extobjc/include/MTLEXTScope.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980d4850bd0a7373f7311c459e728d302b", "path": "Mantle/extobjc/MTLEXTScope.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f8ca6d10484b66c63aef9b549d0fcac4", "path": "Mantle/extobjc/include/MTLMetamacros.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f50e2cd16b63c62e8d91d7abe775fb8d", "name": "extobjc", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98e1eca61269651b28814b5a3d758ea292", "path": "Mantle.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e5c9cc90f6a538577390b929e602acdb", "path": "Mantle-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98f7619d76155d412d4ab961c25c44c195", "path": "Mantle-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e41a96f7ec39521ab23d3d6acd25d7a4", "path": "Mantle-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988c0a8d0305428221ca8e2d9042302043", "path": "Mantle-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e987b45189253ad50f4e749e4865874162d", "path": "Mantle.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f92d543457613c972563dba055a5155f", "path": "Mantle.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981ef4d0961b3b001c85b90ec934a29b2b", "name": "Support Files", "path": "../Target Support Files/Mantle", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987fbd677b273a8ee0b080382511b6d3c5", "name": "Mantle", "path": "Mantle", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98424826f24bb932b61ed306fe5b8a77b3", "path": "SDWebImage/Private/NSBezierPath+SDRoundedCorners.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984820a1d91e2883e0a92bd180f4c2d71e", "path": "SDWebImage/Private/NSBezierPath+SDRoundedCorners.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98db4cb650953cba152f8a1378a05a5ba3", "path": "SDWebImage/Core/NSButton+WebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cff8760d240340a624e224c2edaa74d2", "path": "SDWebImage/Core/NSButton+WebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986937d2fa1b787eae0daa85945e9525c9", "path": "SDWebImage/Core/NSData+ImageContentType.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f9d00c2066c957b213bf0a30c320cbeb", "path": "SDWebImage/Core/NSData+ImageContentType.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bf15bd876ac7c2c18248062608392cf0", "path": "SDWebImage/Core/NSImage+Compatibility.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b7224d3da43804f924feb0834db27999", "path": "SDWebImage/Core/NSImage+Compatibility.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98126fa88adb8c6deca92db530687d40d6", "path": "SDWebImage/Core/SDAnimatedImage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982c3480008c7b347223824bb7ce59fef4", "path": "SDWebImage/Core/SDAnimatedImage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98656b54bed5edab98b1f4c5e125864be4", "path": "SDWebImage/Core/SDAnimatedImagePlayer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9863f5bd05d15fe8cc3875ca870582e475", "path": "SDWebImage/Core/SDAnimatedImagePlayer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9809b0dad5ef726b4f48f7fa9b683da7a1", "path": "SDWebImage/Core/SDAnimatedImageRep.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e394f35a411f21a00c67be46266ef6ea", "path": "SDWebImage/Core/SDAnimatedImageRep.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a93e7e24fc2ca653e4876f2a9b192347", "path": "SDWebImage/Core/SDAnimatedImageView.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d56bcfafc236c5e33bc93117788af7d8", "path": "SDWebImage/Core/SDAnimatedImageView.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fb2266e1b9fec75b9142cc20097474eb", "path": "SDWebImage/Core/SDAnimatedImageView+WebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980a4bc7b7d728247a1a485ff9da4e0699", "path": "SDWebImage/Core/SDAnimatedImageView+WebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984f00723a9e79ee388c0c61da899bf5de", "path": "SDWebImage/Private/SDAssociatedObject.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986c1aa3adda72a29cfa667136ab7bbb64", "path": "SDWebImage/Private/SDAssociatedObject.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981a93dfab6277a5bd7acba50babc1db8f", "path": "SDWebImage/Private/SDAsyncBlockOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987c59b53075ad8ef2aa2702112ad220bf", "path": "SDWebImage/Private/SDAsyncBlockOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9865c88c5bae885594912f6fc4d227d686", "path": "SDWebImage/Core/SDCallbackQueue.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98390001f95e6c8d54147afd043c3bdd8c", "path": "SDWebImage/Core/SDCallbackQueue.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98de5e72ea86262c8315e1127e6d2e955d", "path": "SDWebImage/Private/SDDeviceHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981d0dd26cf1572c1e9e1979a5490cfe7b", "path": "SDWebImage/Private/SDDeviceHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9841ca5172a2e2022bca9925a3134e2895", "path": "SDWebImage/Core/SDDiskCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98261db8b2b4bc16688c1792b36980f758", "path": "SDWebImage/Core/SDDiskCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981f0f580297128ae43355742e119546d8", "path": "SDWebImage/Private/SDDisplayLink.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9860c65defe7325613b3adad209221c116", "path": "SDWebImage/Private/SDDisplayLink.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983bd15dbdd191e316295f24fe2ef3f78c", "path": "SDWebImage/Private/SDFileAttributeHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f06e7ea50a4dfff9b09f74e1a68484b4", "path": "SDWebImage/Private/SDFileAttributeHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98684e7c91d27e3f902d84c5e4fcc13347", "path": "SDWebImage/Core/SDGraphicsImageRenderer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a5e24d304ecb16eae9352fdef138bde3", "path": "SDWebImage/Core/SDGraphicsImageRenderer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ba98d6df7a2b4f3441a20713bba8f908", "path": "SDWebImage/Core/SDImageAPNGCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b7e94bd14028990e8971f6597e44b92f", "path": "SDWebImage/Core/SDImageAPNGCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988f61f6dac37477f7e841994bc6f53aaa", "path": "SDWebImage/Private/SDImageAssetManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ebe82be24c6b579072c9cc75085fee39", "path": "SDWebImage/Private/SDImageAssetManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98eef5425403ea49407a9b56deac848653", "path": "SDWebImage/Core/SDImageAWebPCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987c131b7fd629c28e0a341a3d20ad1773", "path": "SDWebImage/Core/SDImageAWebPCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9834e035e15aa70eb9deece7abfa8dc672", "path": "SDWebImage/Core/SDImageCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b5b8cef6809ce5806a90c8b2243c3090", "path": "SDWebImage/Core/SDImageCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9864e0f0cb90732363d475ef6c53a2a039", "path": "SDWebImage/Core/SDImageCacheConfig.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c249a58fa34f783a3e85153517d000d9", "path": "SDWebImage/Core/SDImageCacheConfig.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9882d89ad05e5a02de072d50e06fe0706c", "path": "SDWebImage/Core/SDImageCacheDefine.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989237bb02f9e0229ae2427e0c3973c5f8", "path": "SDWebImage/Core/SDImageCacheDefine.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986c36fa36704787142ac838d99aa72384", "path": "SDWebImage/Core/SDImageCachesManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98949828475127815c2352ae98c48e59e1", "path": "SDWebImage/Core/SDImageCachesManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d1d39bd3d28242c2e5df497afbf8f7bf", "path": "SDWebImage/Private/SDImageCachesManagerOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9876fb995a3f998645e2e375da4f86e116", "path": "SDWebImage/Private/SDImageCachesManagerOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9840de2b418e841453f76dd00dde4a5732", "path": "SDWebImage/Core/SDImageCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9815472c75e7d41068cd1215128732533a", "path": "SDWebImage/Core/SDImageCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981d014ab0db70879b574793f6d2ff5d4e", "path": "SDWebImage/Core/SDImageCoderHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980410a9d4cc9b545293f21ec8627a1803", "path": "SDWebImage/Core/SDImageCoderHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98595f29ae6560b758d2e782e68e70ad2e", "path": "SDWebImage/Core/SDImageCodersManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b798375418c25cf26c79194f34bff134", "path": "SDWebImage/Core/SDImageCodersManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9840850aa7ab68b6ded9fe97c502668120", "path": "SDWebImage/Core/SDImageFrame.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980fb947608b57700bedb722f2ec2ab604", "path": "SDWebImage/Core/SDImageFrame.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ffb4c9747ddf2c1a48dfc87bf31dd926", "path": "SDWebImage/Private/SDImageFramePool.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982f3571f3552003e64420ceab0c54cabe", "path": "SDWebImage/Private/SDImageFramePool.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98541bf6053c2f3946e580f1ece52e42ef", "path": "SDWebImage/Core/SDImageGIFCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98204523b9cfa05e0edae1950868ce5ccd", "path": "SDWebImage/Core/SDImageGIFCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f281358c8001d5457cc626bc3e308ccf", "path": "SDWebImage/Core/SDImageGraphics.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e22b07bf55bc4b5d6f189b314d19e191", "path": "SDWebImage/Core/SDImageGraphics.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988f1a03804cc9bf1b4baf68b91ae3e94b", "path": "SDWebImage/Core/SDImageHEICCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989649880b0f78c33097fd01ac9ad3aca7", "path": "SDWebImage/Core/SDImageHEICCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fa5aa744e05637d78a4e3a8b075ee751", "path": "SDWebImage/Core/SDImageIOAnimatedCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ae4d3a81642e5ed68963d6b4a02ef099", "path": "SDWebImage/Core/SDImageIOAnimatedCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98011c6544a2bc9adf6a6401d618444ff8", "path": "SDWebImage/Private/SDImageIOAnimatedCoderInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9869f8d28b6140d941fe6d47d082d17e68", "path": "SDWebImage/Core/SDImageIOCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984aa6002287a9e3780aea152d6dd49d01", "path": "SDWebImage/Core/SDImageIOCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98330c72d8f6ad858c4912cfc96f17319e", "path": "SDWebImage/Core/SDImageLoader.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985919be1e40a329772836e013cc2248cf", "path": "SDWebImage/Core/SDImageLoader.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9814df74389d34d65785780d7224353f48", "path": "SDWebImage/Core/SDImageLoadersManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d568284c58fd8ead89b5307a7f147129", "path": "SDWebImage/Core/SDImageLoadersManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fd329a7e208e0d3665341c0a44a96be9", "path": "SDWebImage/Core/SDImageTransformer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983fd80cb41ca5db32f529fd45d606602b", "path": "SDWebImage/Core/SDImageTransformer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98300673f8099321bfe2412ec83887d8ce", "path": "SDWebImage/Private/SDInternalMacros.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d1d77eda25cd6c09c62efcbfa38ec028", "path": "SDWebImage/Private/SDInternalMacros.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9834832cf22fe75f9f565027040bbb18ec", "path": "SDWebImage/Core/SDMemoryCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ea02223b8d81a6cfec52375f9af37a23", "path": "SDWebImage/Core/SDMemoryCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98991f3481719b761be370b5ba99ae5194", "path": "SDWebImage/Private/SDmetamacros.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dcaac3c4d0ffbdacd8972d40df3451ea", "path": "SDWebImage/Private/SDWeakProxy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9844032365342621bbc113a61555271f01", "path": "SDWebImage/Private/SDWeakProxy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a66f59f0c691d1abba604de9bd2f1198", "path": "WebImage/SDWebImage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982f0ed6a04c91c313b070d3aadd2f6afb", "path": "SDWebImage/Core/SDWebImageCacheKeyFilter.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98201a5b9e5ec97e443069b3c98f9fb4c8", "path": "SDWebImage/Core/SDWebImageCacheKeyFilter.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d087f4c9b4ff41349ab7142e6c56e303", "path": "SDWebImage/Core/SDWebImageCacheSerializer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98618ea2f072b32cc826f2be2142958b0e", "path": "SDWebImage/Core/SDWebImageCacheSerializer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d0e29575b1c8ad0358d9b2e9633ddd34", "path": "SDWebImage/Core/SDWebImageCompat.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987f24cf269d93df7309e7aba62a2cb07a", "path": "SDWebImage/Core/SDWebImageCompat.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98343f60be84ccc156cd75dd36e3456732", "path": "SDWebImage/Core/SDWebImageDefine.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98734221d27706bb89353c3639a4e097ab", "path": "SDWebImage/Core/SDWebImageDefine.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989fe7c3b5a6f624fd7a06f9fe0edaa44b", "path": "SDWebImage/Core/SDWebImageDownloader.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983f9ac48ba1f810de199c0f2e5369b691", "path": "SDWebImage/Core/SDWebImageDownloader.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fa914bec8fc84fe59e26fd0c18d81dd5", "path": "SDWebImage/Core/SDWebImageDownloaderConfig.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982a7fa72e69186cabebdffe810ddcd97a", "path": "SDWebImage/Core/SDWebImageDownloaderConfig.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9819a6f3004c225d1b88244ee6e95f246f", "path": "SDWebImage/Core/SDWebImageDownloaderDecryptor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98809a082d6b5708a3f8d48486783cc4b3", "path": "SDWebImage/Core/SDWebImageDownloaderDecryptor.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a3de52500369b29790c4dcb3637ca8b5", "path": "SDWebImage/Core/SDWebImageDownloaderOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98887e0865bf215a1abac3fcd58a14f4cd", "path": "SDWebImage/Core/SDWebImageDownloaderOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987e03efb7821cb4e8d1b49278109427bc", "path": "SDWebImage/Core/SDWebImageDownloaderRequestModifier.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d38ab36fe42412f051a1bbf9c67f42dc", "path": "SDWebImage/Core/SDWebImageDownloaderRequestModifier.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ce57e91a316d482b6e5a46145dcd094e", "path": "SDWebImage/Core/SDWebImageDownloaderResponseModifier.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98749e71dbcf2c4a6eb221e081cb4ef5c9", "path": "SDWebImage/Core/SDWebImageDownloaderResponseModifier.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bfc60f0b764d86a4bdfc54b30fab66dd", "path": "SDWebImage/Core/SDWebImageError.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ea5834c027d63a6ae144c80ec1d3c9de", "path": "SDWebImage/Core/SDWebImageError.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e0069479d3290f8ccebcc6b2c1b82acd", "path": "SDWebImage/Core/SDWebImageIndicator.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f0e2cf03d19e8c004c162b76af72c49d", "path": "SDWebImage/Core/SDWebImageIndicator.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98da83a5b2c24ca801bd10a302f6c2a91b", "path": "SDWebImage/Core/SDWebImageManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983a2389550de076f0dd479f08df79b973", "path": "SDWebImage/Core/SDWebImageManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e1a0e4ad27fe1a80d1df025963ee06f5", "path": "SDWebImage/Core/SDWebImageOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98854cff839f179e425d5bf8826d6d9cf6", "path": "SDWebImage/Core/SDWebImageOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b0d23a6354d6116594c020d0eb7b4302", "path": "SDWebImage/Core/SDWebImageOptionsProcessor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989c759aee74a40148b53602cd1f528b55", "path": "SDWebImage/Core/SDWebImageOptionsProcessor.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fec9509aed9de820fee300d59a157651", "path": "SDWebImage/Core/SDWebImagePrefetcher.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983d5099268114c6ecbecb8ef61731e0d0", "path": "SDWebImage/Core/SDWebImagePrefetcher.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982a84053977f029e9c6a8af5ee0d06146", "path": "SDWebImage/Core/SDWebImageTransition.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98798df6e060a78873eb15123843713afd", "path": "SDWebImage/Core/SDWebImageTransition.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9819a7290628a073b4ce9e05b3bb47e355", "path": "SDWebImage/Private/SDWebImageTransitionInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98897d081df3524ce1e04a09f1dd2d6841", "path": "SDWebImage/Core/UIButton+WebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9824354c922f9f669c9a9fb3e446529cbd", "path": "SDWebImage/Core/UIButton+WebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988a6510913e952c3ed35f7f6e3a96e4f0", "path": "SDWebImage/Private/UIColor+SDHexString.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ea5ae625a4feecf2a96a93267b9d2493", "path": "SDWebImage/Private/UIColor+SDHexString.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987454457ffe851dbc78169df0742588ef", "path": "SDWebImage/Core/UIImage+ExtendedCacheData.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988d7f63fee8711fba61e6fae6c511da35", "path": "SDWebImage/Core/UIImage+ExtendedCacheData.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e5015e400a10b1d8d3b43b5d5b779cc3", "path": "SDWebImage/Core/UIImage+ForceDecode.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9804fd380c55c610ba9da93784becddd09", "path": "SDWebImage/Core/UIImage+ForceDecode.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983506a72aba0fe08cc97f6e75a4230d6f", "path": "SDWebImage/Core/UIImage+GIF.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ad106bbe3bfe9b06bb8c1dda228c533d", "path": "SDWebImage/Core/UIImage+GIF.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985d218043d35e8c37c5b333b011a199f7", "path": "SDWebImage/Core/UIImage+MemoryCacheCost.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fcda6a887b2d170d9b70ab38ba16f3be", "path": "SDWebImage/Core/UIImage+MemoryCacheCost.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98620e34488637c77ee158773130c22881", "path": "SDWebImage/Core/UIImage+Metadata.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9816174419b53040707fda9f6b73268da9", "path": "SDWebImage/Core/UIImage+Metadata.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98549472a17af6115387f5a35619bb365b", "path": "SDWebImage/Core/UIImage+MultiFormat.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fb992c51fc9978927a2e58ac5bf69970", "path": "SDWebImage/Core/UIImage+MultiFormat.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9892d8b531ce44b21d48c8fe5a1097085c", "path": "SDWebImage/Core/UIImage+Transform.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983fd308f04e09b0b2cbc8713b38ad0203", "path": "SDWebImage/Core/UIImage+Transform.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a5500edb87d87ef81178d71d80ab2cbd", "path": "SDWebImage/Core/UIImageView+HighlightedWebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989e5419c38acd880abf7e00a5c0c2ae40", "path": "SDWebImage/Core/UIImageView+HighlightedWebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981cb9b33aa45cf8d7464d94b96a74d8ae", "path": "SDWebImage/Core/UIImageView+WebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f830b39ded061f0c4828f6723b06c2af", "path": "SDWebImage/Core/UIImageView+WebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f40917bd64a588021683a0e25549c129", "path": "SDWebImage/Core/UIView+WebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98338ed93a485fbe4285e771870d518e55", "path": "SDWebImage/Core/UIView+WebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988576df126807198cdf942e0a87ff67eb", "path": "SDWebImage/Core/UIView+WebCacheOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9832da39bd81f47d1885ce93ebe7d2f36e", "path": "SDWebImage/Core/UIView+WebCacheOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9885eef30dffaee18c2d5d24dc5d5af7a9", "path": "SDWebImage/Core/UIView+WebCacheState.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98848ff3c3794a652439da6f993aab04da", "path": "SDWebImage/Core/UIView+WebCacheState.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98e3991b877ef4f9abd9e57b148db0bc39", "path": "WebImage/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9849b931845edf859738dc7bd7b3598519", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9832a0fd1e12ff12bbb8eff46a1162969c", "name": "Core", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e985f9f6941b24c6bae4dd99e7b95d0a4d2", "path": "ResourceBundle-SDWebImage-SDWebImage-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9856f3b3d58e3f4331f3734286f10c23af", "path": "SDWebImage.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c8f602a26df8eaaa208564fdbb7a6942", "path": "SDWebImage-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98ce09e8209fa11e8198fe4f99a3b0d08e", "path": "SDWebImage-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980671d1a6e2f3a439cf247503bded9c3c", "path": "SDWebImage-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98676caeae67336f8e618b81fa71091f67", "path": "SDWebImage-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98bf479306afc8672f25b0fa0fe30a7439", "path": "SDWebImage.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e980cd924ae00304439057107700d1d8416", "path": "SDWebImage.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ec74250a1d03fadaf04d198f1f9e330e", "name": "Support Files", "path": "../Target Support Files/SDWebImage", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9852fda347c04254d719ce53bcb92d1c83", "name": "SDWebImage", "path": "SDWebImage", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9819acd37287c236279d08a89b2d4f721c", "path": "SDWebImageWebPCoder/Classes/SDImageWebPCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98129d3f27065e7cf14f2b649b3b279143", "path": "SDWebImageWebPCoder/Classes/SDImageWebPCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c3a5598d43b0878333666469ff18ca44", "path": "SDWebImageWebPCoder/Private/SDInternalMacros.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9846cee45b6e627c33c6b169c4f0d46178", "path": "SDWebImageWebPCoder/Private/SDmetamacros.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986b02dee552a84f22ee4309ee7beb82fd", "path": "SDWebImageWebPCoder/Module/SDWebImageWebPCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e63a2a3fe444bf69ee4a74e7594b7866", "path": "SDWebImageWebPCoder/Classes/SDWebImageWebPCoderDefine.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980674e5ec18c2011ee642336845458451", "path": "SDWebImageWebPCoder/Classes/SDWebImageWebPCoderDefine.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f07e10b8666a6adc2b0a91139cf934c3", "path": "SDWebImageWebPCoder/Classes/UIImage+WebP.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985c5c84badb5d699a03f96b6ce75e2504", "path": "SDWebImageWebPCoder/Classes/UIImage+WebP.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98eed4180948512b5605a7a057477bfe8c", "path": "SDWebImageWebPCoder.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e181bd413f6233cec42190e2dd5a5823", "path": "SDWebImageWebPCoder-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98c0930beed766dea761c76547d8f3837c", "path": "SDWebImageWebPCoder-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f1f21dc4f097de06347f6615a1944b79", "path": "SDWebImageWebPCoder-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e988b012247241c57889880f2892e7035b3", "path": "SDWebImageWebPCoder.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986ff1c1dee3ccfda72c2ec78ec7019f98", "path": "SDWebImageWebPCoder.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9854a3fda994b909f535d7039cb8f6dc87", "name": "Support Files", "path": "../Target Support Files/SDWebImageWebPCoder", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c13429307e58ebd6f3f1bc8649dd3bfc", "name": "SDWebImageWebPCoder", "path": "SDWebImageWebPCoder", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98752e82cf75f993159c1a796f2b06c6c1", "path": "SwiftyGif/NSImage+SwiftyGif.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9859ff34dba50f3a622627513d12f78cdb", "path": "SwiftyGif/NSImageView+SwiftyGif.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985fb94f11bdcb3bb420619f88efedac0d", "path": "SwiftyGif/ObjcAssociatedWeakObject.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987c00044d8392c87384aa29e1fc6d79a3", "path": "SwiftyGif/SwiftyGif.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98da42a8db612c26a41e194d175ef631e4", "path": "SwiftyGif/SwiftyGifManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985204a991f73d1bd88ab02028bf5d4215", "path": "SwiftyGif/UIImage+SwiftyGif.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9810110cb039dba69faca23bfddc26a0cb", "path": "SwiftyGif/UIImageView+SwiftyGif.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98a358d5576955ae2157db6da73cf3d518", "path": "SwiftyGif/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980dad38ba46b78208005e6ace4c851560", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98e8d3356cf8b4987af11d160389c01be7", "path": "ResourceBundle-SwiftyGif-SwiftyGif-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98cc8f27c91f5f1e1b90384269fd09168e", "path": "SwiftyGif.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989fb7640ae969c0d5eb3c8bd73b6582a3", "path": "SwiftyGif-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e981113df98365de6aae8c89396197b1fda", "path": "SwiftyGif-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bb73dc693bdd288e618c38ee83e7c721", "path": "SwiftyGif-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986ba8f7f2a08a77ea9c9f884d3858184d", "path": "SwiftyGif-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e982404161967c3e002c2704102b9c8004f", "path": "SwiftyGif.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9833dee58a04c609e82bf0695612dc8966", "path": "SwiftyGif.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9863c4ed638b012cf4cd3a7bb7981e6d4d", "name": "Support Files", "path": "../Target Support Files/SwiftyGif", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980a94a5afeaac95df43edb76ec3faaa52", "name": "SwiftyGif", "path": "SwiftyGif", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98af1e5dadc5caff46147e978b0c187ab9", "path": "Toast-Framework/Toast.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fd8e1e52f76454bee47b459247dbef13", "path": "Toast/UIView+Toast.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e24233368c6f1f856d35a02c22b0127c", "path": "Toast/UIView+Toast.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9851ce1db52f73dd1aa257d3c7d4bfa824", "path": "Toast/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985ee8945f78350ebe4b44ba8b45110931", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e986fb505f032c57e6acff700b26a78d448", "path": "ResourceBundle-Toast-Toast-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98a636bf8dafe0a753f6c4630e851781b5", "path": "Toast.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9886b2b007829c3b67f5417f3235f55ce5", "path": "Toast-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9808961ecf35f4e211f60d87cb82362103", "path": "Toast-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9871b42c184cf3a27a0734821b544970f8", "path": "Toast-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c766c546b9bb37297e09766b9a486c16", "path": "Toast-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e983d91d880ecb1ad1dccab818ed218d118", "path": "Toast.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d620c1dab50c7a4a738f4a21c3994402", "path": "Toast.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b1c964af40bbbde7e71bce9f5195082e", "name": "Support Files", "path": "../Target Support Files/Toast", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987e24c5be2c652315c5376ca0e0403d4e", "name": "Toast", "path": "Toast", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.xcframework", "guid": "bfdfe7dc352907fc980b868725387e9807e8f50153a68e6adc7330de8999fc4e", "path": "ImSDK_Plus.xcframework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984dfc78e764241f6ea01e13f79ff96f48", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9809bda0a888750d70f63d3bb5742dc324", "path": "ImSDK_Plus.xcframework/ios-arm64_armv7/ImSDK_Plus.framework/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989ccd44f393f6fc699069d62b385cbdb8", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98f0db53fd14ff4db44abe73e95df255cd", "path": "ResourceBundle-TXIMSDK_Plus_iOS_XCFramework_Privacy-TXIMSDK_Plus_iOS_XCFramework-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e98670c2385e4f61b24b3b4551cff1409ba", "path": "TXIMSDK_Plus_iOS_XCFramework-xcframeworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98bf0ae4f6046225763c2667aa1dc36fef", "path": "TXIMSDK_Plus_iOS_XCFramework.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e989bcc3122661719b79fc8ac22d48359d5", "path": "TXIMSDK_Plus_iOS_XCFramework.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988d470ac921fee3739ac8f6f8a245bb49", "name": "Support Files", "path": "../Target Support Files/TXIMSDK_Plus_iOS_XCFramework", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c3b8c48d2b1a669ff088df2c7acd8e19", "name": "TXIMSDK_Plus_iOS_XCFramework", "path": "TXIMSDK_Plus_iOS_XCFramework", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983278346741f95001b51bf6044bcccf79", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"guid": "bfdfe7dc352907fc980b868725387e981499191ef366ef1430f32fdb152aa93b", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98a3f4680c874ecbcb5b5a6338bc13a426", "path": "Pods-Runner.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9814181933c973297b0001d0e9ee64381d", "path": "Pods-Runner-acknowledgements.markdown", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9887440869a53d6020d968ddf6b9b30aa0", "path": "Pods-Runner-acknowledgements.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fed0970e703d355c9aff813f280aafdc", "path": "Pods-Runner-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e9826e505869a67d81656859ded68178c1d", "path": "Pods-Runner-frameworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98de02ac5005ab1eb052abf25c63731e95", "path": "Pods-Runner-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e98f6f4dcb116856f2da0fa97d3015cb1af", "path": "Pods-Runner-resources.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980ec6fbca7264a6936f2adfc48dd7f5bc", "path": "Pods-Runner-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9850cc7fc2d23136fb4fac488d6c47df20", "path": "Pods-Runner.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98dc1a21852c7a085c7dd8f65cf0fa9907", "path": "Pods-Runner.profile.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f17abf55d75f35efcaf45a1185b085b6", "path": "Pods-Runner.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cf3c1972df678a5e36df75a46391500d", "name": "Pods-Runner", "path": "Target Support Files/Pods-Runner", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e984c4f55ec853c945e234980557a98aed8", "path": "Pods-RunnerTests.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98fc0f7e7242f459f81e455145932dcafd", "path": "Pods-RunnerTests-acknowledgements.markdown", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e985f8b68b152f46f18718da20c04e675cb", "path": "Pods-RunnerTests-acknowledgements.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98022654f1ff78dd844d694dba2439dab2", "path": "Pods-RunnerTests-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989e5ad6b9a07953a12c7008a15bd9c99c", "path": "Pods-RunnerTests-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e5e8bcdff29e5f8321be18f7989b4bc7", "path": "Pods-RunnerTests-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98144cd18850e477837c238075d5256ffe", "path": "Pods-RunnerTests.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981b663a2c82f0220040296818ba53477e", "path": "Pods-RunnerTests.profile.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98965b92d39d30a7872295adc2841cd1b1", "path": "Pods-RunnerTests.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9859551a2ccb1df711861b574920cd49bf", "name": "Pods-RunnerTests", "path": "Target Support Files/Pods-RunnerTests", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dafc421ff02609f2772b356038eb9849", "name": "Targets Support Files", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98677e601b37074db53aff90e47c8f96d1", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "bfdfe7dc352907fc980b868725387e98", "path": "/Users/<USER>/loom_dynamics/game-store/ios/Pods/Pods.xcodeproj", "projectDirectory": "/Users/<USER>/loom_dynamics/game-store/ios/Pods", "targets": ["TARGET@v11_hash=b8e2259fed7266593d92fb594793823a", "TARGET@v11_hash=2c9836d945232606e77b20d9d036236a", "TARGET@v11_hash=7cea588119c9218d483788decd2c3167", "TARGET@v11_hash=0ee66314a88d9bdeed14ad50967b60ff", "TARGET@v11_hash=ffdb91fba4ec2eb3284273eebdd9c91e", "TARGET@v11_hash=afa25fce27b5a384816eb1d4a6454c08", "TARGET@v11_hash=e7ad6187bbe54e23e6a98ef4f6c97946", "TARGET@v11_hash=6f5970ea8cd47e2b4265d6c4afc3d1f4", "TARGET@v11_hash=2f6c295fef841f55d905a10d8dbf09c9", "TARGET@v11_hash=8552b410105aab9befce2685ce5f3bd9", "TARGET@v11_hash=2343f2700c8c117c7b911c3dfcba8bf0", "TARGET@v11_hash=c97a4619bf88fe74a3ca50ad4c5a6502", "TARGET@v11_hash=27b22c5d1d50b9d611f488c4fb0cc8bf", "TARGET@v11_hash=2d4ef7175f318167ad4efd97f7d2c88f", "TARGET@v11_hash=c5842b00e357589766cde6c3da2fb17d", "TARGET@v11_hash=6051427cbae8fdec1b4112983bb709ee", "TARGET@v11_hash=ca721566b42f95ceb5bf35742a4371f6", "TARGET@v11_hash=1050875598bdc0aa56fa7f0688393af7", "TARGET@v11_hash=5fa9c1a01014bf6ed33d9c19a4e46965", "TARGET@v11_hash=369397fa4f69bb5ee1a091be389db98c", "TARGET@v11_hash=990b1131579407f09d220a9dbcd13fa8", "TARGET@v11_hash=2be94a05d595d88cc6c4d173edf7ef3e", "TARGET@v11_hash=5c769d1b9104429f9cef579ee3407ad1", "TARGET@v11_hash=0dc25f599efb9617a0e1435f298f1335", "TARGET@v11_hash=4077cfd8dba9683ef4e3530b9f1eeed5", "TARGET@v11_hash=c03bc484ce85cf5da2efcfe93ff6e1b8", "TARGET@v11_hash=9f285b060a45e04f2cc7c2199596202a", "TARGET@v11_hash=aa8439aa18c535e6aed91a78af85b368", "TARGET@v11_hash=baf5c0ac23ea78d8aef1a2f083481470", "TARGET@v11_hash=107cbd4983ff277b25e821805cf3cbba", "TARGET@v11_hash=c6041419811f629492b4d83686f9f17d", "TARGET@v11_hash=762a992f74ea24213ac479a68d4ed8bb", "TARGET@v11_hash=a102a7844a6339db8db207ecf194d92b", "TARGET@v11_hash=2f7f7156efd6c65bc4f443fb35df09d6", "TARGET@v11_hash=809ff1d2140fc7ce231cdeae773ca27b", "TARGET@v11_hash=c74e62fbd94b2b15b128de13922f52d4", "TARGET@v11_hash=17d61a3e5327e31b1c500c9726ee5c40", "TARGET@v11_hash=8b1372cf24e19be115013c242ab069e6", "TARGET@v11_hash=0632e60b05ec68dc69d9527527a9a356", "TARGET@v11_hash=e4e1306d7e1767f42c483a7b00e600af", "TARGET@v11_hash=abcbe5ea566a1827c07385e94efceeec", "TARGET@v11_hash=2c708c71141d3f7dee5f60073981d989", "TARGET@v11_hash=ac03f686fe8da601592dbb576c6a8ba9", "TARGET@v11_hash=22cd55b5f4704cfcdf4c1350d2ba0a6a", "TARGET@v11_hash=3f511cc1033c18e88b8395dd76de6aac", "TARGET@v11_hash=8e973e60a35e4720e3cc6e50202f9ffc", "TARGET@v11_hash=985f76c8455ac25076fdd2f39b1bf635", "TARGET@v11_hash=4db4fd8de426968734c305e5dbfc46e9", "TARGET@v11_hash=8a23486e498fb69927598db3ca7f2c61", "TARGET@v11_hash=0c23e1112e086efe2e693c73295500d2", "TARGET@v11_hash=c1b8fe557e1dfdb76ca1764770226082", "TARGET@v11_hash=a875a08d7b0f95465b5284fead7d6e3b", "TARGET@v11_hash=ea9821bbe1c1a702438bfcfef293b122", "TARGET@v11_hash=7b11ce61d0df41484893218f78b99b41", "TARGET@v11_hash=f3fd7ef7b32a4e38db4766d20198d33d", "TARGET@v11_hash=6ec5d9f6a1aeefc5292dc0cb7818de9f", "TARGET@v11_hash=b5a7ae64b2fe971e74e2076143d77979", "TARGET@v11_hash=d53b05c5b9f91c4ca0f27ede6bb59e00", "TARGET@v11_hash=7ee62cf58bb154b5b23fac22d055b0f1", "TARGET@v11_hash=749c7133f02f2c636362ffb792526f4f", "TARGET@v11_hash=044325827556e9babd3c24c1cfa2f3f2", "TARGET@v11_hash=fac96b2b7e814379db45e99b37ac7287", "TARGET@v11_hash=dce90385f5e0a7384b8c9f02e052e195", "TARGET@v11_hash=86f9d855fb3609365625be412a5584cf", "TARGET@v11_hash=f76f3b0b3517f0fd0b997eeb45daf1d2", "TARGET@v11_hash=8265fd51e17ad32a04640c6ed444e1af", "TARGET@v11_hash=5f280a35adc8bdbe8bf1040d32b05c65", "TARGET@v11_hash=d22f3dd5e3cde4ceedd5ec5783253c75", "TARGET@v11_hash=e7097e42b9155facaea653e30989d6c0", "TARGET@v11_hash=93e6096e024cd409aa48b39ac291bf11", "TARGET@v11_hash=12984e263f9dbbb28982773fbfa7f439", "TARGET@v11_hash=4f60098f3f58c64ce6a512cec5e1218f", "TARGET@v11_hash=a7829adef29bc089f7d45fe414922f04"]}