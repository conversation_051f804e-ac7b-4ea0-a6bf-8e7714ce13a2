{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a28a39e65619cc3d65963fb70172bebe", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e982f158e38a7733581acf5e46c2f60adaf", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989e2065456da4d2d1e256d0a1a676bf70", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e984100293a8ea0bb0acb354b1e9215b271", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989e2065456da4d2d1e256d0a1a676bf70", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98db94de783b2c2fe64aa4b95538b46f0a", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9817bd41d0928e42fec5156c321d648a56", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98927232859493f6fa7e2b2487ed7f7e36", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980686d118707d8299df6dffae4caacf4e", "guid": "bfdfe7dc352907fc980b868725387e98f67b797fe8b8da7a0cb3fd96a1154a44"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982712d4d18a0abba44d6ea55797cb438c", "guid": "bfdfe7dc352907fc980b868725387e9801ca594af7ffb94d010fe00cae578749"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812e7c5582b032c29e37836e747846dd3", "guid": "bfdfe7dc352907fc980b868725387e98360075975a07b89fdc52187357065d46"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98434f98cc6df61c36412b7959bfae8979", "guid": "bfdfe7dc352907fc980b868725387e9865bb57294772c136fc7bf8bce4741446"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b9956dc79d04129d7526696ad702ccd", "guid": "bfdfe7dc352907fc980b868725387e9802c35ad9f419782dfe769b9602b58562"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829dc91bf9d79a9a5ca676060a4c80943", "guid": "bfdfe7dc352907fc980b868725387e9803ca270362cca9eb47396f351d5bbec8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98defb89c798f3f4fcd48c228c7d30700f", "guid": "bfdfe7dc352907fc980b868725387e9879cf4e913d798818da8cd5748d6e4f3c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853fd2f3ddec47da4ee3861171e6d158b", "guid": "bfdfe7dc352907fc980b868725387e985df911f279009baac821468491de010e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894e1ee88f5d1a3ffb5fc88dd676c3583", "guid": "bfdfe7dc352907fc980b868725387e985fdf2368d2709225c4ec4754f285172d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c68d4ae273bbbf9df39f047b07f8501e", "guid": "bfdfe7dc352907fc980b868725387e98c7b55e9e61af1dbf7cbb0c9e1da2a311"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988abca06125c6256ace4b1a2d6d5cc28f", "guid": "bfdfe7dc352907fc980b868725387e98302871c65b33e36f3bd0950392487267"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817245996844d6ba6e8415689264c88ce", "guid": "bfdfe7dc352907fc980b868725387e9814f4f493ac56548fa64db3c60a391ee0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833b59933fcf795e203faac9787323996", "guid": "bfdfe7dc352907fc980b868725387e98bfde19055be41bdfdff6e786b10c7106"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987af1be29dda07e70a9b3f1b8b756d54c", "guid": "bfdfe7dc352907fc980b868725387e9852b150300c1a510bf8a017c054b030a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98284410daa27b4374b3afa4d747f38651", "guid": "bfdfe7dc352907fc980b868725387e98686633a54245e0a4a571207154c10b46"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bec840292f492792e3df7ccfef62820e", "guid": "bfdfe7dc352907fc980b868725387e9845965c93fff3a00da98ea510e061a6cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98625dd579dce9a80a7e934d4d4f655a13", "guid": "bfdfe7dc352907fc980b868725387e988c548f2107769c73d58946ac3c9ccda9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879f29c8e1e92119fb6c8566649dad700", "guid": "bfdfe7dc352907fc980b868725387e980851970920ddda75dd5d8aeac6cb496c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987eaf7ad54caf2489a113d0b28a2af2b7", "guid": "bfdfe7dc352907fc980b868725387e98f613c673c4354e14459338c729efce27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b290473a08df7525cec014a2d6bfde5", "guid": "bfdfe7dc352907fc980b868725387e981f5b2b0115be336cf3283308bfc60310"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e77b5175d30e9e3b91f28e1be62d9620", "guid": "bfdfe7dc352907fc980b868725387e986832dd33c7f8c9892277abf2105ab916"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890812322f167ede3018fd82109bc49f0", "guid": "bfdfe7dc352907fc980b868725387e9835524d1ea36b33645f0bf5f4b2dad9d5"}], "guid": "bfdfe7dc352907fc980b868725387e98d94ba4f5bc8e48d11aa66ab5d6013f2e", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ab5e1f747dfe477b655528b07584898d", "name": "DKImagePickerController.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}