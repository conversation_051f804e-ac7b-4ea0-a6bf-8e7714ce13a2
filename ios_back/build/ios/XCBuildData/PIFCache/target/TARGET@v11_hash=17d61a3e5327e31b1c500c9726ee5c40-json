{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9842b7a14b2573fbc760906ff84bdcd58f", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98519c0f1aabf34eacbf6f755b813f1496", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d72d473d926dcc5fd6b3904d0521980a", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec62137b0ee28ca4265944856877be27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d72d473d926dcc5fd6b3904d0521980a", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ebf16d88dad2729e444c32094aa46a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983b296a2dfc8bce568a15f27de6df6617", "guid": "bfdfe7dc352907fc980b868725387e98451271ac92a2a6b3bc220a26c9165552", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad70baac1fdfde0520be15dfd8951725", "guid": "bfdfe7dc352907fc980b868725387e98803fd33d35f2b4a487541d8ec4e94db2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987009678c468edb1cdc33a91cc97e041d", "guid": "bfdfe7dc352907fc980b868725387e98ca13dab421a323308e8ddc6cdb237977", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98478d5876a325f0a1a747c0cc01070596", "guid": "bfdfe7dc352907fc980b868725387e9855863e1daf52a474496c62b8e26b1623", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a5d1433335d5fdbc5eb28ea44f1159c", "guid": "bfdfe7dc352907fc980b868725387e98fbeb2ccbdccf43c8c39119e52627393e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c221452a6a7ba015516d4589e80778f", "guid": "bfdfe7dc352907fc980b868725387e98de1977ee82e83b08cdd262f30ab1e546", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1eacedad68cadca141a20f40b008225", "guid": "bfdfe7dc352907fc980b868725387e98f2ff79ac1b761f0f79e693ae36952994", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efb14b13c4f7d7d443b6d8b6aca5ad16", "guid": "bfdfe7dc352907fc980b868725387e98f1833bff7e312b1000761b520b229d5d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a1e60b34c75488d93c368800b3ae49c", "guid": "bfdfe7dc352907fc980b868725387e98b7a704666dd231f45c8c0c5039cf962c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98349d37f6ceba36d21404aa5476a03e01", "guid": "bfdfe7dc352907fc980b868725387e98d254ab7cac101db2e9608be9268e33df", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6d4b9ba8965e81675c9ef4a892dc82c", "guid": "bfdfe7dc352907fc980b868725387e9841995aad82cee4f0d1af9a0970f1d9fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98878e07060fdb018f08bac102bdcb1ecf", "guid": "bfdfe7dc352907fc980b868725387e98f6a6e186019a7b6bbcf7edf80d739ab3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983085667c3acefa2fc7d377dd781ea7a2", "guid": "bfdfe7dc352907fc980b868725387e98dc02dbc796bf1a1c79ffc8c7bf3cea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c8421b5423f4a92f639d51c8f2de93b", "guid": "bfdfe7dc352907fc980b868725387e9878bf6ea35872ff69d8716d14e8726fb7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd3f20096b6d4c0de429c9cb13962e55", "guid": "bfdfe7dc352907fc980b868725387e980929dec59f51f260fb14375cd3aee123", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebb8c37f58a04dad1ef32959af3e9719", "guid": "bfdfe7dc352907fc980b868725387e98978956dcfaf4dcaa425b470b0ab6591c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896092b178723e304351383f095b66579", "guid": "bfdfe7dc352907fc980b868725387e98d3e49fdf20728c8da29875785f93fa34", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847fc4f725bedf155863530de63fa7afb", "guid": "bfdfe7dc352907fc980b868725387e9875ed083cdffa950109d382aa9bb635a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f24162be53c74b3115977c2aaf17ab9", "guid": "bfdfe7dc352907fc980b868725387e98da892b09f0181aca06faf2f98c1b6abd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2dcc7208a6d49be07aa99d721383cab", "guid": "bfdfe7dc352907fc980b868725387e98e03749eb0dfd6a1bd972248fd565e7f5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b2bb87b22c7c3d2ec03e24a545370fb", "guid": "bfdfe7dc352907fc980b868725387e98ba32cbf0b2f954399f58199c72f84410", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982dd408288d880a4c9e3b890b90884499", "guid": "bfdfe7dc352907fc980b868725387e981ec4f2ee3baa2378c72056f6d51532bd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982beb92c50435da2039e3e5e711dff173", "guid": "bfdfe7dc352907fc980b868725387e981f1bb3e1716086026593817395ba776c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a03fa67e33d48dce94be2b8eb2259ec", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989da2e9599a4243f335210c7f3b26667c", "guid": "bfdfe7dc352907fc980b868725387e98c78b17d465f66fc46ff613f796c4b8e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c2bbbbd8d5561f19d85611a85b6c2b2", "guid": "bfdfe7dc352907fc980b868725387e98e2fdcfc26cf4ff69bfe0979351b649db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981872e0a9a654d158c8440721cc0f5a16", "guid": "bfdfe7dc352907fc980b868725387e98d5c7402eee4379664783cc3196df872b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4e02020ea0d9a6c8def9a2ab5235816", "guid": "bfdfe7dc352907fc980b868725387e98f2b049120e51de6572d77eb4fa27601b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fea24a01a6b63e71cce546aa3623917", "guid": "bfdfe7dc352907fc980b868725387e98470c0a98e19494a4f7a55af39256cbd0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831e567900ffc3ad0fd1255a8c584368c", "guid": "bfdfe7dc352907fc980b868725387e985e260f4f5b1601c288ba7f6d58a3836b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989061a636489e5c0fd818287f8bca0eda", "guid": "bfdfe7dc352907fc980b868725387e980a606703b1dc9d426d651976283c7afa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddf0b0677e6d7db15994fd0676f1f7e0", "guid": "bfdfe7dc352907fc980b868725387e98794fa1083299c389c3e06b1da4679eb4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ec0cbb70087ebaa4f94b498348251e8", "guid": "bfdfe7dc352907fc980b868725387e984f81bf588b7132779cfda37357f5988a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a72a4c429044dba44e5f026d07f89720", "guid": "bfdfe7dc352907fc980b868725387e983b380180ac8372c476a807b9287534c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98512737a5448e3159670bb8c28380d63b", "guid": "bfdfe7dc352907fc980b868725387e9826dbc66437fe08e5f660cd7d57a07ade"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf84bfb245b15a5a959c2c2c3d969805", "guid": "bfdfe7dc352907fc980b868725387e9848228779c89b7cf4c56f96d9771d2ce3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98817c5e5c879208c0e4041f9f7f7bacc5", "guid": "bfdfe7dc352907fc980b868725387e981850b344567e5543096f0a83d9eac0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98529b4b1638379cc629704f4c813c4a58", "guid": "bfdfe7dc352907fc980b868725387e98ff0bc9241cfa1703ee578d05d882114d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a927f633e95d4a09dbd6cdcdccf2bc6b", "guid": "bfdfe7dc352907fc980b868725387e98dc21c349147484be8818b0c698c008d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ebdaee286a04e9b5b9dd2f5aa42f416", "guid": "bfdfe7dc352907fc980b868725387e9854ae6e6291c1d1636792ade71a7bed6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865e703b35b245f8d38e232e1bb4aaa94", "guid": "bfdfe7dc352907fc980b868725387e98a76ab6623a0005b7a05de9a09b4bbe0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7bc60b4eec2bb95f69869e626f7d33e", "guid": "bfdfe7dc352907fc980b868725387e985233b06bb9e39497179a34ca3d93e52b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832734980920c93089c05f61f97f91ea8", "guid": "bfdfe7dc352907fc980b868725387e98bdf453b73e17a86d84100cf68d60e04b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d798bc8243b144be38334ee704d98b7", "guid": "bfdfe7dc352907fc980b868725387e98700a094c3bb1e173435375a335e28e84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ac7f00efe070fca7443de48c2318cb7", "guid": "bfdfe7dc352907fc980b868725387e981d3f412c09d65d91f50cc28e7385ca9f"}], "guid": "bfdfe7dc352907fc980b868725387e98be6229230a4715433df2f8e74fbafc5b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987e9cbf3be042958b470cf98aebd11b1b", "guid": "bfdfe7dc352907fc980b868725387e980797c2152e50219ee4196549bb34f857"}], "guid": "bfdfe7dc352907fc980b868725387e984d290968aff9eafa4ed5b85c80a8c610", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fa0d11ed0b4e1a85c13d68e37d1547e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}