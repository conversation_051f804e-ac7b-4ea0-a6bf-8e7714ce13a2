{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e5ae0f909404a135607be394e691b816", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/HydraAsync/HydraAsync-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/HydraAsync/HydraAsync-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/HydraAsync/HydraAsync.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Hydra", "PRODUCT_NAME": "Hydra", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9806c6b6029c5ac4d3c3582a4ed0134603", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c9f0b1b24a42ae697c483e7334edd6ed", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/HydraAsync/HydraAsync-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/HydraAsync/HydraAsync-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/HydraAsync/HydraAsync.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Hydra", "PRODUCT_NAME": "Hydra", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e51c294835e4bd680fc7e11c21b35fa9", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c9f0b1b24a42ae697c483e7334edd6ed", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/HydraAsync/HydraAsync-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/HydraAsync/HydraAsync-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/HydraAsync/HydraAsync.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Hydra", "PRODUCT_NAME": "Hydra", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989f4f26e3fc5651297512794d883c28fc", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987b6dfdecd6941887239a2b9b49c711b8", "guid": "bfdfe7dc352907fc980b868725387e98396f6653cf31f4d76b6e905c25061c66", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98c196b151006a656a964ab5e46f5f3aee", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983782c0980b6993c46f768d11d504e2f0", "guid": "bfdfe7dc352907fc980b868725387e98684035e4ea355f85d2ce8d31f363385e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821224594e6d9b1021fcb8b6728f116ff", "guid": "bfdfe7dc352907fc980b868725387e98fd16d57c27b1eabf50e1127dac248f6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98acf566e77c562a1126d23a393b685691", "guid": "bfdfe7dc352907fc980b868725387e98099041aadcb8f294d91191753f71f191"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98051e06e8e8dacf0ddc128800c6e14bb9", "guid": "bfdfe7dc352907fc980b868725387e98c9e2bfccc9d3168312d5399f50ad0189"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c40fbde14cf1783d3f9dece5ef967eff", "guid": "bfdfe7dc352907fc980b868725387e988618bac04edc65d700f12e4fef9996f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b7038bbec31b926690c344cde4a0abb", "guid": "bfdfe7dc352907fc980b868725387e98a52da68d8516919e8b56ad37a7585c33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9a9f902e2eb8aebbdbf0151bcf871b5", "guid": "bfdfe7dc352907fc980b868725387e982c6f683c11c783ea7c53c8ae9f01a9d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b7fbbeccc6a74734a7b296302965e2b", "guid": "bfdfe7dc352907fc980b868725387e9826576b4dd9473bcd5de7e38ff87cac48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c62d4fd97cce5fe123a3d70b5ffd7f72", "guid": "bfdfe7dc352907fc980b868725387e98417a795a29daa4ca9c7d5b2ff57dc77d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b34b7a5841e77ceb5e7d1ed18c34e9b", "guid": "bfdfe7dc352907fc980b868725387e98b2c0cb42cb72376425ca2039edf8674d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981dde3fee0abd865d6323696800b35f1b", "guid": "bfdfe7dc352907fc980b868725387e983bd059666059b28f7879cbc2abdc80e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ce219745e7152bcf03ac0bf59b8dce6", "guid": "bfdfe7dc352907fc980b868725387e98d120525366c3bfce54f642bbc73fb0e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864903d90612164b9138a9271f504857b", "guid": "bfdfe7dc352907fc980b868725387e98848ddb021e97391bbad139799c78c251"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b22e871921940fd51051ec8dd53e2b07", "guid": "bfdfe7dc352907fc980b868725387e98cfd9c8b20e785095fc127688014d495c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc23eb6f7c357ec340555430b4e128fb", "guid": "bfdfe7dc352907fc980b868725387e98f1493ec0eb6d58c0d721b6199c39a0ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985aa30e8be0560787b204752c16b76fe5", "guid": "bfdfe7dc352907fc980b868725387e9837f78fd17e1c370a7cfc02ee2fe62ea8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841109d36cc3b3745b04ab55bcb52f7d3", "guid": "bfdfe7dc352907fc980b868725387e9854c019db9b52e754fdfd9f8cdbd2ff73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d1a55e659c152d88df4e88309d19c13", "guid": "bfdfe7dc352907fc980b868725387e98e9c95d12b8d16254c4aab1a051d984b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbd9cb6f3e1473837cdd4b5b98d1bccf", "guid": "bfdfe7dc352907fc980b868725387e98581d39c3161c6c3e032d35d1b76a8619"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0ddef3517ad8035118fa25802d9e6be", "guid": "bfdfe7dc352907fc980b868725387e9882905c16e3c5bc5378f36931f9db88b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899d3b32f897161ae618e4c17bab883de", "guid": "bfdfe7dc352907fc980b868725387e982347108c81ad1ce5fe318c74c43cef2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc5544465ef05cadc29081a403913d0c", "guid": "bfdfe7dc352907fc980b868725387e98375972ea135116562a0694429b1fdec1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2fbff5d211ba5b2bdeace6ba16ec39c", "guid": "bfdfe7dc352907fc980b868725387e984b87eb81c95dda6b336e93fd3e1e61b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f38702fa4ce4960399ca0d9a05f7ff5", "guid": "bfdfe7dc352907fc980b868725387e9810c73ec6b5a6079ff7c31a0f3e587829"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ecc485da2d18b0a7d5ddf9853036286", "guid": "bfdfe7dc352907fc980b868725387e98d8bfd5dbcfbd0459ab80b44c50668754"}], "guid": "bfdfe7dc352907fc980b868725387e9811301d7d9c817a1ed5b418f942ed4f4a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987e9cbf3be042958b470cf98aebd11b1b", "guid": "bfdfe7dc352907fc980b868725387e98ceea6fa24e98c6f64e4280c8e3fa9f34"}], "guid": "bfdfe7dc352907fc980b868725387e989edb21284df659215822536b08cf133e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9823fed06f1349f8d23a81b328256b280a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98df71c66ecc82a9796c50f296fb0a215b", "name": "HydraAsync", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9874406eff01050d7992f58c088679c282", "name": "Hydra.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}