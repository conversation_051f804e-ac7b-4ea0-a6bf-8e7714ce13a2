{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c050137a3ef9aacfe710c7906dee7317", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/record_darwin/record_darwin-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/record_darwin/record_darwin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/record_darwin/record_darwin.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "record_darwin", "PRODUCT_NAME": "record_darwin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984742d4a4b4fa6227c6febec58f57645f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ee24dcbc18d512e0554511d7af53f15d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/record_darwin/record_darwin-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/record_darwin/record_darwin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/record_darwin/record_darwin.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "record_darwin", "PRODUCT_NAME": "record_darwin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98953c15c103c5a6f08a87f57d91333f36", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ee24dcbc18d512e0554511d7af53f15d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/record_darwin/record_darwin-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/record_darwin/record_darwin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/record_darwin/record_darwin.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "record_darwin", "PRODUCT_NAME": "record_darwin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987ffd6b4c1bb0615bbf27ebe98eaee9e7", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98af5ddcafe345ae2eba9392276b42fab7", "guid": "bfdfe7dc352907fc980b868725387e98f47c6416b0edafcc3443a07d372e5e3d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b54265a9f4546471f9e1087caa24b81e", "guid": "bfdfe7dc352907fc980b868725387e989d5c7081587327fd33ef75ab287fab92", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98a621a7d316ab35d7db9711a19fa2fb5d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9827df993835fbc3c9a0ea03011c421766", "guid": "bfdfe7dc352907fc980b868725387e98bd53b965adbdaa0c0658b7e1590c6adc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7f8c99ff1c887a0d01a6e102bd4b410", "guid": "bfdfe7dc352907fc980b868725387e9825d3f2f17a67c1795261b1deaa7fe83e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de1895447f4a395994b21a7ee15092a3", "guid": "bfdfe7dc352907fc980b868725387e98670324dfb901104f10fb5b07583f20de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d58fab2913d9ff2103fd95d67fb1e38d", "guid": "bfdfe7dc352907fc980b868725387e98e55da23af5d6dd73295b4965099fc94d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983644dec29ae7200caab249b054049980", "guid": "bfdfe7dc352907fc980b868725387e9853a30fb5a7a6010767301b9fd9da307f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98170299928c1ac3fb0c405e5ac85fa084", "guid": "bfdfe7dc352907fc980b868725387e98e1d5c7a7f49d2e94af9bf7b5fddf3524"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e16183f2a797fcf22da6f88cf67cdb93", "guid": "bfdfe7dc352907fc980b868725387e988881440979f768a12d90dd716f18c300"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b70c32b5d6d704f101c7050e1c95fca2", "guid": "bfdfe7dc352907fc980b868725387e983041519e970b895c7a46cb79a266ca3c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987043a5dd2ec4f77fc3d08203c0ee2940", "guid": "bfdfe7dc352907fc980b868725387e988c410af1b5623c0c1e3bf999ea74226b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e2d5410e616f6829e525c47b3f744ac", "guid": "bfdfe7dc352907fc980b868725387e986cb7d3400f49888b998ca5982eea1663"}], "guid": "bfdfe7dc352907fc980b868725387e9849374404305581dfa72e93f20c02c2da", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987e9cbf3be042958b470cf98aebd11b1b", "guid": "bfdfe7dc352907fc980b868725387e986bcc36a24f802feef30b7de8cc22a23c"}], "guid": "bfdfe7dc352907fc980b868725387e980a749b1f4912e24ec6d77a7fd1cac954", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9813eda3edf215c23d2691b33f628d3f55", "targetReference": "bfdfe7dc352907fc980b868725387e98961ac31429dce116b237588bca051584"}], "guid": "bfdfe7dc352907fc980b868725387e988a55debbae7d50703eb6eda61494c8f1", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98961ac31429dce116b237588bca051584", "name": "record_darwin-record_darwin_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98059c98c6543efa4356d02e5ebc0f7cd7", "name": "record_darwin", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8a7afa009aceb825a823b6fa0c4fae4", "name": "record_darwin.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}