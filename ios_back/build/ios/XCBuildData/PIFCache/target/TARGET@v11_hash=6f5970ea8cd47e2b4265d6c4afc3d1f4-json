{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984c506e4c7c5bfdb77021de62ba724fee", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cd57595edc694abe6f49105bdeb41711", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9845469c5f81bc9942499591b0946fc69b", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988a9585779c507441a82efcaca8389699", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9845469c5f81bc9942499591b0946fc69b", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983dec3b9bf76f4ff5422ee9af0f7acb80", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f76ae245e8109d578585488529d425be", "guid": "bfdfe7dc352907fc980b868725387e98cb06b65220a256128cafbc48ad42592b", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e981f4993abeabde2bc4fb30a52ba3ed039", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980d0747fe23a2c1e098e58369d38b7ffd", "guid": "bfdfe7dc352907fc980b868725387e98f88c90e09517b3b2070de9d8a378e1d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5030770a7aac1faa187dea29d79c8e9", "guid": "bfdfe7dc352907fc980b868725387e98d9bdd92801871c088555e49436901614"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a1447db3abd2d533bc817c3e35a2d62", "guid": "bfdfe7dc352907fc980b868725387e98b6d827545fd78d62c578d2e47e00f560"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f7902bec966b826ed6d8e2790bd1b4e", "guid": "bfdfe7dc352907fc980b868725387e98421afb54eecdab1e342c9e8d90870bc9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3a0f8e362e7df87a615afefa17bfe3b", "guid": "bfdfe7dc352907fc980b868725387e985645ff75cd99e81e43dc4b3aff0e2bfe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cafea1e201ff53c058f0ad74a95afdd", "guid": "bfdfe7dc352907fc980b868725387e9866102a85156baf904a0ac66de6ace4b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c00effa8742a8906534c83d8e3b769d", "guid": "bfdfe7dc352907fc980b868725387e980c4765fea64e28ce954ccbbb8e85ccef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e88fe61036c45dcb445c291e640fec1", "guid": "bfdfe7dc352907fc980b868725387e988ad5ca9b7915c1d54ad2ef9c5fe217bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a53e4689fe9dc1e06227c89531763365", "guid": "bfdfe7dc352907fc980b868725387e98a5ffab24a69cb05e2afa1bbe071347c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987173fb2adca07a52c734af332974e094", "guid": "bfdfe7dc352907fc980b868725387e98c2baefc3cabecbe345cb82a30a324d51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb1a8d96caead5a6391486ba59e717b8", "guid": "bfdfe7dc352907fc980b868725387e98dd26237ff12768119f1197a142bddb21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98416c6078290ce2de93e5de0aad66a94c", "guid": "bfdfe7dc352907fc980b868725387e984544bd26ad1c0b4f970d47f009b02109"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d12067c046d61354a7914b322457b61", "guid": "bfdfe7dc352907fc980b868725387e984149af23c7fea99f0771a472a15e50bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842db70d6fae285fc3bccc6608e07c0c2", "guid": "bfdfe7dc352907fc980b868725387e98f274bed9990702724f4de2a56e5c6e21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804b73121fa3310ca43e7246918a20fd0", "guid": "bfdfe7dc352907fc980b868725387e987b514f8b6931813f4065553500ff73af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb30b063209e24d73fbad6734dde6ddb", "guid": "bfdfe7dc352907fc980b868725387e980737610d98b2829dbc5774e855450eba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df6ec6541ca56619166052eb4997ff19", "guid": "bfdfe7dc352907fc980b868725387e986701f979adc3636fa312052473591163"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889fbd2aa0f10750b2126a4e4723cb0fe", "guid": "bfdfe7dc352907fc980b868725387e9826efbd8822da6116c996379dc802679a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808dacdcad49ad172f0858ad969c8d8f0", "guid": "bfdfe7dc352907fc980b868725387e98db7ed13857955f6059bd0f2176a2e1ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec29a4232687f0d31efef515709604a0", "guid": "bfdfe7dc352907fc980b868725387e9824f4139890d7241872c0b1cd0c23e63a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989834f4544b3146192f2de6c6e1920e31", "guid": "bfdfe7dc352907fc980b868725387e982cf2ea969e1ae7db7029161e46d9ae86"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f3fb757dc23d9132e7d79c3ce036069", "guid": "bfdfe7dc352907fc980b868725387e9850867d69dd52f2a1d8ea15ee7802fbbf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bbd92f448797fcbaebad68bcf2079a9", "guid": "bfdfe7dc352907fc980b868725387e98ceb93501b49f5ad9017be03b5ab5085c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7d8b7b7415619511c4e2fb44a9d7a2c", "guid": "bfdfe7dc352907fc980b868725387e982a84a1f40b0145e7d3c0b20e916725b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a12abbf0a88f77db3bd24a367242ef26", "guid": "bfdfe7dc352907fc980b868725387e98ed0a2537dec7a9438672571136e0f72e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de30a7bda31af7ec4ecc6a946981c1c5", "guid": "bfdfe7dc352907fc980b868725387e98435afcb876dfcaee6f6bdbaa9c510722"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f127f4273e5dc6354229323f0810af8", "guid": "bfdfe7dc352907fc980b868725387e986db72093f73f8378a0ab2382d124b177"}], "guid": "bfdfe7dc352907fc980b868725387e98f4c2a80e143f481d4e8e7d2f99177e6f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b32d5f9da18547a5dda681aff174df7a", "guid": "bfdfe7dc352907fc980b868725387e9858dce0c92864c061c8b6dc8c7d1d327b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841e20065171a32ae1bef9c372ce4df16", "guid": "bfdfe7dc352907fc980b868725387e98548b793a416585787ae77e877f004f2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e9cbf3be042958b470cf98aebd11b1b", "guid": "bfdfe7dc352907fc980b868725387e98608ce6afa88980abb28a7fa2f396a445"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fedf9725cd154a83c38e230bc7083152", "guid": "bfdfe7dc352907fc980b868725387e98a43b4bffbfdadbb985f5612751ae5793"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bdbb9256ad1e1b8554abcf310b1bb320", "guid": "bfdfe7dc352907fc980b868725387e98a651b1601d2cb6359d86df112a8a4781"}], "guid": "bfdfe7dc352907fc980b868725387e98ce937115c4ad446c647907fbe811afdb", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98ab689f6136992dd5d04a857e130f540b", "targetReference": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937"}], "guid": "bfdfe7dc352907fc980b868725387e9861d53653266aaff4f590a01a0e3a7310", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e90c628ccd44af657bee5ff4af2f692", "name": "DKPhotoGallery.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}