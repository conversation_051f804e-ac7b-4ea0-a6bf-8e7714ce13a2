{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981a65912c3d6a68c3c00829e1b147aeb0", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/url_launcher_ios", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "IBSC_MODULE": "url_launcher_ios", "INFOPLIST_FILE": "Target Support Files/url_launcher_ios/ResourceBundle-url_launcher_ios_privacy-url_launcher_ios-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "url_launcher_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e987ba8bfb0a0ffdcea5a7a661712a8f465", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983ca321d994d54e60f14db99823557d9c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/url_launcher_ios", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "IBSC_MODULE": "url_launcher_ios", "INFOPLIST_FILE": "Target Support Files/url_launcher_ios/ResourceBundle-url_launcher_ios_privacy-url_launcher_ios-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "url_launcher_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e983ee947c75ff541123cb3219e2c3ad839", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983ca321d994d54e60f14db99823557d9c", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/url_launcher_ios", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "IBSC_MODULE": "url_launcher_ios", "INFOPLIST_FILE": "Target Support Files/url_launcher_ios/ResourceBundle-url_launcher_ios_privacy-url_launcher_ios-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "url_launcher_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e986dda9498b42671023f320c598fd97313", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98cc91928344d9d9189dd179ed82476dd1", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98f49613d34984626d9302892bdf221845", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981913817a5524fb1243580015ee214236", "guid": "bfdfe7dc352907fc980b868725387e98ee0a57c40b78bad80bd8519f3f87157c"}], "guid": "bfdfe7dc352907fc980b868725387e98eef2c88cfc8cdad5d74e159641f75df2", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9891b3b8cc56823cdea4b418e009a423b2", "name": "url_launcher_ios-url_launcher_ios_privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9827df8da513ac7d6928fc311b53a7155d", "name": "url_launcher_ios_privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}