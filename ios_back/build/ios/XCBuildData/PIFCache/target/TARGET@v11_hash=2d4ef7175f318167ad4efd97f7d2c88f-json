{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c9bece455b0327e6e82431550587b5f4", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980fe6008bd0ea7d60a239f5cfb496f9fd", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98529d8744adfac39fb72499c2e61ebbd9", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984ac27f284fbf5fa4242b11e4c252631e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98529d8744adfac39fb72499c2e61ebbd9", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec29c327167a123c1352766ac15557e9", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98334f4fa960400ce2b9eb6b65882c72a3", "guid": "bfdfe7dc352907fc980b868725387e981bf48faa656201eef480611e59eb3a2a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98706aa1a4330d29c67781ec2841f1a6ff", "guid": "bfdfe7dc352907fc980b868725387e98fdd27cb7aa2ff932fb550d827b753be8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888fa23de262b3bdb7f777b2785d58d7c", "guid": "bfdfe7dc352907fc980b868725387e98d347ff1896ea91fc8853c8ad35ca3d56", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98092f6e2f6efb38e285f90b0cdfd53bac", "guid": "bfdfe7dc352907fc980b868725387e98df995503faf86a7e536e8fd0813b9b8f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3568904b30a164cb8875affdd705d19", "guid": "bfdfe7dc352907fc980b868725387e9856cc3db4c1cedb1aff6f94ec3c3848bd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98243aa128eeefb7079516f306320236d3", "guid": "bfdfe7dc352907fc980b868725387e983ba843baf040ed839e3cc7055a422157", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846cb8f66ac0661744c82d7689309c2cb", "guid": "bfdfe7dc352907fc980b868725387e98f12997f7a870a333b6a59f518794da60", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98459c0fe6881d5408abae57f7b5f1570e", "guid": "bfdfe7dc352907fc980b868725387e9878d7d41c26c5172fd6bae055864d357a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98172e78f7bf5b9f6b79ac877ba57fcd1a", "guid": "bfdfe7dc352907fc980b868725387e98f97c4a065c6202e2c14d251501219d22", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987417081fef67404e63381a124c0de673", "guid": "bfdfe7dc352907fc980b868725387e9888bdffdd6f31cd91a8d0ac402845d65d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98deb52748be64193c80ed77fee0270ffa", "guid": "bfdfe7dc352907fc980b868725387e9887f7b6ef48daddf72653e043318fcd87", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98299295309d9104e46ff2d1c645f08cf6", "guid": "bfdfe7dc352907fc980b868725387e98b1b24115f324e7420a948ad7b13991d3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bae6b269d836750f9c9b5926ba1753a", "guid": "bfdfe7dc352907fc980b868725387e98d5f711cc78f72e2971054f8eb97a4381", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e5b02eb7ad779f596bdcb625eaf2304", "guid": "bfdfe7dc352907fc980b868725387e98d0a3f5212920b993721dd11970c2929b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0620bd4227bbf82c22daab1653c4233", "guid": "bfdfe7dc352907fc980b868725387e98c4abf69a427b75bf3ab0f9d36a1b0640", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834807d513ff64c794fdfd0510d510488", "guid": "bfdfe7dc352907fc980b868725387e98fe21f8dfcedf14c74b19e76e31cde76f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98995f9ab6e5ff99189bde982a73e71fdc", "guid": "bfdfe7dc352907fc980b868725387e98ea71d5e274ee87ec9a228720a1724d19", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831c3438d0575f73e493b7c7cd55c9c03", "guid": "bfdfe7dc352907fc980b868725387e9867d87f36a3301fa6f4f937204c45b32f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c036f6dc9be82a28469813294d6e0ce", "guid": "bfdfe7dc352907fc980b868725387e98136b0224c7c5db6bfadc15cb24630c89", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c336fd052a28a3428621a1f0f3a79e66", "guid": "bfdfe7dc352907fc980b868725387e98c33b10033f253170e76599357bda808e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b83d78bdfe41af884eba90fead59776a", "guid": "bfdfe7dc352907fc980b868725387e98c9bac7e9947ff23384a6a7d7ed7f8ada", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e21e90b2914859e2afad6c2929f99c9a", "guid": "bfdfe7dc352907fc980b868725387e981b90dbbfb40befae32f3ead9a8e875e6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a599b088437830fe082b5bb2c4f199b2", "guid": "bfdfe7dc352907fc980b868725387e9892497edade3bc639b6a4618b08b33e52", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987411a80ddf50a516c3d34535cc0a4418", "guid": "bfdfe7dc352907fc980b868725387e985cf5d042b1374261be80fed767f98ff9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830630fca22762ab96f90b32f34c23f17", "guid": "bfdfe7dc352907fc980b868725387e9806810528f800356228f004f6af12e21f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb30c4984346685dadcf191f286f7baf", "guid": "bfdfe7dc352907fc980b868725387e98831ebff69c2d2935fb996fd708ea9689", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834f222bcca437a79edb4526c50c062f8", "guid": "bfdfe7dc352907fc980b868725387e9897c3233a7f1c0a6ae55fcdf195661e1a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825f0585f3bdd2ebb0a1b40a0d39e16c8", "guid": "bfdfe7dc352907fc980b868725387e98b4408037b3fa895c20f523adfb302339", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98934cef9bf6ee848bfe3ec38d62197835", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9847fe96ff2ea497c8642140f9e5a6c6b0", "guid": "bfdfe7dc352907fc980b868725387e98045e910dfd84d7af6006fdfa6bd299b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b02dae0fe7de4d5ba534872ce75177b9", "guid": "bfdfe7dc352907fc980b868725387e9817356d875cf65945c32eb6f28cfa0d99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899fcf7957a9cafb89d79123b011356d6", "guid": "bfdfe7dc352907fc980b868725387e989caa5d67cf983e47107e73307fc5ee73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddcceba7be5886691283edfdc7b21521", "guid": "bfdfe7dc352907fc980b868725387e9816670788bc7f1e372bfade4c15b03b8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986029fb4bff00faa0249e136bca0c710f", "guid": "bfdfe7dc352907fc980b868725387e987e970ec9abe4009811848ddc29fb01bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b81819434d0142e4b06802e9bae0f873", "guid": "bfdfe7dc352907fc980b868725387e98dceb4c49484f2671ec080703924df404"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d7919e6b149bd00798d0aa8934f5a7f", "guid": "bfdfe7dc352907fc980b868725387e98c466c0865302eac370f2c97020c16aa5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd5cda514001a8b8cc2c8d5b123b7096", "guid": "bfdfe7dc352907fc980b868725387e9834243d8349663fa77a6d342a4d92935f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d3f660b20c0093df5e87c7558f1929f", "guid": "bfdfe7dc352907fc980b868725387e988bb4e555fc68d5a5a5066c00515df22f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc8c170e2ec9caea36de6a6e2b2972f4", "guid": "bfdfe7dc352907fc980b868725387e987b030e730b023619d66e63dd4734ed0e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fb02335b88c27386d7b224718272ce2", "guid": "bfdfe7dc352907fc980b868725387e98f67842ddd0e3d65a93cfb5c92d37c23c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986de39c62ecbcc9540df0bb2aad48664c", "guid": "bfdfe7dc352907fc980b868725387e98e6cb7be20ed4ebf1fff7fcd92276f2e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f873c5ceec6328c39131e10dfddbdb54", "guid": "bfdfe7dc352907fc980b868725387e987bd9212cdbb17ab3307bb09ed894473a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815528084c57184c27a06cff20e5c1c48", "guid": "bfdfe7dc352907fc980b868725387e989680c433d96edbd3e9ed02093c1e0051"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98018031da29bfe2fd028fbc77b2c205a0", "guid": "bfdfe7dc352907fc980b868725387e98191014e424052c31d9044b893043d9a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5b82f74982377ed6729a36abd777f58", "guid": "bfdfe7dc352907fc980b868725387e98748fc9318a8cbe24b312075def129bba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccd0a14193f4b78cfa332de0ffda49fe", "guid": "bfdfe7dc352907fc980b868725387e982dc7aab6ab58faabd390c6451a754485"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98813834327218e70b0ad466f6c1f893a5", "guid": "bfdfe7dc352907fc980b868725387e982d6f31695a582e2137301eea9a8413a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802c4cdd43fc74abb8763c9363e54cd98", "guid": "bfdfe7dc352907fc980b868725387e982ab5a2f9dc3364e6600577311c111115"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9ae972ef42d62a1bc97ed4037fd4c44", "guid": "bfdfe7dc352907fc980b868725387e988465e247d8db0f099685317e1df6282e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98839bc60a9949bf37c49443b6b9f18971", "guid": "bfdfe7dc352907fc980b868725387e9849a6323f210cb5f6c435891085f4fd86"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b22f9aa6277d6c39ee78d4aff3980470", "guid": "bfdfe7dc352907fc980b868725387e9853383f8454d9ec5519b2cd63300dfae2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d471d77d22211d7b1378b9fe0b2e54b", "guid": "bfdfe7dc352907fc980b868725387e988b37fa95e02c2e467d2a17aa4b6a8832"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c9c785bc7be1ed6300b00832e3d1aa8", "guid": "bfdfe7dc352907fc980b868725387e98cd855ffb1ee1def52781566b1fce3ab0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ded8f9134353f07cca4233b7ab28ea7", "guid": "bfdfe7dc352907fc980b868725387e98e7f05e4c3fdf661b41cc7781c9417b35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f049affc2c28b7c2f7b1b136b1176796", "guid": "bfdfe7dc352907fc980b868725387e987d77f379968dbec04e0ebf45e5d48d3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac1d2b71f024e0474f2e313dc153e73e", "guid": "bfdfe7dc352907fc980b868725387e98d3688f74a1b6430a0166041cf36dad59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c8f8e8534f1748682a58b5c0f2644e7", "guid": "bfdfe7dc352907fc980b868725387e983565442fe26993b3b5cf3e2964ddc606"}], "guid": "bfdfe7dc352907fc980b868725387e981f18c3bdd543f0383a05196ffefabc9e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987e9cbf3be042958b470cf98aebd11b1b", "guid": "bfdfe7dc352907fc980b868725387e9808188f8afd5c22436d8f270ee53ec170"}], "guid": "bfdfe7dc352907fc980b868725387e98f59cae5e753b0a31876694ce85c47944", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98ec476f90f1f6e616e836f5cbf5a8826f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e982742fb9f3ddc293e43c65bab714408c6", "name": "Mantle"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9837027570fe3a09bb9c7d0d2620332306", "name": "SDWebImageWebPCoder"}], "guid": "bfdfe7dc352907fc980b868725387e982ec175b6b4d6149d1cce89f5f0b3694a", "name": "flutter_image_compress_common", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b928db338a2b0bf59a36f34e391aa676", "name": "flutter_image_compress_common.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}