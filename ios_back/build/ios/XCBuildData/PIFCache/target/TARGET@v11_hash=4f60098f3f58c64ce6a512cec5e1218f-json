{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a17dc2eb3498f2047cf5a68a69901297", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980d3926596ad3cd9c175f9e28b372b54b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b8bbd36bcc2aac4df71ed3bd233350ac", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98288f36904c064e8ffee3307684f0adf4", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b8bbd36bcc2aac4df71ed3bd233350ac", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984ad21424d04da79e5b6b09fe13bb9d64", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985261dfb09c3c1e9c9d64c703300829f2", "guid": "bfdfe7dc352907fc980b868725387e98fdd60d0f664135d2bcbe45feaa2bdb28", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bca4b8bf31df731a60f8fd22787f620a", "guid": "bfdfe7dc352907fc980b868725387e982b43eb4dc4862317b942fbc5ed879d2d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bd7e4c8d46b032268f0d9f0645d2022", "guid": "bfdfe7dc352907fc980b868725387e9859cfa3b771f3a5a81ac0e192651c59ab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1da6793a42159c415981423d6a6b858", "guid": "bfdfe7dc352907fc980b868725387e987654fdc4242c10774aea5ca6fba0cea3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860b9238e1296c81674a726228bd55058", "guid": "bfdfe7dc352907fc980b868725387e987517fd384f0f14c5edb189fea17fac74", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac0b14c978ea8aa287c93fdd56ced0bb", "guid": "bfdfe7dc352907fc980b868725387e98a5557fae9f5b893e1e3519a66f40f44a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846d08031b2a6c3859adbdac1d6b4722b", "guid": "bfdfe7dc352907fc980b868725387e98066a477d78922083e84e89a060893a0e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0450d4e3b67081e949853d515653b37", "guid": "bfdfe7dc352907fc980b868725387e982ee6eccbd7530e9dfbe6af31fab74ac3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bee58d39bcfa5ff2a78ee7df85edead", "guid": "bfdfe7dc352907fc980b868725387e98632a13842362049e00d756506a50fb54", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866c98a1b4cf58f8e26ab75b90cf7daca", "guid": "bfdfe7dc352907fc980b868725387e9869472e4abf613663b3a8a3bcc618fae4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98696364c0782ff1c3e480d7a4947a5ce7", "guid": "bfdfe7dc352907fc980b868725387e983feee9c6cf8f1c241bfa04f74a24e8a4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803e7c989172096a0e24cea6a899f8f29", "guid": "bfdfe7dc352907fc980b868725387e98f1eb8e0924859f0b3a3062e258d912d2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e377e2b0f7ef2da3a6a4134a550e9827", "guid": "bfdfe7dc352907fc980b868725387e988f60df2ce0d0ed0c54b7c6dd1afeaf80", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985805df3e15595d16b8435e88ba1a1aba", "guid": "bfdfe7dc352907fc980b868725387e988ea922b2c2aa3866c17dec1b6e547764", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981933cde9414b7608f3aa75716a011f01", "guid": "bfdfe7dc352907fc980b868725387e98000059bb5117743e983e30734692f79e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f649da5d6316dd402d60a4695469f12", "guid": "bfdfe7dc352907fc980b868725387e98e3a2e43c3cd650db9d251daf669c5bd0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989822193cdd2093fb3a211621ac854ff9", "guid": "bfdfe7dc352907fc980b868725387e981943d8276f2c0cd571bd0f0ce70b3ec8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edc8769740bfd449f261d5f0396929c4", "guid": "bfdfe7dc352907fc980b868725387e98f425f3d083fc7b072230cc77d4a54d7b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3e33dd60b7918c785c1781488b32210", "guid": "bfdfe7dc352907fc980b868725387e9864cca987cf38a33a5d1871f0a28f03c4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e343d2319b609eb889a9368a571d842b", "guid": "bfdfe7dc352907fc980b868725387e98b00290961bb0a7339eb1ffaf264dd1e4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854bef40ce6966fca939c41c570f28867", "guid": "bfdfe7dc352907fc980b868725387e983203289e9d016f9f4b4325037a4e71ab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee9ef21fe46a8761d4d0888431d2226b", "guid": "bfdfe7dc352907fc980b868725387e9855b4dc629f946d8ab71d8bb88982beb2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a79942a7eaa7a39f8ca20bdea98cfec8", "guid": "bfdfe7dc352907fc980b868725387e987fa99523622033a86d3abce5d5794ecb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857de5e4ae232a87f9a9efaaf85c7e898", "guid": "bfdfe7dc352907fc980b868725387e98092782bef84f333a33de27f5d57ab915", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e981664dfc7329363764fdacd5d07a0c8e8", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f01b0107474bbdb9d9e3849730c0c346", "guid": "bfdfe7dc352907fc980b868725387e98dbd847e2ace5139b27466c62715e4015"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98650585101292d84a5af6e7c05a3b1eeb", "guid": "bfdfe7dc352907fc980b868725387e98bfc970a823ac21b98354b9b1571f5b81"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1d8c880f59850486befbc6d1ea596c9", "guid": "bfdfe7dc352907fc980b868725387e981b3006abe64ebbdbdc23d341fd2013da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894153c47e44b06e7d1321c18c58ebbe5", "guid": "bfdfe7dc352907fc980b868725387e98272b43eb182ba6782418f847be95f0fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2a69492be173aaccc5e40071de6a0a0", "guid": "bfdfe7dc352907fc980b868725387e983aace035b3631636e452bb8e343ff745"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893c63ee9aab708dd69a3ab55582a3c36", "guid": "bfdfe7dc352907fc980b868725387e98b50904c074e225db0463ca5242265375"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f287cf392156bfef0ab439be8314ebba", "guid": "bfdfe7dc352907fc980b868725387e983578e6fbe67d4763f12faeffbb718ef9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4adf1b76e362228f4a05ad97d3a4426", "guid": "bfdfe7dc352907fc980b868725387e9879fdfe5431355aa38cbbf8c0a1728460"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985689c7d7b7af7ef8463ef619855aa13a", "guid": "bfdfe7dc352907fc980b868725387e985aba136aa668a7271f66a1d090630b2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1e476297c1e62693fab8559f3fec2ba", "guid": "bfdfe7dc352907fc980b868725387e984ddfdb2045dff1c4569b679d3d09b617"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cefedbbfce9c7bc9ee4314a30c53440b", "guid": "bfdfe7dc352907fc980b868725387e986c54e9f3f65fae4237ba44418a78ba71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef14fa21b4a4ddb3c300dd682e669f7b", "guid": "bfdfe7dc352907fc980b868725387e98217f80265a58ebf10371bd168c837bcf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a49fbfbd6445e06e997fe767b4518f09", "guid": "bfdfe7dc352907fc980b868725387e98db1a716f5e2aa7c1bec0409ec1930def"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ddcba485d08db4ba8e64f1d87cffbda", "guid": "bfdfe7dc352907fc980b868725387e980b3e727958a9ccc9b974efb86c0e3bd2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898dda593f9c9d7d0b0ba05961ce8cc21", "guid": "bfdfe7dc352907fc980b868725387e9817e7142a8b3397a7b2a9cfbea5a02704"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2b04c59b3ea272b9bfe155f4b785d37", "guid": "bfdfe7dc352907fc980b868725387e987f1e452434ea0e0cb927e5702cf445ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988806c9d5b66984174527567ec8e9ce6b", "guid": "bfdfe7dc352907fc980b868725387e98389774e7604c87c9d631500cbd5db1d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc00274b56435c2e49acc11e64911a75", "guid": "bfdfe7dc352907fc980b868725387e98401a2d2c59277fb92bf9bdc2e46482c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980250c1b222b1157aa1c7167b4fdc4263", "guid": "bfdfe7dc352907fc980b868725387e985fcdd0ec44f159f1c7f3d05a03d8ec33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb7395fe46e4575b771c4a80534445ae", "guid": "bfdfe7dc352907fc980b868725387e98a3cdda96a3819ebfbb6ad48c5b5732d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bab3a7d9b12924812f10d74eeac10e2f", "guid": "bfdfe7dc352907fc980b868725387e980c249dad96a03a034f9e80900ec21d6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c49dd5b65b1ef1b3052ba143cebc919", "guid": "bfdfe7dc352907fc980b868725387e98e7887e9cf16c104235c47ade5443867f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a471fd95c5cf01c7032d99c44325c0ea", "guid": "bfdfe7dc352907fc980b868725387e9830d50d3b8dada408fbd5ac3ca1c576d1"}], "guid": "bfdfe7dc352907fc980b868725387e98339f3c5c18cf261c37731f1ff9cd3c6b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987e9cbf3be042958b470cf98aebd11b1b", "guid": "bfdfe7dc352907fc980b868725387e98f76f428393514513ee220ee3cd45b4b7"}], "guid": "bfdfe7dc352907fc980b868725387e98cf9e1d75d6fd474380329cbf47d24d45", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98911b19d9ae38fc82e6a73bfd4cf29ba7", "targetReference": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1"}], "guid": "bfdfe7dc352907fc980b868725387e98f5ee52bb1ad32275d40a0a37ff2d7e9c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1", "name": "webview_flutter_wkwebview-webview_flutter_wkwebview_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "webview_flutter_wkwebview.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}