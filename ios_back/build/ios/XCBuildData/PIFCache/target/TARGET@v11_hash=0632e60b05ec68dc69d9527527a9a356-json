{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a73c5cd0aa0640824cca6909848b8887", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/photo_manager/photo_manager-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/photo_manager/photo_manager-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/photo_manager/photo_manager.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "photo_manager", "PRODUCT_NAME": "photo_manager", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984dabebdf6f54c881cf6ce3f503dbafc8", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983f4fad178154e66376af7e1e74569e11", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/photo_manager/photo_manager-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/photo_manager/photo_manager-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/photo_manager/photo_manager.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "photo_manager", "PRODUCT_NAME": "photo_manager", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9879370f9b6762ae3b167643fc360811ad", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983f4fad178154e66376af7e1e74569e11", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/photo_manager/photo_manager-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/photo_manager/photo_manager-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/photo_manager/photo_manager.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "photo_manager", "PRODUCT_NAME": "photo_manager", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e020dca05c43366d73fdccc591333ac8", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f242fbe293727019b27c6b2db29167b4", "guid": "bfdfe7dc352907fc980b868725387e9878710cd728c92068bd8e69683a9c4330", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dadefe572184bccaee4ecac939311618", "guid": "bfdfe7dc352907fc980b868725387e988c97be3801e5081bd607f4fa74df6ff2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bc33691d1d7f3922395a96e293bd081", "guid": "bfdfe7dc352907fc980b868725387e9871eb32e10615aaf9019c23144b7f95a2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ba2265d4bf9b14189dfdf105fa8c8ad", "guid": "bfdfe7dc352907fc980b868725387e98a7e308ae10c78458549e5259961936a4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cd4f23c3943575cd9a82b07cb21b9fc", "guid": "bfdfe7dc352907fc980b868725387e98fa655047aea18b6ba53138952f97d0d9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f86f0ed48aa211ecf9bc4d6389e15640", "guid": "bfdfe7dc352907fc980b868725387e9887f0c60e1ef68bf38dd2a564f13f9f93", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd95f024e00d57244a031d9f62ed6a80", "guid": "bfdfe7dc352907fc980b868725387e985730b09091c521cb4cf38d7765267d3c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d546a26feff37940f5e8a2bc00356668", "guid": "bfdfe7dc352907fc980b868725387e982753740d440846f6c2ae41423fd58a80", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98480133d3adf9d6be56cfecbd32daf5ca", "guid": "bfdfe7dc352907fc980b868725387e9838ae91ff0f484db1edf2dc6112a2e403", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d293f20da779570a1373c666f65db85", "guid": "bfdfe7dc352907fc980b868725387e98bb3b9c5c4f1a6c8737d0271953ed1137", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a41facae666f727a75d0d6bb7a0e3c92", "guid": "bfdfe7dc352907fc980b868725387e98af2f8e1c6e8f11c5befab10d780ac722", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872403aaaa0a64a149c7faf9c5bfe382f", "guid": "bfdfe7dc352907fc980b868725387e982b60ba92f31f9eda88d1c51b9eef34be", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aaf1de819198964e68363df3aadd3ee7", "guid": "bfdfe7dc352907fc980b868725387e98a620756e0f4fea4ae638e0d4ca2ad963", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849cca95266f1c0666bc8614335131d67", "guid": "bfdfe7dc352907fc980b868725387e98c68fdfc00d271f35db4f815e5ad0f3e7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca6694a74debeb436879529d1cd32d98", "guid": "bfdfe7dc352907fc980b868725387e98cfce75d685dba23106388a89bb8c0d6f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c47defc4814f47655ce671fa1aee8b1a", "guid": "bfdfe7dc352907fc980b868725387e98f8208deb791274c18209a094150a7479", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c51b693a6aa37b460cb6f7362ae6420", "guid": "bfdfe7dc352907fc980b868725387e9837bbc7e1553d8e7bf227547482c7ab88", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1210e7eb583e632f81ac574885332fe", "guid": "bfdfe7dc352907fc980b868725387e98e0f193b926980f86c1487a36312833f1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b6b2c156424671ed197ab9ee47a75dd", "guid": "bfdfe7dc352907fc980b868725387e981e7551bc2be7dcc3b72b24b4433504e8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cec00c203ecaf5f441e109a45b4c30e5", "guid": "bfdfe7dc352907fc980b868725387e982a129e60e7bbbdb8afdbdfd5c1cf0303", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98046b60067b6d734688546cd98836268c", "guid": "bfdfe7dc352907fc980b868725387e98fd6eadb53ed20a919c50a16628384a8a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d9aa1fbe678b5bd4a42fd12a97db7db", "guid": "bfdfe7dc352907fc980b868725387e98e45a14d63b8cbacbcc480d40c88e24d6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b19a364417ffbbc48cef30cfc989ffd6", "guid": "bfdfe7dc352907fc980b868725387e982de41730feba5fad78fe10142903e9c7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887a5c295d1193e1f1fe0109bc9d0c979", "guid": "bfdfe7dc352907fc980b868725387e98e40741f637fef0be32a92ec1327b0aa9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6024ebbdefb15e9364370e604e8799d", "guid": "bfdfe7dc352907fc980b868725387e98edabc627192599b53ed09ddfe9b3c2e7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c98208b26fec38401685509428d91c0", "guid": "bfdfe7dc352907fc980b868725387e9893e6abff1bd51023f50303c3e0d46f23", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa1fd5b263127463d6b0124698e4af97", "guid": "bfdfe7dc352907fc980b868725387e987fa7aa70d57f80a6f1b6c9738d5dbcc1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980abcff148271f3f566741433d6334f21", "guid": "bfdfe7dc352907fc980b868725387e986c427d2816b0ebf42ce722a019a58b1c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb73762cf923313a756b06eb3070fbb7", "guid": "bfdfe7dc352907fc980b868725387e9858ec25035349958853df7efe58873cf3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98758aaca41e18f5cc642a184489c16d90", "guid": "bfdfe7dc352907fc980b868725387e98b5e861aa263a5dd830ef5c9c2fd613b8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e8e2fb50727a6c8cc41eca0cc679c64", "guid": "bfdfe7dc352907fc980b868725387e98ba6b14259bfc3c9265ed42a13798a245", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9850d0827f95b51d4b1cdee62dc0ff2ebb", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981da39883f767cc4c64d6df30d9ed6d7f", "guid": "bfdfe7dc352907fc980b868725387e98791a628a7532e1c56841d3176ee39f1a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6b0401eb2166614724ad3faca103446", "guid": "bfdfe7dc352907fc980b868725387e9812792eefe246dd26d71e3fbfc556a077"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98129a9f334cb2d47449db3395ff7d7785", "guid": "bfdfe7dc352907fc980b868725387e98f9fb521e9eab3ae14a3d1c320cd702c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3dea1bf1c8b30706a2f72d4e6800c45", "guid": "bfdfe7dc352907fc980b868725387e984817fd90cecbe08c167244552047fc1d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7d8e501b4df5b769eb81379c84c81c2", "guid": "bfdfe7dc352907fc980b868725387e981bd8cd6666e96b40e2be55628431aff9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98401dbc5c15a2ae4ba9e9a092b5a2c8c7", "guid": "bfdfe7dc352907fc980b868725387e9848301141b51218205a2ff0abaef22a42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6bb91726537e72566ded2b302a9a125", "guid": "bfdfe7dc352907fc980b868725387e98b9a46694570c7f4fcb65bc751a07eef4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f004de0919f14100b591e5d72d438654", "guid": "bfdfe7dc352907fc980b868725387e98ae4191c38455989b3a41f31ad7376628"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984de0dc8d824d4393140b059971a2c967", "guid": "bfdfe7dc352907fc980b868725387e98102639c8a0e129c4c6a4047051b8ccf7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f360fc54f1e74bc61fc33051700e36df", "guid": "bfdfe7dc352907fc980b868725387e983ca6659a4102fefc2633c8e532b44abb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1c589e4ceba4dacc10ad55bc9204375", "guid": "bfdfe7dc352907fc980b868725387e980b3dc02c69842c50290b0787d6af8f85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bdecb5def78f87f6f8734e840bf1d0ae", "guid": "bfdfe7dc352907fc980b868725387e98df70dd9e49e016d89fe2ff7178b54dd8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3a55edba48957e905b2752d60f58208", "guid": "bfdfe7dc352907fc980b868725387e9887a69b3bb9bf142bb1d47c86337e30fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b5d5ec2bf2e447a69426fd5ed2a341f", "guid": "bfdfe7dc352907fc980b868725387e989d7c254ce5c27a440a45b536e450e71d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b53a1fa9d6c39a30a8e9c19fd3409d9a", "guid": "bfdfe7dc352907fc980b868725387e985a11ea6f1056378ad092f96e1b6097bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804e17e0792ee4d796d30e884cfb0b509", "guid": "bfdfe7dc352907fc980b868725387e98385f02afa6625c8ebbb700098c9b0c65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988315bf7092b346de4f999f474f19c48e", "guid": "bfdfe7dc352907fc980b868725387e980ca70bfedf4ed6d5f046d7b4951c90ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b997d8bd38a628b506edde56a1dea68", "guid": "bfdfe7dc352907fc980b868725387e98663326eaccc0e669ad4ff26c992d0447"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb00174d39c26bd5e422f0fc89d61f1c", "guid": "bfdfe7dc352907fc980b868725387e987a492ace742b96685708d37f14c7c550"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d61fde4cea407574a036a93940c0e14c", "guid": "bfdfe7dc352907fc980b868725387e98fa1875ac1f1c14dcfe3b68a782c6533c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9b8fa5209826c2c111a6d400aa20d46", "guid": "bfdfe7dc352907fc980b868725387e98747275eab8a34456566f7cf69887ad4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861f05404869dcef42680719cb5d93a65", "guid": "bfdfe7dc352907fc980b868725387e98fa04c7c113e76b372aa20bed74c6cd95"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ef1f74dde328820289adb687340d0da", "guid": "bfdfe7dc352907fc980b868725387e98330f3fff6b5d75efbfe82c543763aeec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c84694e99d7300d1cd595c9c8f45fa7", "guid": "bfdfe7dc352907fc980b868725387e98ec03a4c9e51e780c5db7a5fc0a73234d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832803e476ff240cff5ec7b4dafe9df19", "guid": "bfdfe7dc352907fc980b868725387e988f292d480ddd3a80a4bc33d970318861"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857f20a6ab81757c1a1cdbf0248aade60", "guid": "bfdfe7dc352907fc980b868725387e980e5e8972ffee2037e6eecb217a4ee0b4"}], "guid": "bfdfe7dc352907fc980b868725387e98320e1412584a90fd41da063c83d2dd21", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987e9cbf3be042958b470cf98aebd11b1b", "guid": "bfdfe7dc352907fc980b868725387e98c66fe7d2ba435345befdf4d53ffe9324"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d971dd0125307f6dda33d52f810c5c9f", "guid": "bfdfe7dc352907fc980b868725387e9837f82c7d5501ad000577f511c7066743"}], "guid": "bfdfe7dc352907fc980b868725387e986639f5be6da1c90ed3fe0682aa68698f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98569e89e49d03cc7e5bfa84b901e57021", "targetReference": "bfdfe7dc352907fc980b868725387e98e6c2666f7221fa58caaa0668238bc4de"}], "guid": "bfdfe7dc352907fc980b868725387e9878595d290b1af8130556a3317b477895", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e6c2666f7221fa58caaa0668238bc4de", "name": "photo_manager-photo_manager_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98cd1e97b2c7c0c2a96b4035a4c62b427d", "name": "photo_manager", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9841b881a585d538cf3da17a18e8b8ed12", "name": "photo_manager.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}