{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a1f414670bc404e772d262eb2b183769", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/media_kit_video/media_kit_video-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/media_kit_video/media_kit_video-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/media_kit_video/media_kit_video.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "media_kit_video", "PRODUCT_NAME": "media_kit_video", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980243d3a1bddb5b4ca2b03ac733432da4", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987bf5641bb74aba7f56ccb7c9f9841c5a", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/media_kit_video/media_kit_video-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/media_kit_video/media_kit_video-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/media_kit_video/media_kit_video.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "media_kit_video", "PRODUCT_NAME": "media_kit_video", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98413d502756c7c967565b3c9889e09583", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987bf5641bb74aba7f56ccb7c9f9841c5a", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/media_kit_video/media_kit_video-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/media_kit_video/media_kit_video-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/media_kit_video/media_kit_video.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "media_kit_video", "PRODUCT_NAME": "media_kit_video", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9864dd735e4ee67dbe291725fdd978bfff", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9816b4be00a1dadecd9e17e5e260645007", "guid": "bfdfe7dc352907fc980b868725387e98c83e76f4c552c30c0c4bbbd03f9a4a84", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca6876c54bdcb97099d0dcbcdfa869b1", "guid": "bfdfe7dc352907fc980b868725387e98dac8bd7dde0b503f0bf7d2caa3586b00", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ca6f888e4055a81307e5ccac0ec5647", "guid": "bfdfe7dc352907fc980b868725387e98cb75935e1e375759e45da12c71905a44", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983927f4d59224090cb175eaf1b26990b1", "guid": "bfdfe7dc352907fc980b868725387e980944c23219fcdedef1d81eb235c696a5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b744ece7907bff7ffcf5e57d2cdf1e9b", "guid": "bfdfe7dc352907fc980b868725387e98afa8bfc270368190bacd344d9caa3f40", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98971bf9ebdb1c322c078f45495a61c7b0", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a5f32975d70fb0839dc6e1edb333f6a6", "guid": "bfdfe7dc352907fc980b868725387e98fc094a394e60c361f8c9377e4aa544cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce9fb2689753541b15d413ea6acd0048", "guid": "bfdfe7dc352907fc980b868725387e989941f893cefeb01a23924488be329e72"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0cdcf322420d7d4019bbad033d84cbc", "guid": "bfdfe7dc352907fc980b868725387e9851513df02c4845393cd3d88ba6c5ef94"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a1e3aa4a1b78e95b5f5ac9d2c9ff832", "guid": "bfdfe7dc352907fc980b868725387e984961f68e00023d2741f7b27f60555591"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbf7699dda65791871b81a0b67f534db", "guid": "bfdfe7dc352907fc980b868725387e98921259d140f34faf759214089e260210"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3af108506105050fd903bb94697cb73", "guid": "bfdfe7dc352907fc980b868725387e9809ff469584afbe81380e69394ceebf9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830ef9ae1100a320cca3cc456707296c1", "guid": "bfdfe7dc352907fc980b868725387e9861a0e620f306c862120aa3cdc87148cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984913dcd5db9b5e97de4775534631da9b", "guid": "bfdfe7dc352907fc980b868725387e9845b0ba35b2d1575d5fa4ea9cbf798e0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ddb84c3ee63270dd6e2a4185faaa859", "guid": "bfdfe7dc352907fc980b868725387e98149a9a616cb69eae318c9e2ca280a7ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848882290d98bb28fef2b0acad0f08c66", "guid": "bfdfe7dc352907fc980b868725387e98c5f26d1343a98174f1702a30e4cfb2d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898c5cf2562fe3b6e45b83b41791dafd3", "guid": "bfdfe7dc352907fc980b868725387e98f642aa65f5c8d2fe7f19d7a930b3ccdd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9d1e4437f970d2bee42a0d8fe170be7", "guid": "bfdfe7dc352907fc980b868725387e98f38038fcaa1a4e23834fa13d41b1858f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed13556fa0c97618e0b35ed967ae6b0e", "guid": "bfdfe7dc352907fc980b868725387e98950cca74f960545310eff8550d49e0b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d142298a106acc76be401604405b960", "guid": "bfdfe7dc352907fc980b868725387e98dcd985b429281f6c5ca875f46666ad9a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a53365354ccfe7c5a6c6a2a7a70d8c53", "guid": "bfdfe7dc352907fc980b868725387e988b8248e75e7968662d6df6e6ffe47adc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b8f048d2c4725dfae00781edd2cac8c", "guid": "bfdfe7dc352907fc980b868725387e98d8b2a2f299f9a732fa272cf2feef2fd1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98041163b0ecb66286116c4fdfcb000c60", "guid": "bfdfe7dc352907fc980b868725387e984e8201d6c9e5ae1817c6be7eeedd30cf"}], "guid": "bfdfe7dc352907fc980b868725387e9880bb9cf584e2300d23112d6d963235c0", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987e9cbf3be042958b470cf98aebd11b1b", "guid": "bfdfe7dc352907fc980b868725387e98c839a6d7d5d715ed7da4aa60a97d9203"}], "guid": "bfdfe7dc352907fc980b868725387e9823df8465071d31b6918848ef9d003991", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e987bbcf8c37661cfb45f205082ee77b728", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e98ef586bf23362aebce5c719b4482fa695", "name": "media_kit_video", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9897cdf84f28cf59650494803c009f76dc", "name": "media_kit_video.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}