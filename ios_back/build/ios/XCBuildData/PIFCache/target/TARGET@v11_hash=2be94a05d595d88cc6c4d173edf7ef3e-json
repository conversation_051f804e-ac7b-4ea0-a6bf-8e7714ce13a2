{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981ba4d86b81a9852b142a8bd9f6874b13", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b2f4b25a8d90636f47ae32244238e77c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98896c988a3de55fc53bb6b3a9cb9cce07", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984e4d86d70890c21ed16cf87923a2cf2b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98896c988a3de55fc53bb6b3a9cb9cce07", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/just_audio/just_audio-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/just_audio/just_audio-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/just_audio/just_audio.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "just_audio", "PRODUCT_NAME": "just_audio", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987b7fb43e5cb40c6c38d1a3e45c7031a6", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bf30be0bb88968f8ff5d2ae69916ce98", "guid": "bfdfe7dc352907fc980b868725387e981f6cc9ba376564cc4e242af7b0b45a32", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbd11ae0f83f22f32a3314f7ed352326", "guid": "bfdfe7dc352907fc980b868725387e983ffa8cb1763161c9e979cda397938e17", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffc70ccca8c3df3a18e555dcccd81f61", "guid": "bfdfe7dc352907fc980b868725387e98d2405f3b89fba3d620c6b99fdec7cba7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5ef3b1736ffca83dfa38df8443ce938", "guid": "bfdfe7dc352907fc980b868725387e98dd9a6f15ec8c974b4f1375850e6544a0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2009aad1e659d4ed2cc7bdd25f73e73", "guid": "bfdfe7dc352907fc980b868725387e981982dce0595d07b4ce08c5cbdaac13ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837c4daf2c04f87f2aa4ebea82d2be4be", "guid": "bfdfe7dc352907fc980b868725387e98caff27028902c9d6b06d8bae86e934b6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989351afa88c770ff42e5523f7cd284c7e", "guid": "bfdfe7dc352907fc980b868725387e986b626a4ef2a8ef8fa22b533b334138fc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984acee17bd749e48975e2c6a22688d93f", "guid": "bfdfe7dc352907fc980b868725387e987a065af2610f73e182d8aef95321d80e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819e46df489cf023354c3f9a8b38b5d87", "guid": "bfdfe7dc352907fc980b868725387e9833250aa97c6950a6a95e41b052268137", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fcd7ab8415d4794846859ab8adefef4", "guid": "bfdfe7dc352907fc980b868725387e9844cd995050169df79e6bb415df88b1ac", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849be19a0aeb8d67541a5784bbd11e879", "guid": "bfdfe7dc352907fc980b868725387e9800134652db4a14c9b54a60775d3cd766", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1a73c536fb73af279b0caa2c817fd64", "guid": "bfdfe7dc352907fc980b868725387e981dbdba46066d382822ed87363b2508a2", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b1c6e0dac1c0f3288cdfd038dfd59299", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98026cc4493cd3288ba5e81d419be00f1f", "guid": "bfdfe7dc352907fc980b868725387e987ec5d3a7027491b0853202db00b1f5d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986732e413fae810ab7f9bbdd809f03188", "guid": "bfdfe7dc352907fc980b868725387e9849836eca253a3d81d47ec37b68b493ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860d870228871367a726ae4bde3920454", "guid": "bfdfe7dc352907fc980b868725387e98252c126dacc5ff494aecd2ce735c45d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98415ba6f19e908092e1627fe51f248a26", "guid": "bfdfe7dc352907fc980b868725387e980fcd9ee71d497e180aaddba0459520e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987db6161c030dc90023dbff20316fe050", "guid": "bfdfe7dc352907fc980b868725387e9815cfa6f8be316fd779227d8158cf27ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb2efe9c23c5f901d59eca164e30d82a", "guid": "bfdfe7dc352907fc980b868725387e9801c6cc1227cfade99ed6d5e2199542f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4a7439ca9c338a761243fb7d9b54709", "guid": "bfdfe7dc352907fc980b868725387e98fae72231ecec79976bb0e42e803fbd30"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837fe4ff29e9a7a0440f21041a7216953", "guid": "bfdfe7dc352907fc980b868725387e98b48d635eb12a83e92dded002cc742c7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981313a3234a5df869a78f134eef79de79", "guid": "bfdfe7dc352907fc980b868725387e9854aeea76dd39e7d7289716b838772749"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b78670ccce078a27ff3451c009101fcc", "guid": "bfdfe7dc352907fc980b868725387e9840bc7d43bdb5608ee4d14d90ffd3f980"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbdf9dc5c893c509ee329dbb4bb01ee5", "guid": "bfdfe7dc352907fc980b868725387e984666650d7b13fa61edc18a0bbc64724d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b23e09aebe233b22b2a3d85ca6c997c0", "guid": "bfdfe7dc352907fc980b868725387e989ec6c056940ceac336198585f710d943"}], "guid": "bfdfe7dc352907fc980b868725387e984828d07df21b03575280a435412b2bd2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987e9cbf3be042958b470cf98aebd11b1b", "guid": "bfdfe7dc352907fc980b868725387e984dff9b9164ea7278ab55766135fcbf91"}], "guid": "bfdfe7dc352907fc980b868725387e98e02b84aa74e21e5d0824e135f776cc74", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9898f5fd0d63861b94769f137ac6982066", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e98f1ed40c0d488e7d17a2574221f5de571", "name": "just_audio", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f09d8f416b4b8830579237c0f3add196", "name": "just_audio.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}