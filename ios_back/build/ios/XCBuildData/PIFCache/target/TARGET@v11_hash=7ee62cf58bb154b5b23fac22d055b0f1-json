{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98da9e59a2d1db95208045528360585942", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/tencent_cloud_chat_sdk/tencent_cloud_chat_sdk-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/tencent_cloud_chat_sdk/tencent_cloud_chat_sdk-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/tencent_cloud_chat_sdk/tencent_cloud_chat_sdk.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "tencent_cloud_chat_sdk", "PRODUCT_NAME": "tencent_cloud_chat_sdk", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984b5160a906a59b394c2f883d33ae2a40", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98910fa1ac98aa04a00e5452fb03cee8db", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/tencent_cloud_chat_sdk/tencent_cloud_chat_sdk-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/tencent_cloud_chat_sdk/tencent_cloud_chat_sdk-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/tencent_cloud_chat_sdk/tencent_cloud_chat_sdk.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "tencent_cloud_chat_sdk", "PRODUCT_NAME": "tencent_cloud_chat_sdk", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986d995c3108d139c3a9c9b8f7011a5e85", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98910fa1ac98aa04a00e5452fb03cee8db", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/tencent_cloud_chat_sdk/tencent_cloud_chat_sdk-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/tencent_cloud_chat_sdk/tencent_cloud_chat_sdk-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/tencent_cloud_chat_sdk/tencent_cloud_chat_sdk.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "tencent_cloud_chat_sdk", "PRODUCT_NAME": "tencent_cloud_chat_sdk", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984569a86a501d11e985a3727b88c54198", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9888fc0a6e1ef7f9b4256a8ac9d0ee6c4d", "guid": "bfdfe7dc352907fc980b868725387e9804aa32ec14e0070757e08713c37d0837", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98bf5e1a0f54bfc089b04cb2dc0a490476", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a5ef6d3d6340ab91dfb8182b13878d82", "guid": "bfdfe7dc352907fc980b868725387e989700a8b09094718b61fcb3f2e3d7169d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987dbe8b43adeb1b18cb53bce13785569a", "guid": "bfdfe7dc352907fc980b868725387e98e3a783bb9f8bf0799ed2698046f7a724"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a16e08b2733a222fc31db8943f6f061", "guid": "bfdfe7dc352907fc980b868725387e981d0f74017dd9f1bd3ff385af84e3dbc9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982853fe099ee18cc0765b885996a32b72", "guid": "bfdfe7dc352907fc980b868725387e9886563f4ac5602e8667cffd00a030d45a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ca819e3d441832a5da05477659cbf0c", "guid": "bfdfe7dc352907fc980b868725387e98f68470d88ce0dfd39bb350cbae0fa9db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d3c625217da425b02b31ded64efa4d6", "guid": "bfdfe7dc352907fc980b868725387e98a35a183a5b7f9fa01073cb644465934d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821b1f2a72ec989b97165c5bffc65f1df", "guid": "bfdfe7dc352907fc980b868725387e98b2a1cd5aca8c1c9c120ea423eea98a16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c8e8331ab4b8adc87d8fcfa98be7038", "guid": "bfdfe7dc352907fc980b868725387e98ffed8fc288d1478b60bd5ab46cca6445"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883e4f388e760a688f1d1171be86a2145", "guid": "bfdfe7dc352907fc980b868725387e985342d3e49dd4823c32908bdacfbadacd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e32add709c2fff3a0f194fba1f4eee8c", "guid": "bfdfe7dc352907fc980b868725387e9873b0e9d830551755c83a134c2239d469"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e06ca55505d73b43e7c945c845d36092", "guid": "bfdfe7dc352907fc980b868725387e9831343d2c57096a29a326253b0604409a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2d5e2acbab786a90e1a028608b83ff9", "guid": "bfdfe7dc352907fc980b868725387e9899c71575f04cf1a2b894920b13ecebaa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98624e51350295cd0d1b834a5f60390564", "guid": "bfdfe7dc352907fc980b868725387e98ee77c9d1cfb7b1f18078a0fa363d1182"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98972d9e4e4ad39522b22723b7ee08b988", "guid": "bfdfe7dc352907fc980b868725387e9816a56d199c6edffa8bdb255dfa23fa33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d99bc96510d796edd245440758a7b7c6", "guid": "bfdfe7dc352907fc980b868725387e98df4fb9e9a5ef4e6e5791a21386dcc78a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffa44c660b3a25840362b325f48b353c", "guid": "bfdfe7dc352907fc980b868725387e9832d7a8985ccb22658e5b24b284fd5c8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98178af52f6f70b560bc27456b44502ff5", "guid": "bfdfe7dc352907fc980b868725387e982f50abbf296ae94007e97673d3e73c12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9fbc229c1789b4376f9cb85b94b8271", "guid": "bfdfe7dc352907fc980b868725387e9898325f634163a5f5d5ca158ce2671f00"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e60edab10165605d9726422869c8c70", "guid": "bfdfe7dc352907fc980b868725387e98afa7c22dcb2cdeab28f482e4f3f6c945"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8fcabd420e00c9805fc437539881d14", "guid": "bfdfe7dc352907fc980b868725387e9857a6f767ff4c4e6cd6846cb70db952cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d68bb13ed90570abf2224ef64a7ffe50", "guid": "bfdfe7dc352907fc980b868725387e986a5cccc3b656fee419b52fe48d74ad6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986913ec2930da6eeae8f2c3f4cc6d2186", "guid": "bfdfe7dc352907fc980b868725387e98ea215a0e7b0cb5cb4d385acd29be0111"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864c9be3f7084903fe47eddda67104c38", "guid": "bfdfe7dc352907fc980b868725387e981bec802395d44d284b239b667b015257"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875b0498c7427b3e2fbd9ee8a44b09c20", "guid": "bfdfe7dc352907fc980b868725387e98c69f47e34d0c6791829d8f5414aa4245"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e84a9ffc089898e7ce2ce29b01ecc0fb", "guid": "bfdfe7dc352907fc980b868725387e98b87aa11bdcb7184f24ce7d6849c2d9d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad27ad7c7360b9272bb841ae661389d8", "guid": "bfdfe7dc352907fc980b868725387e982f3d592e8f32a55aad4feb9ecae9cb1d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98126ab231080cb8608230a4260f608e36", "guid": "bfdfe7dc352907fc980b868725387e9814c88eaa3948df84d16f6ebdd18f48dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860fb974810f88ca7ce0e4ac06c9b044f", "guid": "bfdfe7dc352907fc980b868725387e98c5287ea202b872302b143fada7ded27f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec4b4500d6008fe3f054cf0e0b954df5", "guid": "bfdfe7dc352907fc980b868725387e98a03715d22268a2c1b293ae01e276040f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835d7ef45b6d4c92ddeeb9379e36901d9", "guid": "bfdfe7dc352907fc980b868725387e98034a351c1550b11c17eab0f1debcb4d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fd197c85712a8d50bd3597c8508595b", "guid": "bfdfe7dc352907fc980b868725387e989ee5f095ab5455918acefe41a85327f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862915b59a65c88809a8affd5f718298e", "guid": "bfdfe7dc352907fc980b868725387e980536f1d5c3b43e4a8eb791e4fe029401"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1485881a555a27ab5a1bd671bb12820", "guid": "bfdfe7dc352907fc980b868725387e9875645b5190011e0ca7caf02f33150029"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4e99058b7a418beb81d02ab320b82f2", "guid": "bfdfe7dc352907fc980b868725387e9817d796a71f7870b96c06f9ec13f20662"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837a7086b4f81a0ed099ab36a8da40a8a", "guid": "bfdfe7dc352907fc980b868725387e98d3ea3ca914d34e1d3b108742dd9faaf7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4d97f4af80bb371661763c39a1c08b2", "guid": "bfdfe7dc352907fc980b868725387e98e38242046343b9dbc1c86dff14ddb222"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2ce8ce7ab5c6b84084588ffcc249833", "guid": "bfdfe7dc352907fc980b868725387e9810408735358609860c79ff13ebb054f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989eeb4b65f4ad3a70bc18839e3c3e69d7", "guid": "bfdfe7dc352907fc980b868725387e985b320414e27b7bb44c2535098c2ec236"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b7fc865846aa78322f0a59bb5b4818a", "guid": "bfdfe7dc352907fc980b868725387e980f0a76894d4e68c3fecaa67912ace06d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ee2645f929aed9849b7792427b9f254", "guid": "bfdfe7dc352907fc980b868725387e987cd9f92c3261040810a8efdda8657514"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b64cdb732ee8fe28e1624c01ef8163c", "guid": "bfdfe7dc352907fc980b868725387e98ec39de0fa7b8bafe44b557f80f050d7e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8ee5d60e187c477dc0720795fc3b135", "guid": "bfdfe7dc352907fc980b868725387e98d29ea893fee877fdfd4e9b222db85e26"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98acb1db9923d76e97288089f91dc34824", "guid": "bfdfe7dc352907fc980b868725387e980d4ad8cb6399962ebca144125894a877"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0658ec2255e146125b242aa827d7854", "guid": "bfdfe7dc352907fc980b868725387e98ce6fd655b8212f7ac7e30bfa76a93f8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e04d9d1889466b3c6fa73b6d3857fde0", "guid": "bfdfe7dc352907fc980b868725387e98c0c1eff914b35c8b81e8f1bb25036050"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e85c149a5b3b824f1514271fc3dd0a2b", "guid": "bfdfe7dc352907fc980b868725387e987b40990e5abb691659c60d489eccb527"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98611bbd7f31722bcf30c3ee7b05dd68c2", "guid": "bfdfe7dc352907fc980b868725387e984a562ec9a4e70b591c0df352c13b2c2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f36de6c4dfbd7e8f77c160b38339995", "guid": "bfdfe7dc352907fc980b868725387e985ab97c05f3d14bfda86ebe7d9990b210"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b27bbcd6661d34111051a81503210da", "guid": "bfdfe7dc352907fc980b868725387e985170a1d500909878fad7516617ffe87d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aee24f33f2aae8a346355332fee54a4b", "guid": "bfdfe7dc352907fc980b868725387e98a7cc5a0a1ca56dd65038fe12c0585060"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eeb1352cfcb343cbaf0d5efd72882c15", "guid": "bfdfe7dc352907fc980b868725387e98dd936fdf63c84463b5cb1b9c4412854e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1161dee93d0fd6081a0ab1fc138a6b2", "guid": "bfdfe7dc352907fc980b868725387e987a2e80c1f4a7dee235f7f481b54fc8e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5b408252b7e138898128abd1fb5a4c4", "guid": "bfdfe7dc352907fc980b868725387e989b0f46b931ade924501b43dd2930c81e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f470eb7ca038b6e61edd5b77e2365bd", "guid": "bfdfe7dc352907fc980b868725387e987514553580fe99323916975e8a7b347a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a96256c1d3f21d229495333e72a9b0c", "guid": "bfdfe7dc352907fc980b868725387e98a4bfa30ad03ddf22abfd5e65e422d92f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef7c9dddbe5bb495b05f60d1bf427afa", "guid": "bfdfe7dc352907fc980b868725387e9875f10d65e74ef4e3e4514d470c8b1a3c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7cf7b998d635753490167b8b71a611d", "guid": "bfdfe7dc352907fc980b868725387e98d294b569fbb4beeab2e97aec3f762212"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee0153ab20d9b826a3c3b17e67c65dc1", "guid": "bfdfe7dc352907fc980b868725387e98130d504c986403743bb589bb10ad5885"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a321e0bfbee07a9325b7b9e99525f8d3", "guid": "bfdfe7dc352907fc980b868725387e98f886293929feba82f8bf479a7ecac259"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4cd251c2faa76d698c917ba54f942a9", "guid": "bfdfe7dc352907fc980b868725387e98e0dd6aa009ecd47a1386c571d05104d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986998228b7602776370dec01b347950af", "guid": "bfdfe7dc352907fc980b868725387e9877bf067e780047ad8b2741570cc715d8"}], "guid": "bfdfe7dc352907fc980b868725387e985be35c8f2be061abba6868456f2e7531", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987e9cbf3be042958b470cf98aebd11b1b", "guid": "bfdfe7dc352907fc980b868725387e98aa83829e73f98b61e0526f65d7a65699"}], "guid": "bfdfe7dc352907fc980b868725387e981151d104c3133607be3bd2d492c99fef", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e981153a40cca0547b64b12d03347d114fe", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98df71c66ecc82a9796c50f296fb0a215b", "name": "HydraAsync"}, {"guid": "bfdfe7dc352907fc980b868725387e98f1e4294f9d6a6e169e02b24c5d04222a", "name": "TXIMSDK_Plus_iOS_XCFramework"}], "guid": "bfdfe7dc352907fc980b868725387e98ba204d184f78a46e2605c4a18658ab11", "name": "tencent_cloud_chat_sdk", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ec6577985ee917b8779818a66bcd1b13", "name": "tencent_cloud_chat_sdk.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}