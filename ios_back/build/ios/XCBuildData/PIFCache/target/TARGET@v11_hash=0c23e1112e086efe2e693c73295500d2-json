{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988b012247241c57889880f2892e7035b3", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/SDWebImageWebPCoder/SDWebImageWebPCoder-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SDWebImageWebPCoder/SDWebImageWebPCoder-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SDWebImageWebPCoder/SDWebImageWebPCoder.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "SDWebImageWebPCoder", "PRODUCT_NAME": "SDWebImageWebPCoder", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980376258cf776abf8ce6cb4e71cf0e5c2", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986ff1c1dee3ccfda72c2ec78ec7019f98", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/SDWebImageWebPCoder/SDWebImageWebPCoder-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SDWebImageWebPCoder/SDWebImageWebPCoder-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SDWebImageWebPCoder/SDWebImageWebPCoder.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "SDWebImageWebPCoder", "PRODUCT_NAME": "SDWebImageWebPCoder", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9882724d5ad0240f81686d533b9f06f575", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986ff1c1dee3ccfda72c2ec78ec7019f98", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/SDWebImageWebPCoder/SDWebImageWebPCoder-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SDWebImageWebPCoder/SDWebImageWebPCoder-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SDWebImageWebPCoder/SDWebImageWebPCoder.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "SDWebImageWebPCoder", "PRODUCT_NAME": "SDWebImageWebPCoder", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9822f672670fb4d72497b3a2f5eeb8b914", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9819acd37287c236279d08a89b2d4f721c", "guid": "bfdfe7dc352907fc980b868725387e986c283334b0f78e3ce7f3995e776821e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3a5598d43b0878333666469ff18ca44", "guid": "bfdfe7dc352907fc980b868725387e989118f7791c2917f8c155075204020844", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846cee45b6e627c33c6b169c4f0d46178", "guid": "bfdfe7dc352907fc980b868725387e98edd755837502d44416c150d3b1850839", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b02dee552a84f22ee4309ee7beb82fd", "guid": "bfdfe7dc352907fc980b868725387e98c1c06d18c4cc9266c1b3785d6953d65e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e63a2a3fe444bf69ee4a74e7594b7866", "guid": "bfdfe7dc352907fc980b868725387e98815d7e43900e1d49bfa3acf6cf130c8e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f07e10b8666a6adc2b0a91139cf934c3", "guid": "bfdfe7dc352907fc980b868725387e9890c9a0c5d198c44843a651b0fa6b7e3e", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98bcec3892e3fccf55522c44e10b4fb6d5", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98129d3f27065e7cf14f2b649b3b279143", "guid": "bfdfe7dc352907fc980b868725387e98d19bd8b452c2d89908a483f66d228717"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e181bd413f6233cec42190e2dd5a5823", "guid": "bfdfe7dc352907fc980b868725387e983440d50d1770549113d8bc94de8edd25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980674e5ec18c2011ee642336845458451", "guid": "bfdfe7dc352907fc980b868725387e983d7985d437801894b0bbf80f5e1f8861"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c5c84badb5d699a03f96b6ce75e2504", "guid": "bfdfe7dc352907fc980b868725387e98274fb2600683282693736ea4688ae907"}], "guid": "bfdfe7dc352907fc980b868725387e989bfdf2eedd58ad6f23c1b542e8c26b26", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98144836b1cb38b7590a760794efd54afa", "guid": "bfdfe7dc352907fc980b868725387e98ef962ae0db04c0df1232eb871c3b9a10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e9cbf3be042958b470cf98aebd11b1b", "guid": "bfdfe7dc352907fc980b868725387e98da5cb7aeacf8fec8deff4d47b3d76817"}], "guid": "bfdfe7dc352907fc980b868725387e98c3b08a8dbb89f44cf4421f62f39fd87e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98a3b4fb766144d79e871a4ac6cdcde678", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e98459ecbbef4dbbe8a07a0c87bad0e0d1b", "name": "libwebp"}], "guid": "bfdfe7dc352907fc980b868725387e9837027570fe3a09bb9c7d0d2620332306", "name": "SDWebImageWebPCoder", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98a513e2d8d894b8762a51df43e9ec3f83", "name": "SDWebImageWebPCoder.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}