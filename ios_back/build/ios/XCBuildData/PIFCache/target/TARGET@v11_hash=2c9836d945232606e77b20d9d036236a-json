{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9857928b53fa76a93ebbb8617557403a5e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "camera_avfoundation", "PRODUCT_NAME": "camera_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983000b497bb09479468c0390e594d8342", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982eff8229378cc0a9e0c9780bf6bf0017", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "camera_avfoundation", "PRODUCT_NAME": "camera_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c6c98f0462a9c32dd4b5f1d229426903", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982eff8229378cc0a9e0c9780bf6bf0017", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "camera_avfoundation", "PRODUCT_NAME": "camera_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980bc205f56ddd18721c867471c3867697", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cc1950a7d96b6733f6c77ef32611e158", "guid": "bfdfe7dc352907fc980b868725387e98fc20616a15f31ba464c0f9a642f15653", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fd59c64f50a91bf62abdf03a95eafca", "guid": "bfdfe7dc352907fc980b868725387e9871a5b8d3c22d04974867efb20e22d95c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ddba538070623742c14717ffc70ef05", "guid": "bfdfe7dc352907fc980b868725387e98b5f12e0ca26a18d9d0945c54a216c1a6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827cb3e65b9e74bfaf56c6a1b10ec29f7", "guid": "bfdfe7dc352907fc980b868725387e987b1287d3ba1007aa8c9b2f52f3123308", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813e5b5bf2af98250ce309f144632af44", "guid": "bfdfe7dc352907fc980b868725387e986fd7bfca36f8ad091101c3916316456d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ea70c9d3f2a753ba673b587a564aa73", "guid": "bfdfe7dc352907fc980b868725387e989ecc9bd639cdccab5c9468096569e548", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7a66e695bb44a43760a94260d88625b", "guid": "bfdfe7dc352907fc980b868725387e985d4eb7da8eccc55ad0b397b1c1d9e7a0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985daad70e31af9751009e834878f314de", "guid": "bfdfe7dc352907fc980b868725387e98d822c3029b41266a00c999c718098f3a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895f8e3c2fab7e96efce99e44709a60eb", "guid": "bfdfe7dc352907fc980b868725387e98d130aaacaabfeca46c2af2e82f0f9d85", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98916e98ff8091d604b0cd1bf4aaf1d963", "guid": "bfdfe7dc352907fc980b868725387e98d809d133d804c7119759b1c0adbe128f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e8b393dc1c2836b9526486818d48956", "guid": "bfdfe7dc352907fc980b868725387e9885b0ed3a104ec2f6fec14bbe1f0fc71e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98635e8bb720b7450b6ed3459a79267eac", "guid": "bfdfe7dc352907fc980b868725387e98730d8c989c76dc7c40f55e78274c7691", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d84d268062097d3abc082f3fe185f562", "guid": "bfdfe7dc352907fc980b868725387e98420dd556901c3fe6806fb7e8eb299496", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e981a370673afe48c5e5b57fb4f303b8a37", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984d3907b6c9410c624f24277ba7bfb60a", "guid": "bfdfe7dc352907fc980b868725387e98251fa87b14a46256a65aea8046382815"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986700f4851f9f6f1eb34dc2ae4a182a98", "guid": "bfdfe7dc352907fc980b868725387e9870cf6efe522413c7839ff780f421d9c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98364d55bf5b64bb0e52b8de1eae109b85", "guid": "bfdfe7dc352907fc980b868725387e98b67ba1f20965fb090953102d8b9e77e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842bc8e814cb9526edf015c40ff611013", "guid": "bfdfe7dc352907fc980b868725387e98980ed3b1e3b760fef951475869a19ec7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98045fc60c0a74bb0621470a00c59889a0", "guid": "bfdfe7dc352907fc980b868725387e98ed1034f6f5134817be4a6662d0f26720"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2e761f24e92b22ded3ed87b8acbf18d", "guid": "bfdfe7dc352907fc980b868725387e987dc5de714b0573a49d4d22ad02993040"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98520b9d151fbef7f286c66690e2b58f93", "guid": "bfdfe7dc352907fc980b868725387e980af47782cf93ebbd9cd4563d099fab2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e82de1369fe14ce58fbdb90fab982799", "guid": "bfdfe7dc352907fc980b868725387e981a6cd23d283efd86bfdd1afd05a4bcf1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b8a47f53fc02792572c4ce3e4b790bb", "guid": "bfdfe7dc352907fc980b868725387e98c936744384390af07aaa6ec0b221696f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9c224b552360b193d1e21e86d17c51e", "guid": "bfdfe7dc352907fc980b868725387e985795bbe0c35984ad12fc18caeafc28bb"}], "guid": "bfdfe7dc352907fc980b868725387e988aa9516a25305f519908a499366583ae", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987e9cbf3be042958b470cf98aebd11b1b", "guid": "bfdfe7dc352907fc980b868725387e98a98357da29796fa7ba184eeb42eeabcf"}], "guid": "bfdfe7dc352907fc980b868725387e98a775a8254b292ef44c8d2229c94936be", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98e90c81574e4dc7bcabb9bf8386afb39a", "targetReference": "bfdfe7dc352907fc980b868725387e98b9038e7e871b75150c57ed973218b158"}], "guid": "bfdfe7dc352907fc980b868725387e988eaff77d0a06402ee4d9c53d81ecc38a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98b9038e7e871b75150c57ed973218b158", "name": "camera_avfoundation-camera_avfoundation_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f08a09402d437c098acddc7bcf497e64", "name": "camera_avfoundation", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983903b9d6299cde09dc2b081ad04abafe", "name": "camera_avfoundation.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}