{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987b45189253ad50f4e749e4865874162d", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/Mantle/Mantle-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Mantle/Mantle-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Mantle/Mantle.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Mantle", "PRODUCT_NAME": "Mantle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832f57634461bfc07541306e0e0909c0a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f92d543457613c972563dba055a5155f", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/Mantle/Mantle-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Mantle/Mantle-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Mantle/Mantle.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Mantle", "PRODUCT_NAME": "Mantle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b77ea053d833db80a19dea3c0cec528e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f92d543457613c972563dba055a5155f", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/Mantle/Mantle-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Mantle/Mantle-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Mantle/Mantle.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Mantle", "PRODUCT_NAME": "Mantle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980fcf27a1859dbb654e6d1632ecd3da9c", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988c0de256ca7e881d02d38aa3f800fa98", "guid": "bfdfe7dc352907fc980b868725387e98ba5a719f03637779d0e4114c173859a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c0a8d0305428221ca8e2d9042302043", "guid": "bfdfe7dc352907fc980b868725387e98e2307c7e3b6ededb064e5ddd2a68ec15", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98672509428d71bb8592ca6c29018bc406", "guid": "bfdfe7dc352907fc980b868725387e982d562db17f8b5b4b452d2545362214e8", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce04ab2aaa4f4da7e57f6d2a0aba2070", "guid": "bfdfe7dc352907fc980b868725387e98d04ba8a39c5cad90f8f9d0692576eaa8", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983682e6ae5eaa6162887dbdd23554cf55", "guid": "bfdfe7dc352907fc980b868725387e98c33f235531156ee0adb6b8d65a5c7830", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ae829aceaf51eaa11e9c7dcf6e11c02", "guid": "bfdfe7dc352907fc980b868725387e985a895af414c847d231b5f4da7f1e0eb3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8ca6d10484b66c63aef9b549d0fcac4", "guid": "bfdfe7dc352907fc980b868725387e98a1f1dfde0a897a7a48297b39703ffe87", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ab103334d3a6a6a40c4efece1918a65", "guid": "bfdfe7dc352907fc980b868725387e98cf56166b008f01c301c175ad0b214b71", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807f152aa4f8b44ca2f68b2b6d6d31716", "guid": "bfdfe7dc352907fc980b868725387e98d42fe72f176a80ac9bc8f49ca458e7a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870646ba0d954e081272d99141449a301", "guid": "bfdfe7dc352907fc980b868725387e983f53c7c613094709f31b06d10211e623", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98885ecce8a61beeeb940c392d2583bd25", "guid": "bfdfe7dc352907fc980b868725387e982529dcf37e031935f7df29cb7a0c72be", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd5c1355ebb00e9627832f8fa917ee1e", "guid": "bfdfe7dc352907fc980b868725387e98b957187b0e56ff1342b0658816919310", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b79bf8ead06d5025555f68b77516aec", "guid": "bfdfe7dc352907fc980b868725387e98d1e1455bdecb88b6dd994789b1a8d005", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e2aa088a2d6212e87fce7607bcbf40b", "guid": "bfdfe7dc352907fc980b868725387e984dc9a9173228d460dce8bd05febcaa21", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a84233186539b8c6e988b580a510e17", "guid": "bfdfe7dc352907fc980b868725387e9866e18cdc65ffb51bafc9666d133d6961", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffcaf3c4000612a154f12f7294bfff29", "guid": "bfdfe7dc352907fc980b868725387e9853fba8ec8f30e77eea0ee42d162d7b37", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982930150a4afc57a161b742ab3ab9bf75", "guid": "bfdfe7dc352907fc980b868725387e9850835d8912cad2b1f1810e1d3a3f127e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98129d5e1c86c719ae78e6920708790dc1", "guid": "bfdfe7dc352907fc980b868725387e98625e8428f767980263004f084f2f06fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c29905972e98f1df72ac2bdc83984f3", "guid": "bfdfe7dc352907fc980b868725387e98c30c5ac2b99a509847068097d1b90b04", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c587b1676f5cb5532721cbc32f9770a8", "guid": "bfdfe7dc352907fc980b868725387e98d90e924c70c687d95153625ccf22e1b4", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9836a2081adbc8385e3ca55444cbc4c87d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e5c9cc90f6a538577390b929e602acdb", "guid": "bfdfe7dc352907fc980b868725387e986b7cfccea424129119a7e0b7223d203f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fd7e4df5bfa07b6c4693812f82dec2d", "guid": "bfdfe7dc352907fc980b868725387e987e2fd0030cc6bcc3a49952734b8e1436"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d4850bd0a7373f7311c459e728d302b", "guid": "bfdfe7dc352907fc980b868725387e989addc411376632e0842314e1f3dbd3eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec9f54214ed7fd0f608fee74c5d91150", "guid": "bfdfe7dc352907fc980b868725387e98f2b75083ed4223737365239d09129b17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804b45f52d65914a38d27eccf98303e52", "guid": "bfdfe7dc352907fc980b868725387e98f6a65e94218fb5f98f3b3395aafcb285"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b8e034f7aa740dba6e40fd66f59d881", "guid": "bfdfe7dc352907fc980b868725387e98719f6caf81038a4fb6b36c401e41927f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d16229eb9422f38f3d0b367b68da434b", "guid": "bfdfe7dc352907fc980b868725387e9897454b0f741f6ecb896065015ddc7084"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af6599f82322ca1fc083e23e3e27634d", "guid": "bfdfe7dc352907fc980b868725387e980555b502c9c0197b86706e000f128fe4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98588934e938b2ec797d8c60df10c8179c", "guid": "bfdfe7dc352907fc980b868725387e9803c38b008504781c8e28657eb9ba9929"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a783159dc0910a937fa02f2603eb87f3", "guid": "bfdfe7dc352907fc980b868725387e985a2ed91aac5da7e9139f8ae19e8ee587"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980830ed23affb5ac740ea11263178bdf5", "guid": "bfdfe7dc352907fc980b868725387e988ad01aaa6d9324c69a1230aa30684633"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843b4e79ecd0b5cf98030b8fea0ae34e1", "guid": "bfdfe7dc352907fc980b868725387e9871bc7f2e968b7164541a22f887513a7e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bad02fb5bcce5fc63a7967b13e41ae9", "guid": "bfdfe7dc352907fc980b868725387e98811905a9c4ba0e19039cca05ddec61d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b6493879c2a7f2c9c678c674ced64a7", "guid": "bfdfe7dc352907fc980b868725387e9833f6e0f517dacc5b6398ab313854ccb4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e78d06c99f407b2bcb92d4ec77d7c55a", "guid": "bfdfe7dc352907fc980b868725387e980bb1e730b18176ad52f058cae5417ecf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2e63755adae1fc152a924432842c5ee", "guid": "bfdfe7dc352907fc980b868725387e981af43685ae73254d59946e622f376749"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987efc1de9776a557b296b65e39198dec7", "guid": "bfdfe7dc352907fc980b868725387e98641fcdc650b720a8dfa8fb181fa118f6"}], "guid": "bfdfe7dc352907fc980b868725387e98b1dde1f6d525a703b7caf328c78bea57", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987e9cbf3be042958b470cf98aebd11b1b", "guid": "bfdfe7dc352907fc980b868725387e9844ec617da2f257b0b49d378a54c7b385"}], "guid": "bfdfe7dc352907fc980b868725387e987ca18eb2083c48f78e919a58dc322457", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98c95c6b31cc49dcf5ba698c2dfa2917e5", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e982742fb9f3ddc293e43c65bab714408c6", "name": "Mantle", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986716a62ee19f61fc991dd3f4aa3e1163", "name": "Mantle.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}