import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:wd/core/models/entities/home_banner_entity.dart';
import 'package:wd/core/utils/global_config.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/app_image.dart';

/// 游戏首页轮播图
class GameHomeBannerWidget extends StatelessWidget {
  final List<HomeBannerEntity> banners;
  final Function(int)? onTap;
  final EdgeInsetsGeometry? margin;
  late double paddingH;
  late double height;

  GameHomeBannerWidget({
    super.key,
    required this.banners,
    this.onTap,
    this.margin,
    double? paddingH,
    double? height,
  }) {
    this.paddingH = paddingH ?? 13.gw;
    this.height = height ?? 140.gw;
  }

  @override
  Widget build(BuildContext context) {
    if (banners.isEmpty) {
      return _buildPlaceholder(
        isShowShadow: true,
        margin: margin,
      );
    }

    return CarouselSlider(
      options: CarouselOptions(
        // height: height,
        viewportFraction: 1,
        autoPlay: true,
        aspectRatio: 400 / 251,
        autoPlayInterval: const Duration(seconds: 5),
        autoPlayAnimationDuration: const Duration(milliseconds: 800),
      ),
      items: banners.map((banner) {
        return GestureDetector(
          onTap: () => onTap?.call(banners.indexOf(banner)),
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: paddingH),
            height: height,
            width: double.infinity,
            child: Stack(
              children: [
                AppImage(
                  imageUrl: banner.bannerUrl,
                  fit: BoxFit.fill,
                  radius: 10.gw,
                  placeholder: _buildPlaceholder(),
                ),
                /// 叠加 gradient
                Container(
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Color.fromRGBO(15, 15, 15, 0.0),
                        Color.fromRGBO(3, 3, 3, 0.94),
                        Color(0xFF030303),
                      ],
                      stops: [
                        0.0,
                        0.9168,
                        1.0,
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  _buildPlaceholder({margin, isShowShadow = false}) {
    return Container(
      margin: margin,
      height: height,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFFECEFF6), Color(0xFFFEFEFF)],
        ),
        borderRadius: BorderRadius.circular(10.gw),
        boxShadow: [
          if (isShowShadow)
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 10,
              offset: const Offset(2, 2),
            ),
        ],
      ),
      child: Center(
          child: Text(
        GlobalConfig.getAppName(),
        style: TextStyle(color: const Color(0xff6A7391).withOpacity(0.1)),
      )),
    );
  }
}
