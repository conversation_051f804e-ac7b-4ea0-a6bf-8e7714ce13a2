import 'package:flutter/material.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/global_config.dart';
import 'package:wd/core/utils/screenUtil.dart';

enum CurrencySymbolIconStyle {
  primary, // 主色
  secondary,
}

class CurrencySymbolIcon extends StatelessWidget {
  CurrencySymbolIcon({
    super.key,
    this.style = CurrencySymbolIconStyle.primary,
    double? fontSize,
  }) : fontSize = fontSize ?? 9.gw;

  final double fontSize;
  final CurrencySymbolIconStyle style;

  @override
  Widget build(BuildContext context) {
    Color bgColor = context.colorTheme.borderE;
    Color textColor = context.colorTheme.tabItemBgA;

    if (style == CurrencySymbolIconStyle.secondary) {
      bgColor = context.colorTheme.tabInactive;
      textColor = context.colorTheme.textTitle;
    }

    return Container(
      padding: EdgeInsets.all(5.gw),
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: bgColor,
      ),
      alignment: Alignment.center,
      child: Text(
        GlobalConfig().systemConfig.currencySymbol,
        style: TextStyle(color: textColor, fontSize: fontSize),
      ),
    );
  }
}
