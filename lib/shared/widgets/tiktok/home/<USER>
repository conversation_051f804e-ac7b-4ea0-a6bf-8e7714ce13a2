import 'package:flutter/material.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:like_button/like_button.dart';

class TikTokRightBarWidget extends StatefulWidget {
  final bool isLiked;
  final int likeCount;
  final bool isMuteOn; // 是否静音
  final LikeButtonTapCallback onClickLike;
  final Function? onClickShare;
  final Function? onClickMute;

  const TikTokRightBarWidget({
    super.key,
    required this.isLiked,
    required this.likeCount,
    required this.isMuteOn,
    required this.onClickLike,
    this.onClickShare,
    this.onClickMute,
  });

  @override
  _VideoRightBarWidgetState createState() {
    return _VideoRightBarWidgetState();
  }
}

class _VideoRightBarWidgetState extends State<TikTokRightBarWidget> {
  final double _widgetWidth = 48.gw;

  late int _likeCount = widget.likeCount;
  late bool _isLiked = widget.isLiked;

  @override
  void initState() {
    super.initState();
  }

  @override
  void didUpdateWidget(TikTokRightBarWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.isLiked != widget.isLiked) {
      _isLiked = widget.isLiked;
      _likeCount = _isLiked ? _likeCount - 1 : _likeCount + 1;
    }
    if (oldWidget.likeCount != widget.likeCount) {
      _likeCount = widget.likeCount;
    }
  }

  @override
  Widget build(BuildContext context) {
    return ConstrainedBox(
      constraints: BoxConstraints(
        maxWidth: _widgetWidth,
      ),
      child: Column(
        children: [
          _getLikeButton(),
          SizedBox(height: 30.gw),
          _getShareButton(),
          SizedBox(height: 30.gw),
          _getMuteButton(),
        ],
      ),
    );
  }

  //获取点赞按钮
  _getLikeButton() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        LikeButton(
            onTap: (isLiked) async {
              final flag = await widget.onClickLike(isLiked);
              if (flag == true) {
                setState(() {
                  _isLiked = !_isLiked;
                  _likeCount = isLiked ? _likeCount - 1 : _likeCount + 1;
                });
              }
              return flag;
            },
            size: 36.gw,
            isLiked: _isLiked,
            circleColor: const CircleColor(start: ColorRes.color_3, end: ColorRes.color_3),
            likeBuilder: (isLike) {
              return isLike == true
                  ? Image.asset('assets/images/tiktok/btn_like_heart.png')
                  : Image.asset(
                      'assets/images/tiktok/btn_like_heart.png',
                      color: Colors.white,
                    );
            },
            bubblesColor: const BubblesColor(
              dotPrimaryColor: ColorRes.color_3,
              dotSecondaryColor: ColorRes.color_3,
              dotThirdColor: ColorRes.color_3,
              dotLastColor: ColorRes.color_3,
            )),
        const SizedBox(height: 2),
        Text(
          _likeCount.likesString(),
          style: TextStyle(color: Colors.white, fontSize: 12.fs),
        )
      ],
    );
  }

  //获取分享按钮
  _getShareButton() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        IconButton(
          iconSize: _widgetWidth,
          padding: const EdgeInsets.all(0.0),
          onPressed: () {
            widget.onClickShare?.call();
          },
          icon: Image.asset(
            'assets/images/tiktok/btn_share.png',
            width: 36.gw,
            height: 30.gw,
          ),
        ),
        Text(
          '分享',
          style: TextStyle(color: Colors.white, fontSize: 12.fs),
        )
      ],
    );
  }

  //获取静音按钮
  _getMuteButton() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        IconButton(
          iconSize: _widgetWidth,
          padding: const EdgeInsets.all(0.0),
          onPressed: () {
            widget.onClickMute?.call();
          },
          icon: Image.asset(
            'assets/images/tiktok/btn_video_mute_${widget.isMuteOn ? 'on' : 'off'}.png',
            width: 36.gw,
            height: 36.gw,
          ),
        ),
        Text(
          widget.isMuteOn ? '音量关' : '音量开',
          style: TextStyle(color: Colors.white, fontSize: 12.fs),
        )
      ],
    );
  }
}

class ColorRes {
  // static const Color color_1 = Color.fromARGB(255, 21, 23, 35);
  // static const Color color_2 = Color.fromARGB(150, 255, 255, 255);
  static const Color color_3 = Color(0xffd0316c);
// static const Color color_4 = Color.fromARGB(255, 250, 206, 21);
}
