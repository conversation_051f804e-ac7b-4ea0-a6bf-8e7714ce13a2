import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/constants/constants.dart';
import 'package:wd/core/utils/clipboardTool.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/global_config.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/system_util.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'dart:ui' as ui;

import '../../../features/page/4_mine/promotion_rewards/promotion_rewards_cubit.dart';
import '../../../features/page/4_mine/promotion_rewards/promotion_rewards_state.dart';

class PromotionSharePage extends StatefulWidget {
  const PromotionSharePage({super.key});

  @override
  State<StatefulWidget> createState() => _PromotionSharePageState();
}

class _PromotionSharePageState extends State<PromotionSharePage>
    with SingleTickerProviderStateMixin {
  final GlobalKey _globalKey = GlobalKey();
  bool _excludeFromScreenshot = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      key: _globalKey,
      child: BlocBuilder<PromotionRewardsCubit, PromotionRewardsState>(
        builder: (context, state) {
          return Stack(
            children: [
              Container(
                width: 1.gsw - 40.gw,
                height: (1.gsw - 40.gw) * 489 / 400,
                decoration: const BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage("assets/images/share/bg_share.jpg"),
                    fit: BoxFit.cover,
                    alignment: Alignment.center,
                  ),
                  borderRadius: BorderRadius.all(Radius.circular(20)),
                ),
              ),
              _defaultShare(state),
            ],
          );
        },
      ),
    );
  }

  Padding _defaultShare(PromotionRewardsState state) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 13.fs),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(height: 150.gw),
          Image.asset(
            "assets/images/share/icon_invite_tips.png",
            width: 247.gw,
            height: 90.gw,
          ),
          SizedBox(height: 10.gw),
          _getQrCodeSection(userInviteLink: state.userInviteLink),
          SizedBox(height: 19.gw),
          _getOperationSection(userInviteLink: state.userInviteLink),
          SizedBox(height: 25.gw),
          Image.asset(
            "assets/images/share/banner_slogan.png",
            width: 350.gw,
            height: 95.gw,
          ),
        ],
      ),
    );
  }

  // 二维码模块
  _getQrCodeSection({required String? userInviteLink, bool isVideo = true}) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(10.gw), // 和 BoxDecoration 一致
      child: BackdropFilter(
        filter: ui.ImageFilter.blur(sigmaX: 24.0, sigmaY: 24.0),
        child: Container(
          width: isVideo ? 164.gw : 142.gw,
          height: isVideo ? 191.gw : 163.gw,
          padding: EdgeInsets.fromLTRB(10.gw, 10.gw, 10.gw, 0),
          decoration: BoxDecoration(
            color: const Color(0x36363675),
            borderRadius: BorderRadius.all(Radius.circular(10.gw)),
          ),
          child: Column(
            children: [
              QrImageView(
                padding: EdgeInsets.zero,
                data: userInviteLink ?? kAppDownloadLinkStr,
                eyeStyle: const QrEyeStyle(
                  eyeShape: QrEyeShape.square,
                  color: Colors.white,
                ),
                embeddedImageStyle: QrEmbeddedImageStyle(
                  size: Size(42.gw, 42.gw),
                ),
              ),
              Expanded(
                child: Center(
                  child: AutoSizeText(
                    "扫一扫下载${GlobalConfig.getAppName()}APP",
                    softWrap: false,
                    minFontSize: 8.fs,
                    style: TextStyle(fontSize: 13.fs, color: Colors.black),
                  ),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  // 可操作模块：图片分享、复制链接
  _getOperationSection({required String? userInviteLink, Gradient? gradient}) {
    buildGradientBtn(String title, GestureTapCallback onTap) {
      return InkWell(
        onTap: onTap,
        child: Container(
          height: 40.gw,
          decoration: BoxDecoration(
            gradient: gradient ??
                const LinearGradient(
                  colors: [
                    Color(0xFFEACA9F), // 渐变色3
                    Color(0xFFB9936D), // 渐变色4
                  ],
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2), // 阴影颜色和透明度
                offset: Offset(0, 4.gw), // 阴影偏移 (水平, 垂直)
                blurRadius: 4.gw, // 阴影模糊半径
              ),
            ],
            borderRadius: BorderRadius.circular(20.gw), // 按钮圆角
          ),
          child: Center(
            child: Text(
              title,
              style: TextStyle(
                fontSize: 18.45.fs, // 字体大小
                color: Colors.white, // 文字颜色
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      );
    }

    return Opacity(
      opacity: _excludeFromScreenshot ? 0 : 1,
      child: Row(
        children: [
          Expanded(child: buildGradientBtn("图片分享", () => _capturePng())),
          SizedBox(width: 22.gw),
          Expanded(
            child: buildGradientBtn(
                "复制链接",
                () => ClipboardTool.setData(
                    userInviteLink ?? kAppDownloadLinkStr)),
          ),
        ],
      ),
    );
  }

  Future<void> _capturePng() async {
    setState(() {
      _excludeFromScreenshot = true;
    });

    await Future.delayed(const Duration(milliseconds: 100));

    try {
      RenderRepaintBoundary boundary = _globalKey.currentContext!
          .findRenderObject() as RenderRepaintBoundary;
      var image = await boundary.toImage(pixelRatio: 3.0);
      ByteData? byteData =
          await image.toByteData(format: ui.ImageByteFormat.png);
      Uint8List pngBytes = byteData!.buffer.asUint8List();

      SystemUtil.saveImage(pngBytes);
    } catch (e) {
      debugPrint(e.toString());
    }

    setState(() {
      _excludeFromScreenshot = false;
    });
  }
}
