import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/constants/constants.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:wd/core/utils/clipboardTool.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/system_util.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'dart:ui' as ui;
import 'package:wd/core/theme/themes.dart';

import '../../../features/page/4_mine/promotion_rewards/promotion_rewards_cubit.dart';
import '../../../features/page/4_mine/promotion_rewards/promotion_rewards_state.dart';

class PromotionSharePage extends StatefulWidget {
  const PromotionSharePage({super.key});

  @override
  State<StatefulWidget> createState() => _PromotionSharePageState();
}

class _PromotionSharePageState extends State<PromotionSharePage>
    with SingleTickerProviderStateMixin {
  final GlobalKey _globalKey = GlobalKey();
  bool _excludeFromScreenshot = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      key: _globalKey,
      child: BlocBuilder<PromotionRewardsCubit, PromotionRewardsState>(
        builder: (context, state) {
          return Stack(
            children: [
              Container(
                width: 1.gsw - 40.gw,
                height: (1.gsw - 40.gw) * 489 / 400,
                decoration: const BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage("assets/images/share/bg_share.jpg"),
                    fit: BoxFit.cover,
                    alignment: Alignment.center,
                  ),
                  borderRadius: BorderRadius.all(Radius.circular(20)),
                ),
              ),
              _defaultShare(state),
            ],
          );
        },
      ),
    );
  }

  Padding _defaultShare(PromotionRewardsState state) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 13.fs),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(height: 60.gw),
          Text('act_invite_friends_title'.tr(),
              textAlign: TextAlign.center,
              style: context.textTheme.secondary.fs24.w700),
          SizedBox(height: 12.gw),
          Text(
            'act_invite_friends_desc'.tr(),
            textAlign: TextAlign.center,
            style: context.textTheme.secondary,
          ),
          SizedBox(height: 32.gw),
          _getQrCodeSection(userInviteLink: state.userInviteLink),
          SizedBox(height: 80.gw),
          _getOperationSection(userInviteLink: state.userInviteLink),
          SizedBox(height: 25.gw),
          Container(
            width: double.infinity,
            height: 90.gw,
            padding: EdgeInsets.symmetric(horizontal: 24.gw),
            decoration: ShapeDecoration(
              color: const Color(0xFF161616),
              shape: RoundedRectangleBorder(
                side: const BorderSide(
                  width: 1,
                  color: Color(0xFF202020),
                ),
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  'act_easy_earnings_desc'.tr(),
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: const Color(0xFFB4B3B3),
                    fontSize: 20.fs,
                    fontWeight: FontWeight.w500,
                    height: 1.40,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 二维码模块
  _getQrCodeSection({required String? userInviteLink}) {
    return Container(
      width: 210.gw,
      height: 272.gw,
      padding: EdgeInsets.all(16.gw),
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage("assets/images/share/qr_bg.png"),
          fit: BoxFit.cover,
          alignment: Alignment.center,
        ),
      ),
      child: Column(
        children: [
          QrImageView(
            padding: EdgeInsets.zero,
            data: userInviteLink ?? kAppDownloadLinkStr,
            eyeStyle: const QrEyeStyle(
              eyeShape: QrEyeShape.square,
              color: Colors.white,
            ),
            dataModuleStyle: const QrDataModuleStyle(
              dataModuleShape: QrDataModuleShape.square,
              color: Colors.white,
            ),
            embeddedImageStyle: QrEmbeddedImageStyle(
              size: Size(42.gw, 42.gw),
            ),
          ),
          SizedBox(height: 14.gw),
          Expanded(
            child: Center(
              child: AutoSizeText(
                "${'act_scan_to_download'.tr()} APP", // ${GlobalConfig.getAppName()}
                softWrap: false,
                style: context.textTheme.secondary,
              ),
            ),
          )
        ],
      ),
    );
  }

  // 可操作模块：图片分享、复制链接
  _getOperationSection({required String? userInviteLink}) {
    return Opacity(
      opacity: _excludeFromScreenshot ? 0 : 1,
      child: Row(
        children: [
          Expanded(child: buildGradientBtn("", () => _capturePng())),
          SizedBox(width: 22.gw),
          Expanded(
            child: buildGradientBtn2(
              "",
              () => ClipboardTool.setData(
                userInviteLink ?? kAppDownloadLinkStr,
              ),
            ),
          ),
        ],
      ),
    );
  }

  buildGradientBtn(String title, GestureTapCallback onTap) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: 192.gw,
        padding: const EdgeInsets.all(16),
        decoration: ShapeDecoration(
          color: const Color(0xFFFFD038),
          shape: RoundedRectangleBorder(
            side: const BorderSide(
              width: 1,
              color: Color(0xFFFFE157),
            ),
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              'act_download_image'.tr(),
              style: TextStyle(
                color: const Color(0xFF030303),
                fontSize: 14.fs,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  buildGradientBtn2(String title, GestureTapCallback onTap) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: 192,
        padding: const EdgeInsets.all(16),
        decoration: ShapeDecoration(
          color: const Color(0xFF4F58A7),
          shape: RoundedRectangleBorder(
            side: const BorderSide(
              width: 1,
              color: Color(0xFF5F69C1),
            ),
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              'act_copy_link'.tr(),
              style: TextStyle(
                color: Colors.white,
                fontSize: 14.fs,
                fontFamily: 'Anek Devanagari',
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _capturePng() async {
    setState(() {
      _excludeFromScreenshot = true;
    });

    await Future.delayed(const Duration(milliseconds: 100));

    try {
      RenderRepaintBoundary boundary = _globalKey.currentContext!
          .findRenderObject() as RenderRepaintBoundary;
      var image = await boundary.toImage(pixelRatio: 3.0);
      ByteData? byteData =
          await image.toByteData(format: ui.ImageByteFormat.png);
      Uint8List pngBytes = byteData!.buffer.asUint8List();

      SystemUtil.saveImage(pngBytes);
    } catch (e) {
      debugPrint(e.toString());
    }

    setState(() {
      _excludeFromScreenshot = false;
    });
  }
}
