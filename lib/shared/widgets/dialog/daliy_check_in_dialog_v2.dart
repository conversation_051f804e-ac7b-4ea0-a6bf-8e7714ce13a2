import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:wd/core/constants/base64_image.dart';
import 'package:wd/core/models/entities/daily_check_in_entity.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/features/page/2_activity/activity_list_cubit.dart';
import 'package:wd/features/page/2_activity/activity/check_in_item_widget.dart';
import 'package:wd/shared/widgets/hot_push_image.dart';

class DailyCheckInDialogV2 {
  final void Function(DailyCheckInItem model) onClickCheckIn;

  DailyCheckInDialogV2({
    required this.onClickCheckIn,
  });

  show(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return _DailyCheckInDialogContent(
          onClickCheckIn: onClickCheckIn,
        );
      },
    );
  }
}

class _DailyCheckInDialogContent extends StatefulWidget {
  final void Function(DailyCheckInItem model) onClickCheckIn;

  const _DailyCheckInDialogContent({
    required this.onClickCheckIn,
  });

  @override
  State<StatefulWidget> createState() => _DailyCheckInDialogContentState();
}

class _DailyCheckInDialogContentState
    extends State<_DailyCheckInDialogContent> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return IntrinsicHeight(
      child: Center(
        child: Container(
          color: Colors.black,
          width: MediaQuery.of(context).size.width - 40, // 80% of screen width
          child: _buildDialogBox(context),
        ),
      ),
    );
  }

  Widget _buildDialogBox(BuildContext context) {
    return BlocBuilder<ActivityListCubit, ActivityListState>(
      builder: (context, state) {
        if (state.checkInModel == null) return Container();

        return Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            _buildTotalDays(state.checkInModel!.totalDays.toString()),
            SizedBox(height: 48.gw),
            _buildContent(context, list: state.checkInModel!.fullList),
          ],
        );
      },
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Text(
          'act_daily_check_in'.tr(),
          style: TextStyle(
            color: Colors.white,
            fontSize: 20.fs,
            fontWeight: FontWeight.w500,
          ),
        ),
        const Spacer(),
        InkWell(
          onTap: Navigator.of(context).pop,
          child: Container(
            width: 60.gw,
            height: 40.gw,
            alignment: Alignment.center,
            child: HotPushImage(
              imagePath: "assets/images/check_in/btn_check_in_dialog_close.png",
              width: 28.gw,
              height: 28.gw,
            ),
          ),
        )
      ],
    );
  }

  Widget _buildTotalDays(String total) {
    return Text(
      '${'act_u_have_signed_for'.tr()} $total ${'act_day'.tr()}',
      style: TextStyle(
        color: const Color(0xFFB4B3B3),
        fontSize: 14.fs,
        fontWeight: FontWeight.w400,
      ),
    );
  }

  Widget _buildContent(
    BuildContext context, {
    required List<DailyCheckInItem> list,
  }) {
    return GridView.builder(
      shrinkWrap: true,
      padding: EdgeInsets.only(bottom: 10.gw),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 6,
        mainAxisSpacing: 5.gw,
        crossAxisSpacing: 5.gw,
        childAspectRatio: 56 / 75,
      ),
      itemBuilder: (context, index) {
        final e = list[index];
        return CheckInItemWidget(
          model: e,
          onClickCheckIn: widget.onClickCheckIn,
        );
      },
      itemCount: list.length,
    );
  }
}
