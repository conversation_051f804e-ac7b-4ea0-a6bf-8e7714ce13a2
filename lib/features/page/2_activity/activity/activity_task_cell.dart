import 'package:flutter/material.dart';
import 'package:wd/core/models/entities/activity_list_entity.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/shared/widgets/common_progress_bar.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

class ActivityTaskCell extends StatelessWidget {
  final ActivityTask model;

  /// 点击做任务
  final VoidCallback onClickDoTask;

  /// 点击完成任务
  final VoidCallback onClickCompleteTask;

  const ActivityTaskCell({
    super.key,
    required this.model,
    required this.onClickDoTask,
    required this.onClickCompleteTask,
  });

  @override
  Widget build(BuildContext context) {
    Widget body = Container();
    if (model.subReceiveType < 10) {
      // 新人任务
      body = buildNewGuyInProcessContent(model, context);
    } else {
      body = _buildAvailableOrCompletedContent(model, context);
    }

    return Container(
      clipBehavior: Clip.hardEdge,
      decoration: ShapeDecoration(
        color: const Color(0xFF101010),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
      child: body,
    );
  }

  buildNewGuyInProcessContent(ActivityTask task, BuildContext context) {
    /// receiveStatus	领取状态 1-未完成 2-已完成未领取 3-已领取
    /// subStatus	新手活动状态 1-未开始 2-进行中 3-已结束
    /// subReceiveType 1-绑定手机号 2-绑定银行卡 3-VIP升级 4-彩票下注 5-体育下注 6-真人下注 7-电子下注 8-棋牌下注 9-捕鱼下注
    var title = 'act_task_go_to_doing'.tr();
    bool normalOrEmptyBtnBG = true;
    switch (task.subReceiveType) {
      case 1:
      case 2:
        title = 'act_task_go_to_bind'.tr();
        normalOrEmptyBtnBG = true;
      case 3:
        break;
    }
    switch (task.receiveStatus) {
      case 2:
        title = 'act_task_claimable'.tr();
        normalOrEmptyBtnBG = true;
        break;
      case 3:
        title = 'act_task_claimed'.tr();
        normalOrEmptyBtnBG = false;
        break;
    }

    if (task.subStatus == 3) {
      title = 'act_task_end'.tr();
      normalOrEmptyBtnBG = false;
    }

    return Container(
      height: 99.gw,
      padding: EdgeInsets.only(left: 7.gw, right: 16.gw),
      child: Row(
        children: [
          _buildLeft(task),
          SizedBox(width: 12.gw),
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildTitle(task.title),
                    AneText(
                      "${task.finishAmount.formattedMoney}/${task.sumAmount.formattedMoney}",
                      style: context.textTheme.title.fs16,
                    ),
                  ],
                ),
                SizedBox(height: 16.gw),
                CommonProgressBar(
                    total: task.sumAmount.toInt(), current: task.finishAmount),
                SizedBox(height: 16.gw),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    GoldAmountTextWidget(
                        amount: task.receiveAmount.formattedMoney),
                    InkWell(
                      onTap: () {
                        ///receiveStatus 领取状态 1-未完成 2-已完成未领取 3-已领取
                        switch (task.receiveStatus) {
                          case 1:
                            onClickDoTask();
                            break;
                          case 2:
                            onClickCompleteTask();
                            break;
                        }
                      },
                      child: normalOrEmptyBtnBG
                          ? normalBtnBG(title, context)
                          : emptyBtnBG(title, context),
                    )
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLeft(ActivityTask task) {
    var statusImagePath =
        "assets/images/activity/task/icon_task_new_guys_not_start.png";
    String title = 'act_task_state_not_start'.tr();
    if (task.subStatus == 2) {
      statusImagePath =
          "assets/images/activity/task/icon_task_new_guys_progress.png";
      title = 'act_task_state_progress'.tr();
    }
    if (task.subStatus == 3) {
      statusImagePath =
          "assets/images/activity/task/icon_task_new_guys_end.png";
      title = 'act_task_state_completed'.tr();
    }

    return Stack(
      alignment: AlignmentDirectional.bottomCenter,
      children: [
        SizedBox(width: 69.gw, height: 85),
        Image.asset(
          "assets/images/activity/task/icon_task_${task.subReceiveType}.png",
          width: 59.gw,
          height: 75.gw,
        ),
        Image.asset(
          statusImagePath,
          width: 69.gw,
          height: 30.gw,
        ),
        Positioned(
          bottom: 7.gw,
          child: Text(
            title,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 8,
              fontWeight: FontWeight.w500,
            ),
          ),
        )
      ],
    );
  }

  _buildTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        color: Colors.white,
        fontSize: 16,
        fontWeight: FontWeight.w400,
      ),
    );
  }

  /// 可领取/已领取
  _buildAvailableOrCompletedContent(ActivityTask task, BuildContext context) {
    var title = 'act_task_go_to_doing'.tr();
    bool normalOrEmptyBtnBG = true;
    switch (task.receiveStatus) {
      case 2:
        title = 'act_task_claimable'.tr();
        normalOrEmptyBtnBG = true;
        break;
      case 3:
        title = 'act_task_claimed'.tr();
        normalOrEmptyBtnBG = false;
        break;
    }

    return Padding(
      padding: EdgeInsets.all(16.gw),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: AneText(
                  task.title,
                  style: context.textTheme.title.fs16,
                ),
              ),
              SizedBox(width: 5.gw),
              emptyCoinBG(
                GoldAmountTextWidget(
                  amount: task.receiveAmount.formattedMoney,
                ), context
              ),
            ],
          ),
          SizedBox(height: 11.gw),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    emptyTimeBG(
                        'act_task_time_start'.tr(), task.beginTime, context),
                    SizedBox(height: 8.gw),
                    emptyTimeBG(
                        'act_task_time_end'.tr(), task.endTime, context),
                  ],
                ),
              ),
              SizedBox(width: 8.fs),
              Column(
                children: [
                  Image.asset(
                    "assets/images/activity/task/bag_cions.png",
                    width: 102.gw,
                    height: 75.gw,
                    fit: BoxFit.cover,
                  ),
                  InkWell(
                    onTap: () {
                      if (model.receiveStatus == 1) {
                        onClickDoTask();
                      } else if (model.receiveStatus == 2) {
                        onClickCompleteTask();
                      }
                    },
                    child: normalOrEmptyBtnBG
                        ? normalBtnBG(title, context)
                        : emptyBtnBG(title, context),
                  )
                ],
              ),
            ],
          )
        ],
      ),
    );
  }

  Widget normalBtnBG(String title, BuildContext context) {
    return Container(
      height: 25.gw,
      padding: EdgeInsets.symmetric(horizontal: 12.gw),
      decoration: ShapeDecoration(
        color: context.colorTheme.btnBgPrimary,
        shape: RoundedRectangleBorder(
          side: const BorderSide(
            width: 1,
            color: Color(0xFFFFE157),
          ),
          borderRadius: BorderRadius.circular(24),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            title,
            style: const TextStyle(
              color: Color(0xFF030303),
              fontSize: 12,
              fontFamily: 'Inter',
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget emptyBtnBG(String title, BuildContext context) {
    return Container(
      height: 25.gw,
      padding: EdgeInsets.symmetric(horizontal: 12.gw),
      decoration: ShapeDecoration(
        color: const Color(0xFF6E5502),
        shape: RoundedRectangleBorder(
          side: const BorderSide(
            width: 1,
            color: Color(0xFFC09404),
          ),
          borderRadius: BorderRadius.circular(24),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(title, style: context.textTheme.primary.fs12.w500),
        ],
      ),
    );
  }

  Widget emptyCoinBG(Widget child, BuildContext context) {
    return Container(
      height: 22.gw,
      padding: EdgeInsets.only(left: 4.gw, right: 8.gw),
      decoration: ShapeDecoration(
        color: const Color(0xFF202020),
        shape: RoundedRectangleBorder(
          side: const BorderSide(
            width: 1,
            color: Color(0xFF494843),
          ),
          borderRadius: BorderRadius.circular(24),
        ),
      ),
      child: child,
    );
  }

  Widget emptyTimeBG(String title, String time, BuildContext context) {
    final dateTime = DateTime.parse(time);
    final formattedDate = DateFormat('yyyy-MM-dd').format(dateTime);
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF161616),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: context.colorTheme.borderA,
          width: 1,
        ),
      ),
      child: Container(
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
            colors: [Color(0x33F7DD33), Colors.transparent],
            stops: [0.0, 0.16],
          ),
          borderRadius: BorderRadius.circular(6),
        ),
        child: Padding(
          padding: const EdgeInsets.all(8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(title, style: context.textTheme.primary.fs16.w500),
                  AneText(
                    formattedDate,
                    style: context.textTheme.title,
                  ),
                ],
              ),
              timeHMS(time, context),
            ],
          ),
        ),
      ),
    );
  }

  Widget timeHMS(String beginTime, BuildContext context) {
    final dateTime = DateTime.parse(beginTime);
    String hourStr = dateTime.hour.toString().padLeft(2, '0');
    String minuteStr = dateTime.minute.toString().padLeft(2, '0');
    String secondStr = dateTime.second.toString().padLeft(2, '0');
    return Row(
      children: [
        Stack(
          alignment: AlignmentDirectional.centerEnd,
          children: [
            Container(
              width: 24.gw,
              height: 24.gw,
              decoration: ShapeDecoration(
                color: const Color(0xFF202020),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4)),
              ),
              child: Center(
                child: Text(
                  hourStr,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
            ),
            Container(
              width: 1,
              height: 5,
              decoration: ShapeDecoration(
                color: const Color(0xFFFFE157),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(1)),
              ),
            ),
          ],
        ),
        SizedBox(width: 4.gw),
        Stack(
          alignment: AlignmentDirectional.centerEnd,
          children: [
            Container(
              width: 24.gw,
              height: 24.gw,
              decoration: ShapeDecoration(
                color: const Color(0xFF202020),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4)),
              ),
              child: Center(
                child: Text(
                  minuteStr,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
            ),
            Positioned(
              left: 0,
              child: Container(
                width: 1,
                height: 5,
                decoration: ShapeDecoration(
                  color: const Color(0xFFFFE157),
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(1)),
                ),
              ),
            ),
            Positioned(
              right: 0,
              child: Container(
                width: 1,
                height: 5,
                decoration: ShapeDecoration(
                  color: const Color(0xFFFFE157),
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(1)),
                ),
              ),
            ),
          ],
        ),
        SizedBox(width: 4.gw),
        Stack(
          alignment: AlignmentDirectional.centerStart,
          children: [
            Container(
              width: 24.gw,
              height: 24.gw,
              decoration: ShapeDecoration(
                color: const Color(0xFF202020),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4)),
              ),
              child: Center(
                child: Text(
                  secondStr,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
            ),
            Container(
              width: 1,
              height: 5,
              decoration: ShapeDecoration(
                color: const Color(0xFFFFE157),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(1)),
              ),
            ),
          ],
        ),
      ],
    );
  }
}

class GoldAmountTextWidget extends StatelessWidget {
  final String amount;

  const GoldAmountTextWidget({super.key, required this.amount});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Image.asset(
          "assets/images/activity/icon_amount_gold.png",
          width: 14.gw,
          height: 14.gw,
        ),
        const SizedBox(width: 4),
        Text(
          "$amount ${'act_coin'.tr()}",
          style: TextStyle(color: Colors.white, fontSize: 12.fs),
        ),
      ],
    );
  }
}
