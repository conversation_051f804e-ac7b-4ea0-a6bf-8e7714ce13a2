import 'package:collection/collection.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/models/entities/daily_check_in_entity.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/singletons/user_state.dart';
import 'package:wd/core/utils/auth_util.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/features/page/2_activity/sub/activity_game_list_page.dart';
import 'package:wd/features/page/2_activity/sub/activity_task_list_page.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/features/page/2_activity/activity/activity_sign_in_widget.dart';
import 'package:wd/shared/widgets/common_dialog.dart';
import 'package:wd/shared/widgets/common_tabbar.dart';
import 'package:wd/shared/widgets/dialog/daliy_check_in_dialog_v2.dart';

import 'activity_list_cubit.dart';

class ActivityScreen extends StatefulWidget {
  const ActivityScreen({super.key});

  @override
  State<ActivityScreen> createState() => _ActivityScreenState();
}

class _ActivityScreenState extends State<ActivityScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  int currentTabIndex = 0;
  final List<CommonTabBarItem> tabList = [
    CommonTabBarItem(title: "game_activity".tr(), imageUrl: "assets/images/activity/icon_activity_game.png"),
    CommonTabBarItem(title: "self_service_claim".tr(), imageUrl: "assets/images/activity/icon_activity_self_claim.png"),
  ];
  final PageController _pageController = PageController();

  @override
  void initState() {
    super.initState();

    _tabController = TabController(length: tabList.length, vsync: this)
      ..addListener(() {

      });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ActivityListCubit>().refreshData();
    });
  }
  _onChangeTabIndex(int index) {
    _updateCurrentTabIndex(index);
    if (!sl<UserCubit>().state.isLogin && index == 1) {
      // 弹出登录框，登录成功后再切换
      AuthUtil.checkIfLogin(() {
        // 登录成功后再切换到tab 1
        _updateCurrentTabIndex(1);
        _pageController.jumpToPage(1);
      });
      // 先将tab切回index 0
      _updateCurrentTabIndex(0);
      return;
    }
    _pageController.jumpToPage(index);
  }

  _updateCurrentTabIndex(int index) {
    if (currentTabIndex != index) {
      setState(() {
        currentTabIndex = index;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<UserCubit, UserState>(
      listenWhen: (previous, current) => current.isLogin != previous.isLogin,
      listener: (context, state) => context.read<ActivityListCubit>().refreshData(),
      child: Scaffold(
        body: Column(
          children: [
            /// 签到模块
            BlocBuilder<ActivityListCubit, ActivityListState>(
              builder: (context, state) {
                if (state.checkInModel == null) {
                  return SizedBox(height: MediaQuery.of(context).padding.top);
                }

                return ActivitySignInWidget(
                  model: state.checkInModel!,
                  totalGoldEarned: state.totalGoldEarned,
                  netState: state.checkInListNetState,
                  onClickCheckIn: onClickCheckIn,
                  onClickMoreList: () {
                    AuthUtil.checkIfLogin(() {
                      DailyCheckInDialogV2(onClickCheckIn: onClickCheckIn).show(context);
                    });
                  },
                  onClickGoRecordsList: () {
                    AuthUtil.checkIfLogin(() {
                      sl<NavigatorService>().push(AppRouter.activityRecords);
                    });
                  },
                );
              },
            ),
            SizedBox(height: 0.gw),
            /// 活动模块
            BlocBuilder<ActivityListCubit, ActivityListState>(builder: (context, state) {
              return _buildContent(state);
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildContent(ActivityListState state) {
    final cubit = context.read<ActivityListCubit>();
    return Expanded(
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.gw),
            child: CommonTabBar(
              tabList,
              isScrollable: false,
              onTap: _onChangeTabIndex,
              currentIndex: currentTabIndex,
            ),
          ),
          SizedBox(height: 5.gw),
          Expanded(
            child: PageView(
              controller: _pageController,
              physics: const NeverScrollableScrollPhysics(),
              onPageChanged: (index) => cubit.onChangeTabIndex(index),
              children: [
                ActivityGameListPage(
                    key: const PageStorageKey("ActivityGameListPage"),
                    dataList: state.gameCategoryList,
                    index: state.currentGameTabIndex,
                    netState: state.gameNetState,
                    refreshMethod: () => cubit.fetchGameActivityCategoryList(),
                    onFetch: (viewModel) => cubit.fetchGameListData(viewModel),
                    onChangeTabIndex: (index) => cubit.onChangeGameTabIndex(index),
                    onClickCell: (model) {
                      AuthUtil.checkIfLogin(() {
                        sl<NavigatorService>().push(AppRouter.activityDetail, arguments: model);
                      });
                    }),
                ActivityTaskListPage(
                  key: const PageStorageKey("ActivityTaskListPage"),
                  dataList: state.taskCategoryList,
                  index: state.currentTaskTabIndex,
                  netState: state.taskNetState,
                  refreshMethod: () => cubit.fetchTaskCategoryList(),
                  onFetch: (viewModel) => cubit.fetchTaskListData(viewModel),
                  onChangeTabIndex: (index) => cubit.onChangeTaskTabIndex(index),
                  onClickCell: (model) {
                    AuthUtil.checkIfLogin(() {
                      sl<NavigatorService>().push(AppRouter.activityDetail, arguments: model);
                    });
                  },
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  onClickCheckIn(DailyCheckInItem model) {
    final cubit = context.read<ActivityListCubit>();
    DailyCheckInItem? needBackdateModel =
        cubit.state.checkInModel!.fullList.firstWhereOrNull((day) => day.signInState == SignInState.needBackdate.value);
    AuthUtil.checkIfLogin(() {
      if (model.signInState == SignInState.unsigned.value && needBackdateModel != null) {
        _showBackdateConfirmDialog(cubit, model);
      } else {
        cubit.onClickCheckBtn(context, model);
      }
    });
  }

  void _showBackdateConfirmDialog(ActivityListCubit cubit, DailyCheckInItem model) {
    CommonDialog.show(
      context: context,
      title: 'common_tips'.tr(),
      content: 'act_make_up_tips_detail'.tr(),
      cancelBtnTitle: 'act_make_up_tips_comfirm'.tr(),
      sureBtnTitle: 'act_make_up_tips_cancel'.tr(),
      complete: () => cubit.confirmClearCheckInInfo(model),
    );
  }
}
