import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/base/common_refresher.dart';
import 'package:wd/core/models/entities/platform_amount_entity.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/singletons/user_state.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/shared/widgets/app_image.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/common_card_page.dart';
import 'package:wd/shared/widgets/header_content_card.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

import 'my_wallet_cubit.dart';
import 'my_wallet_state.dart';

/// 我的钱包
class MyWalletPage extends BasePage {
  const MyWalletPage({super.key});

  @override
  BasePageState<BasePage> getState() => _MyWalletPageState();
}

class _MyWalletPageState extends BasePageState<MyWalletPage> with SingleTickerProviderStateMixin {
  late AnimationController animationController;

  /// 刷新组件控制器
  final RefreshController refreshController = RefreshController(initialRefresh: false);

  @override
  void initState() {
    super.initState();
    pageTitle = "我的钱包";
    isNeedEmptyDataWidget = false;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          pageBgColor = Theme.of(context).scaffoldBackgroundColor;
        });
      }
    });
    animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _getData();
  }

  @override
  void dispose() {
    animationController.dispose();
    refreshController.dispose();
    super.dispose();
  }

  void _getData() {
    context.read<MyWalletCubit>().loadWallets();
  }

  List<Map<String, dynamic>> _getWalletActionList() {
    return [
      {
        'title': 'deposit'.tr(),
        'icon': 'assets/images/mine/wallet/deposit.png',
        'onCall': () => context.read<MyWalletCubit>().onGoToDepositPage(),
        'left': 0.0,
        'right': 8.0,
      },
      {
        'title': 'withdraw'.tr(),
        'icon': 'assets/images/mine/wallet/withdraw.png',
        'onCall': () => context.read<MyWalletCubit>().onGoToWithdrawPage(),
        'left': 0.0,
        'right': 0.0,
      },
      {
        'title': 'bills'.tr(),
        'icon': 'assets/images/mine/wallet/bills.png',
        'onCall': () => context.read<MyWalletCubit>().onGoToTransferPage(),
        'left': 8.0,
        'right': 0.0,
      },
    ];
  }

  Widget _getWalletActionItem(BuildContext context, Map<String, dynamic> item) {
    return Expanded(
      child: GestureDetector(
        onTap: item['onCall'],
        child: Container(
          margin: EdgeInsets.only(left: item['left'], right: item['right']),
          padding: EdgeInsets.only(bottom: 8.gw),
          decoration: BoxDecoration(
            color: context.theme.cardColor,
            borderRadius: BorderRadius.circular(5),
            border: Border(
              bottom: BorderSide( 
                color: Colors.white.withOpacity(0.05),
                width: 0.5,
                strokeAlign: BorderSide.strokeAlignInside,
              ),
            ),
          ),
          child: Column(
            children: [
              AppImage(
                imageUrl: item['icon'],
                height: 100.gw,
                width: 100.gw,
              ),
              const SizedBox(height: 5),
              Text(
                item['title'],
                style: Theme.of(context).textTheme.displaySmall?.copyWith(fontSize: 12),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _getRMBAssets(BuildContext context, MyWalletState state) {
    return AnimationLimiter(
      child: Column(
        children: AnimationConfiguration.toStaggeredList(
          duration: const Duration(milliseconds: 375),
          childAnimationBuilder: (widget) => SlideAnimation(
            horizontalOffset: 50.0,
            child: FadeInAnimation(
              child: widget,
            ),
          ),
          children: [
            Container(
              height: 120,
              width: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 20.gw),
              decoration: const BoxDecoration(
                image: DecorationImage(
                  image: AssetImage('assets/images/mine/wallet/asset_bg.png'),
                  fit: BoxFit.cover,
                ),
              ),
              child: Stack(
                alignment: Alignment.centerLeft,
                children: [
                  Positioned(
                    right: 0,
                    child: AppImage(
                      imageUrl: 'assets/images/mine/wallet/asset_avatar.png',
                      fit: BoxFit.cover,
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Row(
                        children: [
                          AppImage(
                            imageUrl: 'assets/images/mine/wallet/asset_coins.png',
                            fit: BoxFit.cover,
                          ),
                          SizedBox(width: 10.gw),
                          AneText(
                            'my_assets'.tr(),
                            style: context.textTheme.regular,
                          )
                        ],
                      ),
                      SizedBox(height: 10.gw),
                      BlocSelector<UserCubit, UserState, String?>(
                        selector: (state) => state.balanceInfo?.accountMoney.formattedMoney,
                        builder: (context, balance) {
                          return AneText(
                            balance ?? '-', // 使用state中的balance
                            style: context.textTheme.secondary.w500.fs25,
                          );
                        },
                      ),
                    ],
                  )
                ],
              ),
            ),
            SizedBox(height: 16.gw),
            Row(
              children: _getWalletActionList().map((e) => _getWalletActionItem(context, e)).toList(),
            ),
            SizedBox(height: 16.gw),
          ],
        ),
      ),
    );
  }

  Widget _getListView(BuildContext context, MyWalletState state) {
    if (state.isFirstFetchingAll) {
      return const Expanded(child: Center(child: CircularProgressIndicator()));
    }

    return Expanded(
      child: CommonRefresher(
        enablePullDown: true,
        enablePullUp: false,
        refreshController: refreshController,
        onRefresh: _getData,
        onLoading: () {},
        listWidget: ListView.builder(
          itemCount: state.dataList!.length,
          itemBuilder: (context, index) {
            return _getListViewWidget(context, state.dataList![index], context.read<MyWalletCubit>(), index);
          },
        ),
      ),
    );
  }

  Widget _getListViewWidget(BuildContext context, PlatformAmountEntity model, MyWalletCubit cubit, int index) {
    final isIndexOdd = index % 2 == 1;
    final isLastIndex = index == cubit.state.dataList!.length - 1;
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 6.gw),
      decoration: BoxDecoration(
        color: isIndexOdd ? context.theme.dividerColor : context.theme.cardColor,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(isLastIndex ? 12 : 0),
          bottomRight: Radius.circular(isLastIndex ? 12 : 0),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AneText(
                    model.platformName,
                    style: context.textTheme.regular.fs15,
                  ),
                  SizedBox(height: 2.gw),
                  AneText(
                    model.amount.formattedMoney,
                    style: context.textTheme.secondary.fs20.w600,
                  ),
                ],
              ),
            ),
            GestureDetector(
                onTap: () => cubit.onRefresh(
                    animationController: animationController, channelCode: model.platformId.toString(), model: model),
                child: model.isLoading
                    ? RotationTransition(
                        turns: Tween<double>(begin: 0, end: 2).animate(animationController),
                        child: Icon(Icons.restart_alt_outlined, color: context.colorTheme.textTitle),
                      )
                    : Icon(Icons.restart_alt_outlined, color: context.colorTheme.textTitle))
          ],
        ),
      ),
    );
  }

  Widget mainPageWidget(MyWalletState state) {
    return CommonCardPage(
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 10.gw, horizontal: 15.gw),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _getRMBAssets(context, state),
            SizedBox(height: 18.gw),
            _getVenueBalance(state),
            _getListView(context, state),
          ],
        ),
      ),
    );
  }

  HeaderContentCard _getVenueBalance(MyWalletState state) {
    return HeaderContentCard(
      header: Row(
        children: [
          Expanded(
            flex: 4,
            child: AneText(
              'venue_balance'.tr(),
              style: context.textTheme.btnSecondary.fs20.w600,
            ),
          ),
          Expanded(
            flex: 3,
            child: CommonButton(
              title: "one_click_recycle".tr(),
              height: 36.gw,
              style: CommonButtonStyle.quaternary,
              radius: 20,
              backgroundColor: context.colorTheme.tabItemBgA.withOpacity(0.5),
              child: state.isFetchingTransferAll
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        AneText('one_click_recycle'.tr(), style: context.textTheme.primary),
                        SizedBox(width: 4.gw),
                        SizedBox(
                          width: 6.gw,
                          height: 6.gw,
                          child: CircularProgressIndicator(strokeWidth: 1.gw, color: Colors.white),
                        ),
                      ],
                    )
                  : AneText(
                      'one_click_recycle'.tr(),
                      style: context.textTheme.primary,
                    ),
              onPressed: () => context.read<MyWalletCubit>().onRefreshOneClick(),
            ),
          ),
        ],
      ),
      content: const SizedBox.shrink(),
    );
  }

  void _listener(BuildContext context, MyWalletState state) {
    refreshController.refreshCompleted();
    refreshController.loadComplete();
    if (state.isNoMoreDataState == true) {
      refreshController.loadNoData();
    }
  }

  @override
  Widget buildPage(BuildContext context) {
    return BlocConsumer<MyWalletCubit, MyWalletState>(
      listener: _listener,
      builder: (context, state) {
        return resultWidget(state, (baseState, context) => mainPageWidget(state), refreshMethod: () {
          _getData();
        });
      },
    );
  }
}
