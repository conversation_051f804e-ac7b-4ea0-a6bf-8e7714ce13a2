import 'package:bloc/bloc.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/models/apis/agent.dart';
import 'package:wd/core/models/entities/customer_service_config_entity.dart';

import 'agent_recruitment_state.dart';

class AgentRecruitmentCubit extends Cubit<AgentRecruitmentState> {
  AgentRecruitmentCubit() : super(const AgentRecruitmentState()) {
    fetchCustomerServiceConfig();
  }

  Future<void> fetchCustomerServiceConfig() async {
    emit(state.copyWith(netState: NetState.loadingState));
    try {
      final res = await AgentApi.fetchCustomerServiceConfig();
      emit(state.copyWith(
        data: [
          CustomerServiceConfigData()
            ..id = 1
            ..name = '客服1'
            ..type = 1
            ..link = 'https://wdclus002.com'
            ..description =
                "lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."
            ..logo = 'https://wdclus002.com',
          CustomerServiceConfigData()
            ..id = 2
            ..name = '客服2'
            ..type = 2
            ..link = 'https://wdclus002.com'
            ..description =
                "lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."
            ..logo = 'https://wdclus002.com',
          CustomerServiceConfigData()
            ..id = 3
            ..name = '客服3'
            ..type = 1
            ..link = 'https://wdclus002.com'
            ..description =
                "lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua."
            ..logo = 'https://wdclus002.com',
        ],
        netState: NetState.dataSuccessState,
      ));
    } on Exception catch (_) {
      emit(state.copyWith(
        netState: NetState.error404State,
      ));
    }
  }
}
