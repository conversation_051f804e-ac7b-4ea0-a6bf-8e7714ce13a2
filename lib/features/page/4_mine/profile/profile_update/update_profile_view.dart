import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/easy_loading.dart';
import 'package:wd/shared/widgets/sheet/fund_pwd_sheet.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';
import '../../../../../core/constants/assets.dart';
import '../../../../../shared/widgets/icon_textfield.dart';
import 'phone_update/phone_update_cubit.dart';
import 'phone_update/phone_update_view.dart';
import 'update_profile_cubit.dart';
import 'update_profile_state.dart';

class UpdateProfileView extends StatelessWidget {
  final String initialValue;
  final UserFieldType field;

  const UpdateProfileView({
    super.key,
    required this.initialValue,
    required this.field,
  });

  @override
  Widget build(BuildContext context) {
    final title = _getAppBarTitle(field);
    return MultiBlocProvider(
      providers: [
        BlocProvider(
            create: (context) => UpdateProfileCubit(initialValue, field)),
        BlocProvider(create: (context) => PhoneUpdateCubit()),
      ],
      child: Scaffold(
        backgroundColor: context.theme.scaffoldBackgroundColor,
        appBar: _buildAppBar(context, title),
        body: Padding(
          padding: EdgeInsets.all(20.gw),
          child: BlocConsumer<UpdateProfileCubit, UpdateProfileState>(
            listener: (context, state) {
              if (state.updateSuccess ?? false) {
                Navigator.pop(context, state.value);
              }
            },
            builder: (context, state) {
              if (field == UserFieldType.phone) {
                return PhoneUpdateWidget(phoneNo: initialValue);
              }
              return _buildInputCard(context, state, title);
            },
          ),
        ),
      ),
    );
  }

  AppBar _buildAppBar(BuildContext context, String title) {
    return AppBar(
      backgroundColor: context.theme.appBarTheme.backgroundColor,
      elevation: 0,
      leading: Container(
        margin: EdgeInsets.all(13.gw),
        decoration: BoxDecoration(
          color: context.colorTheme.borderA,
          borderRadius: BorderRadius.circular(8.gw),
        ),
        child: IconButton(
          icon: Icon(
            Icons.chevron_left,
            color: context.colorTheme.textSecondary,
            size: 20,
          ),
          onPressed: () => Navigator.pop(context),
          padding: EdgeInsets.zero,
        ),
      ),
      centerTitle: true,
      title:
          AneText(title, style: context.theme.appBarTheme.titleTextStyle!.fs20),
    );
  }

  Widget _buildInputCard(
      BuildContext context, UpdateProfileState state, String title) {
    return Container(
      padding: EdgeInsets.all(16.gw),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(12.gw),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AneText(
            _getFieldTitle(field),
            style: context.textTheme.secondary.fs20.w500,
          ),
          SizedBox(height: 32.gw),
          _buildInputField(context, state),
          SizedBox(height: 32.gw),
          _buildSaveButton(context, state),
        ],
      ),
    );
  }

  Widget _buildInputField(BuildContext context, UpdateProfileState state) {
    return IconTextfield(
      controller: state.controller,
      hintText: _getHintTextForFieldType(field),
      prefixIcon: Image.asset(
        _getFieldIcon(context),
      ),
      prefixPadding: EdgeInsets.all(18.gw),
      onChanged: (value) =>
          context.read<UpdateProfileCubit>().valueChanged(value),
    );
    // return Container(
    //   decoration: BoxDecoration(
    //     border: Border.all(
    //       color: context.colorTheme.borderA,
    //       width: 1,
    //     ),
    //     borderRadius: BorderRadius.circular(12.gw),
    //   ),
    //   child: Row(
    //     children: [
    //       _buildInputPrefix(context),
    //       Expanded(
    //         child: TextField(
    //           controller: state.controller,
    //           style: context.textTheme.regular.fs16,
    //           decoration: InputDecoration(
    //             border: InputBorder.none,
    //             hintText: _getHintTextForFieldType(field),
    //             hintStyle: context.textTheme.secondary.fs13,
    //             contentPadding: EdgeInsets.symmetric(
    //               horizontal: 20.gw,
    //               vertical: 18.gw,
    //             ),
    //           ),
    //           onChanged: (value) =>
    //               context.read<UpdateProfileCubit>().valueChanged(value),
    //         ),
    //       ),
    //     ],
    //   ),
    // );
  }

  String _getFieldIcon(BuildContext context) => switch (field) {
        UserFieldType.nickName => Assets.iconProfileUser,
        UserFieldType.email => Assets.iconProfileEmail,
        UserFieldType.realName => Assets.iconProfileUser,
        _ => Assets.iconProfilePhone
      };

  Widget _buildSaveButton(BuildContext context, UpdateProfileState state) {
    return CommonButton(
      enable: state.controller.text.isNotEmpty,
      title: "save".tr(),
      style: CommonButtonStyle.primary,
      onPressed: () => _handleUpdateButtonPress(context),
    );
  }

  String _getFieldTitle(UserFieldType fieldType) => switch (fieldType) {
        UserFieldType.realName => 'real_name_add'.tr(),
        UserFieldType.nickName => 'nickName_add'.tr(),
        UserFieldType.email => 'email_add'.tr(),
        UserFieldType.phone => 'phone_number_add'.tr(),
        _ => fieldType.toString().split('.').last.tr()
      };

  String _getHintTextForFieldType(UserFieldType fieldType) =>
      switch (fieldType) {
        UserFieldType.realName => 'enter_real_name'.tr(),
        UserFieldType.nickName => 'enter_nickname'.tr(),
        UserFieldType.phone => 'enter_phone'.tr(),
        UserFieldType.email => 'enter_email_address'.tr(),
        _ => ''
      };

  String _getAppBarTitle(UserFieldType fieldType) => switch (fieldType) {
        UserFieldType.realName => 'nickName_appbar_title'.tr(),
        UserFieldType.nickName => 'real_name_appbar_title'.tr(),
        UserFieldType.phone => 'phone_number_appbar_title'.tr(),
        UserFieldType.email => 'email_appbar_title'.tr(),
        _ => 'update'.tr()
      };

  void _handleUpdateButtonPress(BuildContext context) async {
    if (field == UserFieldType.phone || field == UserFieldType.email) {
      final isValidated = context.read<UpdateProfileCubit>().validateInput();
      if (isValidated) {
        if (sl<UserCubit>().isFundPasswordInputLocked) {
          GSEasyLoading.showToast("支付密码连续错误5次，请1分钟后重试");
          return;
        }
        final fundPwd = await FundPwdSheet(context).show();
        if (fundPwd == null && !context.mounted) return;
        context.read<UpdateProfileCubit>().updateField(password: fundPwd);
      }
    } else {
      context.read<UpdateProfileCubit>().updateField();
    }
  }
}
