import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

import '../../../../../core/constants/assets.dart';
import '../profile_cubit.dart';
import '../profile_state.dart';

/// A gender dropdown widget using PopupMenuButton
/// Follows the user's preference for popup menu components
class ProfileGenderDropdown extends StatefulWidget {
  final UserProfileState state;

  const ProfileGenderDropdown({
    super.key,
    required this.state,
  });

  @override
  State<ProfileGenderDropdown> createState() => _ProfileGenderDropdownState();
}

class _ProfileGenderDropdownState extends State<ProfileGenderDropdown> {
  bool _isGenderMenuOpen = false;

  @override
  Widget build(BuildContext context) {
    return PopupMenuButton<int>(
      tooltip: '',
      offset: Offset(0, 40.gw),
      constraints: BoxConstraints.tightFor(
        width: 98.gw,
      ),
      color: context.colorTheme.foregroundColor,
      padding: EdgeInsets.zero,
      menuPadding: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(4.gw),
      ),
      onOpened: () => setState(() => _isGenderMenuOpen = true),
      onCanceled: () => setState(() => _isGenderMenuOpen = false),
      child: _isGenderMenuOpen
          ? _buildOpenState(context)
          : _buildClosedState(context),
      itemBuilder: (BuildContext context) {
        List<PopupMenuEntry<int>> items = [];

        for (int i = 0; i < genders.length; i++) {
          final String gender = genders[i];
          final bool isSelected = widget.state.gender == i;

          items.add(
            PopupMenuItem<int>(
              value: i,
              height: 56.gw,
              padding: EdgeInsets.zero,
              child: _buildGenderMenuItem(context, gender.tr(), isSelected),
            ),
          );

          // Add divider between items (but not after the last item)
          if (i < genders.length - 1) {
            items.add(
              const PopupMenuDivider(
                height: 0,
              ),
            );
          }
        }

        return items;
      },
      onSelected: (int selectedIndex) {
        setState(() {
          _isGenderMenuOpen = false;
        });
        context.read<UserProfileCubit>().setTempGender(selectedIndex);
        context.read<UserProfileCubit>().confirmGender();
      },
    );
  }

  Widget _buildOpenState(BuildContext context) {
    return Container(
      height: 30.gw,
      width: 98.gw,
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(8.gw),
        border: Border.all(
          color: context.colorTheme.borderA,
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          SizedBox(
            height: 30.gw,
            width: 61.gw,
            child: Center(
              child: AneText(
                genders[widget.state.gender ?? 0].tr(),
                style: context.textTheme.primary,
              ),
            ),
          ),
          Container(
            height: 30.gw,
            width: 30.gw,
            padding: EdgeInsets.all(10.gw),
            decoration: BoxDecoration(
              color: context.colorTheme.borderA,
              borderRadius: BorderRadius.only(
                topRight: Radius.circular(6.gw),
                bottomRight: Radius.circular(6.gw),
              ),
            ),
            child: SvgPicture.asset(
              Assets.iconToolBarArrowDown,
              width: 12.gw,
              height: 6.gw,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildClosedState(BuildContext context) {
    return Row(
      children: [
        Container(
          height: 30.gw,
          width: 60.gw,
          decoration: BoxDecoration(
            color: context.colorTheme.borderA,
            borderRadius: BorderRadius.circular(6),
          ),
          child: Center(
            child: AneText(
              genders[widget.state.gender ?? 0].tr(),
              style: context.textTheme.title,
            ),
          ),
        ),
        SizedBox(width: 16.gw),
        SvgPicture.asset(
          Assets.iconForward_v2,
          width: 6.gw,
          height: 12.gw,
        ),
      ],
    );
  }

  Widget _buildGenderMenuItem(
      BuildContext context, String title, bool isSelected) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 12.gw),
      child: AneText(
        title,
        style: context.textTheme.title.copyWith(
          color: isSelected
              ? context.colorTheme.btnBgPrimary
              : context.colorTheme.textSecondary,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
        ),
      ),
    );
  }
}
