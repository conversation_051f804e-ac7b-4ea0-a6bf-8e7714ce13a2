import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/text/ane_text.dart';

import '../../../../../core/constants/assets.dart';

/// A reusable profile field widget
/// Displays an icon, title, subtitle (optional), trailing widget, and optional forward arrow
class ProfileField extends StatelessWidget {
  final String icon;
  final String title;
  final String? subtitle;
  final Widget trailing;
  final VoidCallback? onTap;
  final bool isLast;
  const ProfileField({
    super.key,
    required this.icon,
    required this.title,
    this.subtitle,
    required this.trailing,
    this.onTap,
    this.isLast = false,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: context.theme.cardColor,
          borderRadius: isLast
              ? BorderRadius.only(
                  bottomLeft: Radius.circular(isLast ? 8.gw : 0),
                  bottomRight: Radius.circular(isLast ? 8.gw : 0),
                )
              : null,
        ),
        padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 16.gw),
        child: Row(
          children: [
            // Icon
            Container(
              width: 24.gw,
              height: 24.gw,
              padding: EdgeInsets.all(5.gw),
              decoration: BoxDecoration(
                color: context.colorTheme.foregroundColor,
                borderRadius: BorderRadius.circular(4.gw),
              ),
              child: Image.asset(icon, width: 24.gw, height: 24.gw),
            ),
            SizedBox(width: 8.gw),
            // Title and Subtitle
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AneText(
                    title,
                    style: context.textTheme.secondary.w500,
                  ),
                  if (subtitle != null)
                    AneText(
                      subtitle!,
                      style: context.textTheme.title,
                    ),
                ],
              ),
            ),
            // Trailing
            trailing,
            if (onTap != null) ...[
              SizedBox(width: 17.gw),
              _buildForwardIcon(context),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildForwardIcon(BuildContext context) => SvgPicture.asset(
        Assets.iconForward_v2,
        width: 6.gw,
        height: 12.gw,
      );
}

/// A divider widget for separating profile fields
class ProfileFieldDivider extends StatelessWidget {
  const ProfileFieldDivider({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 1,
      color: context.colorTheme.borderA,
    );
  }
}
