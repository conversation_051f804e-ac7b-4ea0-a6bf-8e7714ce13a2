import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/constants/assets.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/common_card.dart';
import 'package:wd/shared/widgets/common_switch.dart';
import 'package:wd/shared/widgets/common_textformfield.dart';

import 'add_wallet_cubit.dart';
import 'add_wallet_state.dart';

class AddWalletPage extends BasePage {
  const AddWalletPage({super.key});

  @override
  BasePageState<BasePage> getState() => _AddWalletPageState();
}

class _AddWalletPageState extends BasePageState<AddWalletPage> {
  final TextEditingController textControllerBankNum = TextEditingController();
  final TextEditingController textControllerBankName = TextEditingController();

  @override
  void initState() {
    pageTitle = "添加钱包";
    context.read<AddWalletCubit>().fetchBankListData();

    super.initState();
  }

  @override
  void dispose() {
    textControllerBankNum.dispose();
    textControllerBankName.dispose();
    super.dispose();
  }

  @override
  Widget buildPage(BuildContext context) {
    return CommonCard(
      child: Form(
        key: context.read<AddWalletCubit>().state.formKey,
        autovalidateMode: AutovalidateMode.disabled,
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 12.5.gw),
          child: SingleChildScrollView(
            child: Column(
              children: [
                // _buildRealNameWidget(),
                // SizedBox(height: 10.gw),
                _buildFormFieldsSection(),
                SizedBox(height: 10.gw),
                _buildDefaultCardSwitchSection(),
                SizedBox(height: 15.gw),
                _buildSubmitButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFormFieldsSection() {
    return getRadiusContainer(
      child: Column(
        children: [
          CommonTextFormField(
            controller: textControllerBankNum,
            validator: (v) {
              if (v == null || v.trim().isEmpty) {
                return "钱包地址不能为空";
              }
              if (StringUtil.containsSpecialCharsOrSpaces(v)) {
                return "请勿包含空格或特殊字符";
              }
              return null;
            },
            hintText: "请输入钱包地址".tr(),
            title: "钱包地址".tr(),
          ),
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () => context
                .read<AddWalletCubit>()
                .onClickShowBankListPicker(context: context, controller: textControllerBankName),
            child: CommonTextFormField(
              controller: textControllerBankName,
              validator: (v) {
                if (v == null || v.trim().isEmpty) {
                  return "请选择钱包名称".tr();
                }
                if (StringUtil.containsSpecialCharsOrSpaces(v)) {
                  return "请勿包含空格或特殊字符";
                }
                return null;
              },
              hintText: "请选择钱包名称".tr(),
              title: "钱包名称".tr(),
              suffixIcon: Image.asset(
                Assets.nextIcon,
                width: 15.gw,
                height: 15.gw,
              ),
              inputEnable: false,
            ),
          ),
          SizedBox(height: 12.5.gw),
        ],
      ),
    );
  }

  Widget _buildDefaultCardSwitchSection() {
    return getRadiusContainer(
      height: 45.gh,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text("设置为默认钱包".tr(), style: Theme.of(context).textTheme.titleLarge),
          BlocBuilder<AddWalletCubit, AddWalletState>(
            builder: (context, state) {
              return CommonSwitch(
                value: state.isDefaultCard,
                onChanged: (value) => context.read<AddWalletCubit>().onClickSwitch(value),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSubmitButton() {
    return CommonButton(
        title: '提交'.tr(),
        backgroundColor: Theme.of(context).colorScheme.primary,
        onPressed: () => context.read<AddWalletCubit>().onSubmit(
              context,
              cardNo: textControllerBankNum.text,
            ));
  }

  Widget getRadiusContainer({required Widget child, double? height}) {
    return Container(
      padding: EdgeInsets.all(10.gw),
      height: height,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(5.r),
      ),
      child: child,
    );
  }
}
