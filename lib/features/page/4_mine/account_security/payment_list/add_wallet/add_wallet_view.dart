import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/constants/assets.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/common_card.dart';
import 'package:wd/shared/widgets/common_switch.dart';
import 'package:wd/shared/widgets/icon_textfield.dart';

import 'add_wallet_cubit.dart';
import 'add_wallet_state.dart';

class AddWalletPage extends BasePage {
  const AddWalletPage({super.key});

  @override
  BasePageState<BasePage> getState() => _AddWalletPageState();
}

class _AddWalletPageState extends BasePageState<AddWalletPage> {
  final TextEditingController textControllerBankNum = TextEditingController();
  final TextEditingController textControllerBankName = TextEditingController();

  @override
  void initState() {
    pageTitle = "add_wallet".tr();
    context.read<AddWalletCubit>().fetchBankListData();

    super.initState();
  }

  @override
  void dispose() {
    textControllerBankNum.dispose();
    textControllerBankName.dispose();
    super.dispose();
  }

  @override
  Widget buildPage(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: LayoutBuilder(
        builder: (context, constraints) {
          return SingleChildScrollView(
            reverse: true, // Ensures input fields are visible when keyboard opens
            child: ConstrainedBox(
              constraints: BoxConstraints(minHeight: constraints.maxHeight),
              child: IntrinsicHeight(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CommonCard(
                      child: Form(
                        key: context.read<AddWalletCubit>().state.formKey,
                        autovalidateMode: AutovalidateMode.disabled,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildWalletAddressTextField(),
                            SizedBox(height: 20.gw),
                            _buildWalletNameTextField(),
                            SizedBox(height: 20.gw),
                            _buildDefaultCardSwitchSection(),
                          ],
                        ),
                      ),
                    ),
                    const Spacer(),
                    Padding(
                      padding: EdgeInsets.only(
                        bottom: MediaQuery.of(context).viewInsets.bottom + 16,
                        top: 16,
                      ),
                      child: _buildSubmitButton(),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildWalletAddressTextField() {
    return IconTextfield(
      controller: textControllerBankNum,
      hintText: "wallet_address_hint".tr(),
      hintStyle: context.textTheme.highlight,
      keyboardType: TextInputType.text,
      onChanged: (value) {
        // Add validation logic if needed
      },
      textStyle: context.textTheme.regular.fs16,
      prefixIcon: IconButton(
        icon: SvgPicture.asset(
          Assets.iconWalletSvg, // placeholder icon
          width: 24.gw,
          height: 24.gw,
        ),
        onPressed: null,
      ),
    );
  }

  Widget _buildWalletNameTextField() {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () => context
          .read<AddWalletCubit>()
          .onClickShowBankListPicker(context: context, controller: textControllerBankName),
      child: IconTextfield(
        controller: textControllerBankName,
        hintText: "wallet_name_hint".tr(),
        hintStyle: context.textTheme.highlight,
        keyboardType: TextInputType.text,
        onChanged: (value) {
          // Add validation logic if needed
        },
        textStyle: context.textTheme.regular.fs16,
        prefixIcon: IconButton(
          icon: SvgPicture.asset(
            Assets.iconWallet, // placeholder icon
            width: 24.gw,
            height: 24.gw,
          ),
          onPressed: null,
        ),
        suffixIcon: Image.asset(
          Assets.nextIcon,
          width: 15.gw,
          height: 15.gw,
        ),
      ),
    );
  }

  Widget _buildDefaultCardSwitchSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text("set_as_default_wallet".tr(), style: context.textTheme.title),
        BlocBuilder<AddWalletCubit, AddWalletState>(
          builder: (context, state) {
            return CommonSwitch(
              value: state.isDefaultCard,
              onChanged: (value) => context.read<AddWalletCubit>().onClickSwitch(value),
            );
          },
        ),
      ],
    );
  }

  Widget _buildSubmitButton() {
    return CommonButton(
        title: 'submit'.tr(),
        onPressed: () => context.read<AddWalletCubit>().onSubmit(
              context,
              cardNo: textControllerBankNum.text,
            ));
  }

  Widget getRadiusContainer({required Widget child, double? height}) {
    return Container(
      padding: EdgeInsets.all(10.gw),
      height: height,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(5.r),
      ),
      child: child,
    );
  }
}
