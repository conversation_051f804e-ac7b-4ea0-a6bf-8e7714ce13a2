import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/config/bottom_nav_config.dart';
import 'package:wd/core/models/entities/video_list_entity.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/singletons/user_singleton.dart';
import 'package:wd/core/singletons/user_state.dart';
import 'package:wd/core/utils/auth_util.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/features/page/main/screens/main_screen_cubit.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/main.dart';
import 'package:wd/shared/widgets/a_third_party/top_snackbar_flutter/top_snack_bar.dart';
import 'package:wd/shared/widgets/banner/chat_entrance_banner.dart';
import 'package:wd/shared/widgets/indicator/video_filckr_indicator.dart';
import 'package:wd/shared/widgets/snack_bar/video_balance_tips_snack_bar.dart';
import 'package:wd/shared/widgets/tiktok/video_detail/video_detail_info_view.dart';
import 'package:wd/shared/widgets/tiktok/video_sliver_grid_view.dart';
import 'package:wd/shared/widgets/video_player/m3u8_player/m3u8Player.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'video_detail_cubit.dart';
import 'video_detail_state.dart';

class VideoDetailPage extends BasePage {
  VideoListRecords model;
  final String videoCategory;
  final String? pageTitle;

  VideoDetailPage({
    super.key,
    required this.model,
    required this.videoCategory,
    this.pageTitle,
  });

  @override
  BasePageState<BasePage> getState() => _VideoDetailPageState();
}

class _VideoDetailPageState extends BasePageState<VideoDetailPage> with RouteAware {
  late ScrollController _scrollController;

  final RefreshController refreshController = RefreshController(initialRefresh: false);

  final paddingH = 14.gw;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();

    pageTitle = widget.pageTitle ?? "午夜影院";
    final cubit = context.read<VideoDetailCubit>();
    cubit.fetchVideoUrl(widget.model.id);

    showBalanceSnakeBar();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    routeObserver.subscribe(this, ModalRoute.of(context)!);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    routeObserver.unsubscribe(this);
    // 确保退出时屏幕方向恢复为竖屏
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
    ]);
    super.dispose();
  }

  @override
  void didPop() {
    dismissCurrentSnackBar();
    super.didPop();
  }

  @override
  void didPushNext() {
    dismissCurrentSnackBar();
    super.didPushNext();
  }

  /// 余额大于0的时候显示顶部snackBar
  void showBalanceSnakeBar() async {
    if (!sl<UserCubit>().state.isLogin) return;
    sl<UserCubit>().fetchUserBalance();
    if ((sl<UserCubit>().state.balanceInfo?.accountMoney ?? 0) <= 0) return;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      var paddingTop = MediaQuery.of(context).padding.top;
      if (paddingTop == 0) paddingTop = 30.gw;
      showTopSnackBar(
        Overlay.of(context),
        VideoBalanceTipsSnackBar(
          balance: sl<UserCubit>().state.balanceInfo?.accountMoney.toString() ?? '',
          onTapGoPlayGame: () {
            sl<MainScreenCubit>().selectedNavTypeChanged(BottomNavType.gameHome);
            sl<NavigatorService>().popUntil(AppRouter.nav);
            dismissCurrentSnackBar();
          },
        ),
        curve: Curves.fastLinearToSlowEaseIn,
        displayDuration: const Duration(seconds: 30),
        padding: EdgeInsets.fromLTRB(28.gw, paddingTop + 44.gw, 28.gw, 0),
      );
    });
  }

  void _scrollToTop() {
    _scrollController.animateTo(
      0, // 滚动到顶部的偏移量
      duration: const Duration(milliseconds: 300), // 滑动动画的持续时间
      curve: Curves.easeInOut, // 动画曲线
    );
  }

  @override
  Widget buildPage(BuildContext context) {
    final cubit = context.read<VideoDetailCubit>();

    return BlocListener<UserCubit, UserState>(
      listenWhen: (previous, current) => current.isLogin != previous.isLogin,
      listener: (context, state) {
        cubit.onLoginChanged(state.isLogin);
      },
      child: BlocBuilder<VideoDetailCubit, VideoDetailState>(
        builder: (context, state) {
          return CustomScrollView(
            controller: _scrollController,
            slivers: [
              // 固定的空白
              SliverToBoxAdapter(child: SizedBox(height: paddingH)),
              // 视频播放器
              if (state.currentVideoDetailModel != null) ...[
                SliverToBoxAdapter(
                    child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: paddingH),
                  child: AspectRatio(
                    aspectRatio: 16 / 9,
                    child: Container(
                        clipBehavior: Clip.hardEdge,
                        decoration: BoxDecoration(
                          color: Colors.black,
                          borderRadius: BorderRadius.all(Radius.circular(8.gw)),
                        ),
                        child: M3u8Player(
                          key: ValueKey(state.currentVideoDetailModel!.lineUrl),
                          routeObserver: routeObserver,
                          videoUrl: kDebugMode
                              ? 'https://bitdash-a.akamaihd.net/content/sintel/hls/playlist.m3u8'
                              : state.currentVideoDetailModel!.lineUrl,
                          useChewie: true,
                          onClickLogin: () {
                            if (sl<UserCubit>().state.isLogin) {
                              sl<MainScreenCubit>().selectedNavTypeChanged(BottomNavType.gameHome);
                              sl<NavigatorService>().popUntil(AppRouter.nav);
                            } else {
                              AuthUtil.checkIfLogin(() {
                                cubit.judgeTimeLimit();
                              });
                            }
                          },
                          minutesLimit: state.minutesLimit,
                        )),
                  ),
                )),
              ] else ...[
                SliverToBoxAdapter(
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: paddingH),
                    child: AspectRatio(
                        aspectRatio: 16 / 9,
                        child: Container(
                          clipBehavior: Clip.hardEdge,
                          decoration: BoxDecoration(
                              color: Colors.black,
                              borderRadius: BorderRadius.all(
                                Radius.circular(8.gw),
                              )),
                          alignment: Alignment.center,
                          child: const VideoFlickrIndicator(),
                        )),
                  ),
                ),
              ],

              // 视频信息
              if (state.currentVideoDetailModel != null)
                SliverToBoxAdapter(
                  child: Padding(
                    padding: EdgeInsets.only(left: paddingH, right: paddingH, top: 5.gw),
                    child: VideoDetailInfoView(
                      model: state.currentVideoDetailModel!,
                      onClickLike: () async {
                        return await cubit.onClickVideoLike();
                      },
                      onClickShare: () => sl<NavigatorService>().push(AppRouter.appShare),
                    ),
                  ),
                ),

              SliverToBoxAdapter(
                child: Padding(
                  padding: EdgeInsets.only(left: paddingH, right: paddingH, top: 20.gw),
                  child: ChatEntranceBanner(),
                ),
              ),
              // SliverToBoxAdapter(
              //   child: Padding(
              //     padding: EdgeInsets.only(left: paddingH, right: paddingH, top: 16.gw),
              //     child: Text(
              //       "相关视频",
              //       style: TextStyle(fontSize: 16.fs, fontWeight: FontWeight.w500, color: const Color(0xff6A7391)),
              //     ),
              //   ),
              // ),
              // SliverGrid 显示网格布局
              if (state.currentVideoDetailModel != null)
                SliverPadding(
                  padding: EdgeInsets.only(left: paddingH, right: paddingH, top: 24.gw),
                  sliver: VideoSliverGridView(
                    paddingH: paddingH,
                    videoList: state.currentVideoDetailModel!.recommendList,
                    onTapCell: (VideoListRecords value) {
                      widget.model = value;
                      cubit.fetchVideoUrl(value.id);
                      _scrollToTop();
                    },
                  ),
                ),
            ],
          );
        },
      ),
    );
  }
}
