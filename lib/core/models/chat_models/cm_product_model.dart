class CMProductModel {
  final String? id;
  final int? mentorId;
  final String? productName;
  final String? applicationTime;
  final String? mentorName;
  final double? price;
  final int? commissionRatio;
  final int? productId;

  CMProductModel({
    this.id,
    this.mentorId,
    this.productName,
    this.applicationTime,
    this.mentorName,
    this.price,
    this.commissionRatio,
    this.productId,
  });

  CMProductModel copyWith({
    String? id,
    int? mentorId,
    String? productName,
    String? applicationTime,
    String? mentorName,
    double? price,
    int? commissionRatio,
    int? productId,
  }) {
    return CMProductModel(
      id: id ?? this.id,
      mentorId: mentorId ?? this.mentorId,
      productName: productName ?? this.productName,
      applicationTime: applicationTime ?? this.applicationTime,
      mentorName: mentorName ?? this.mentorName,
      price: price ?? this.price,
      commissionRatio: commissionRatio ?? this.commissionRatio,
      productId: productId ?? this.productId,
    );
  }

  factory CMProductModel.fromJson(Map<String, dynamic> json) {
    return CMProductModel(
      id: json['id'] as String?,
      mentorId: json['mentorId'] as int?,
      productName: json['productName'] as String?,
      applicationTime: json['applicationTime'] as String?,
      mentorName: json['mentorName'] as String?,
      price: (json['price'] as num?)?.toDouble(),
      commissionRatio: json['commissionRatio'] as int?,
      productId: json['productId'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'mentorId': mentorId,
      'productName': productName,
      'applicationTime': applicationTime,
      'mentorName': mentorName,
      'price': price,
      'commissionRatio': commissionRatio,
      'productId': productId,
    };
  }

  @override
  String toString() {
    return 'CMProductModel(id: $id, mentorId: $mentorId, productName: $productName, applicationTime: $applicationTime, mentorName: $mentorName, price: $price, commissionRatio: $commissionRatio, productId: $productId)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is CMProductModel &&
        other.id == id &&
        other.mentorId == mentorId &&
        other.productName == productName &&
        other.applicationTime == applicationTime &&
        other.mentorName == mentorName &&
        other.price == price &&
        other.commissionRatio == commissionRatio &&
        other.productId == productId;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        mentorId.hashCode ^
        productName.hashCode ^
        applicationTime.hashCode ^
        mentorName.hashCode ^
        price.hashCode ^
        commissionRatio.hashCode ^
        productId.hashCode;
  }
}
