import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/top_up_record_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/top_up_record_entity.g.dart';

@JsonSerializable()
class TopUpRecordEntity {
	late List<TopUpRecord> records = [];
	late int total = 0;
	late int size = 0;
	late int current = 0;
	late int pages = 0;

	TopUpRecordEntity();

	factory TopUpRecordEntity.fromJson(Map<String, dynamic> json) => $TopUpRecordEntityFromJson(json);

	Map<String, dynamic> toJson() => $TopUpRecordEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class TopUpRecord {
	late int id = 0;
	late String transactionNo = '';
	late int userId = 0;
	late String userNo = '';
	late String  depositor = '';
	late double orderInitialAmount;
	late double orderAmount;
	late double realAmount;
	late double reduceAmount;
	late double reduceRate;
	late double reduceMaxLimit;
	late double finalAmount;
	late double serviceChargeRate;
	late double serviceCharge;
	late double serviceChargeMaxLimit;
	late String cashinWayCode = '';
	late String cashinWayName = '';
	late String cashinTypeCode = '';
	late String cashinTypeName = '';
	late String cashinChannel = '';
	late String cardNo = '';
	late String cardRealName = '';
	late String requestTime = '';
	late int orderStatus = 0;
	late double integral = 0;
	late int isTodayFirst = 0;
	late String operateTime = '';
	late String operator = '';
	late String remark = '';
	late String serialNumber = "";
	late double exchangeRate = 0;
	late String currency = "";

	TopUpRecord();

	factory TopUpRecord.fromJson(Map<String, dynamic> json) => $TopUpRecordFromJson(json);

	Map<String, dynamic> toJson() => $TopUpRecordToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}


@JsonSerializable()
class TransactFilterWayList {
	late List<TransactFilterWay> list = [];

	TransactFilterWayList();

	factory TransactFilterWayList.fromJson(Map<String, dynamic> json) => $TransactFilterWayListFromJson(json);

	Map<String, dynamic> toJson() => $TransactFilterWayListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class TransactFilterWay {
	late int id = 0;
	late String wayCode = '';
	late String wayName = '';
	bool isSel = false;

	TransactFilterWay();

	factory TransactFilterWay.fromJson(Map<String, dynamic> json) => $TransactFilterWayFromJson(json);

	Map<String, dynamic> toJson() => $TransactFilterWayToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}


@JsonSerializable()
class TransactFilterTypeList {
	late List<TransactFilterType> list = [];

	TransactFilterTypeList();

	factory TransactFilterTypeList.fromJson(Map<String, dynamic> json) => $TransactFilterTypeListFromJson(json);

	Map<String, dynamic> toJson() => $TransactFilterTypeListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class TransactFilterType {
	late int id = 0;
	late String typeCode = '';
	late String typeName = '';
	late String wayCode = '';
	bool isSel = false;

	TransactFilterType();

	factory TransactFilterType.fromJson(Map<String, dynamic> json) => $TransactFilterTypeFromJson(json);

	Map<String, dynamic> toJson() => $TransactFilterTypeToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}