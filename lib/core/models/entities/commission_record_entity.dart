import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/commission_record_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/commission_record_entity.g.dart';

@JsonSerializable()
class CommissionRecordEntity {
  late List<CommissionRecordList> records = [];
  late int total = 0;
  late int size = 0;
  late int current = 0;
  late List<CommissionRecordOrders> orders = [];
  late CommissionRecordOptimizeCountSql optimizeCountSql;
  late CommissionRecordSearchCount searchCount;
  late bool optimizeJoinOfCountSql = false;
  late int maxLimit = 0;
  late String countId = '';
  late int pages = 0;

  CommissionRecordEntity();

  factory CommissionRecordEntity.fromJson(Map<String, dynamic> json) => $CommissionRecordEntityFromJson(json);

  Map<String, dynamic> toJson() => $CommissionRecordEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class CommissionRecordList {
  late int userId = 0;
  late String userNo = '';
  late int changeAmount = 0;
  late String createTime = '';

  CommissionRecordList();

  factory CommissionRecordList.fromJson(Map<String, dynamic> json) => $CommissionRecordListFromJson(json);

  Map<String, dynamic> toJson() => $CommissionRecordListToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class CommissionRecordOrders {
  late String column = '';
  late bool asc = false;

  CommissionRecordOrders();

  factory CommissionRecordOrders.fromJson(Map<String, dynamic> json) => $CommissionRecordOrdersFromJson(json);

  Map<String, dynamic> toJson() => $CommissionRecordOrdersToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class CommissionRecordOptimizeCountSql {
  CommissionRecordOptimizeCountSql();

  factory CommissionRecordOptimizeCountSql.fromJson(Map<String, dynamic> json) =>
      $CommissionRecordOptimizeCountSqlFromJson(json);

  Map<String, dynamic> toJson() => $CommissionRecordOptimizeCountSqlToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class CommissionRecordSearchCount {
  CommissionRecordSearchCount();

  factory CommissionRecordSearchCount.fromJson(Map<String, dynamic> json) => $CommissionRecordSearchCountFromJson(json);

  Map<String, dynamic> toJson() => $CommissionRecordSearchCountToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
