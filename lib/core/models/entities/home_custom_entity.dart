import 'package:equatable/equatable.dart';
import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/home_custom_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/home_custom_entity.g.dart';

@JsonSerializable()
class HomeCustomEntity with EquatableMixin {
  List<String>? banners = [];
  List<String>? notices = [];
  HomeCustomSection? jin;
  HomeCustomSection? sport;
  HomeCustomSection? casino;
  HomeCustomSection? lottery;
  HomeCustomSection? eSport;
  HomeCustomSection? game;
  HomeCustomSection? rank;

  HomeCustomEntity({
    this.banners,
    this.notices,
    this.jin,
    this.sport,
    this.casino,
    this.lottery,
    this.eSport,
    this.game,
    this.rank,
  });

  HomeCustomEntity copyWith({
    List<String>? banners,
    List<String>? notices,
    HomeCustomSection? jin,
    HomeCustomSection? sport,
    HomeCustomSection? casino,
    HomeCustomSection? lottery,
    HomeCustomSection? eSport,
    HomeCustomSection? game,
    HomeCustomSection? rank,
  }) {
    return HomeCustomEntity(
      banners: banners ?? this.banners,
      notices: notices ?? this.notices,
      jin: jin ?? this.jin,
      sport: sport ?? this.sport,
      casino: casino ?? this.casino,
      lottery: lottery ?? this.lottery,
      eSport: eSport ?? this.eSport,
      game: game ?? this.game,
      rank: rank ?? this.rank,
    );
  }

  factory HomeCustomEntity.fromJson(Map<String, dynamic> json) => $HomeCustomEntityFromJson(json);

  Map<String, dynamic> toJson() => $HomeCustomEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }

  @override
  List<Object?> get props => [
        banners,
        notices,
        jin,
        sport,
        casino,
        lottery,
        eSport,
        game,
        rank,
      ];
}

@JsonSerializable()
class HomeCustomSection {
  String title = '';
  String icon = '';
  bool isHot = false;
  List<HomeCustomSubItem> list = [];

  HomeCustomSection();

  factory HomeCustomSection.fromJson(Map<String, dynamic> json) => $HomeCustomSectionFromJson(json);

  Map<String, dynamic> toJson() => $HomeCustomSectionToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

enum HomeJumpType {
  unknown,
  webView,
  liveVideo,
  tabMenu,
}

// 添加扩展方法用于序列化和反序列化
extension HomeJumpTypeExtension on HomeJumpType {
  String toJson() {
    switch (this) {
      case HomeJumpType.webView:
        return 'webView';
      case HomeJumpType.liveVideo:
        return 'liveVideo';
      case HomeJumpType.tabMenu:
        return 'tabMenu';
      case HomeJumpType.unknown:
      default:
        return 'unknown';
    }
  }

  static HomeJumpType fromJson(String value) {
    switch (value) {
      case 'webView':
        return HomeJumpType.webView;
      case 'liveVideo':
        return HomeJumpType.liveVideo;
      case 'tabMenu':
        return HomeJumpType.tabMenu;
      case 'unknown':
      default:
        return HomeJumpType.unknown;
    }
  }
}

@JsonSerializable()
class HomeCustomSubItem {
  int id = 0;
  String image = '';
  String name = '';
  String eName = '';
  String loginUrl = '';
  int thirdPlatformId = 0;
  HomeJumpType type = HomeJumpType.unknown;
  bool isHot = false;

  HomeCustomSubItem();

  factory HomeCustomSubItem.fromJson(Map<String, dynamic> json) => $HomeCustomSubItemFromJson(json);

  Map<String, dynamic> toJson() => $HomeCustomSubItemToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
