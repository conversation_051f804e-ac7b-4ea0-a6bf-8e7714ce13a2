import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/invite_code_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/invite_code_entity.g.dart';

@JsonSerializable()
class InviteCodeEntity {
	late String inviteCode = '';
	late String spreadUrl = '';

	InviteCodeEntity();

	factory InviteCodeEntity.fromJson(Map<String, dynamic> json) => $InviteCodeEntityFromJson(json);

	Map<String, dynamic> toJson() => $InviteCodeEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}