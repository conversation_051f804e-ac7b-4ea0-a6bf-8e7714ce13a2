import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/user_vip_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/user_vip_entity.g.dart';

@JsonSerializable()
class UserVipEntity {
  late String vipTitle = '';
  late int vipLevel = 0;
  late double integral;
  late int nextLevelIntegral = 0;

  UserVipEntity();

  factory UserVipEntity.fromJson(Map<String, dynamic> json) => $UserVipEntityFromJson(json);

  Map<String, dynamic> toJson() => $UserVipEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
