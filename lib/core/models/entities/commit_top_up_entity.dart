import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/commit_top_up_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/commit_top_up_entity.g.dart';

@JsonSerializable()
class CommitTopUpEntity {
  late String orderNo = '';
  dynamic payWayName;
  dynamic tradeTime;
  late double amount;
  late String currency = '';
  late String payUrl = '';
  late TopUpOrderExt? ext;

  CommitTopUpEntity();

  factory CommitTopUpEntity.fromJson(Map<String, dynamic> json) => $CommitTopUpEntityFromJson(json);

  Map<String, dynamic> toJson() => $CommitTopUpEntityToJson(this);

  @override
  String toString() {

    return jsonEncode(this);
  }
}

@JsonSerializable()
class TopUpOrderExt {
  late String payee = '';
  late String orderNo = '';
  late int orderAmount = 0;
  late int orderLiveTime = 0; // 订单存活时间
  late String payType = ''; // 支付类型
  late String bankCard = '';
  late String receivingBank = '';
  late String orderStatus = '';
  late double depositNo;
  late String addressQrCode = '';
  late String exchangeRate = '';
  late String pact = '';
  late String depositAddress = '';
  late String currency = '';
  late String bannerImg = ''; // 头部图标
  late String chainImg = ''; // 区块链图标
  late String usdtImg = ''; // USDT图标
  late String cnyImg = ''; // CNY图标
  late String exchangeRateImg = ''; // 汇率图标
  late String tipHead = ''; // 充值说明标题
  late String tipContent = ''; // 充值说明内容

  TopUpOrderExt();

  factory TopUpOrderExt.fromJson(Map<String, dynamic> json) => $TopUpOrderExtFromJson(json);

  Map<String, dynamic> toJson() => $TopUpOrderExtToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
