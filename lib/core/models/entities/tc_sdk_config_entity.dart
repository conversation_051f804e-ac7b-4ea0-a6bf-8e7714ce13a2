import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/tc_sdk_config_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/tc_sdk_config_entity.g.dart';

@JsonSerializable()
class TCSDKConfigEntity {
	late String userSig = '';
	late String sdkAppId = '';
	late String userId = '';

	TCSDKConfigEntity();

	factory TCSDKConfigEntity.fromJson(Map<String, dynamic> json) => $TCSDKConfigEntityFromJson(json);

	Map<String, dynamic> toJson() => $TCSDKConfigEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}