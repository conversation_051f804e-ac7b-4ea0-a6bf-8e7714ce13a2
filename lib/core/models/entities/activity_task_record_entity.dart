import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/activity_task_record_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/activity_task_record_entity.g.dart';

@JsonSerializable()
class ActivityTaskRecordEntity {
	late List<ActivityTaskRecord> records = [];
	late int total = 0;
	late int size = 0;
	late int current = 0;
	late int pages = 0;

	ActivityTaskRecordEntity();

	factory ActivityTaskRecordEntity.fromJson(Map<String, dynamic> json) => $ActivityTaskRecordEntityFromJson(json);

	Map<String, dynamic> toJson() => $ActivityTaskRecordEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ActivityTaskRecord {
	late int id = 0;
	late String activeName = '';
	late int activeType = 0;
	late double sendAmount = 0.0;
	late String createTime = '';

	ActivityTaskRecord();

	factory ActivityTaskRecord.fromJson(Map<String, dynamic> json) => $ActivityTaskRecordFromJson(json);

	Map<String, dynamic> toJson() => $ActivityTaskRecordToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}