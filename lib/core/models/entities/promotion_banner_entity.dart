import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/promotion_banner_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/promotion_banner_entity.g.dart';

@JsonSerializable()
class PromotionBannerEntity {
	late List<PromotionBannerList> list = [];

	PromotionBannerEntity();

	factory PromotionBannerEntity.fromJson(Map<String, dynamic> json) => $PromotionBannerEntityFromJson(json);

	Map<String, dynamic> toJson() => $PromotionBannerEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class PromotionBannerList {
	late String id = '';
	late String title = '';
	late String link = '';
	late String addtime = '';
	@JSONField(name: "a_content")
	late String aContent = '';
	late String kclass = '';
	late String description = '';
	late String startime = '';
	late String endtime = '';
	late String sort = '';
	late String cid = '';
	late String ctitle = '';
	late String csort = '';

	PromotionBannerList();

	factory PromotionBannerList.fromJson(Map<String, dynamic> json) => $PromotionBannerListFromJson(json);

	Map<String, dynamic> toJson() => $PromotionBannerListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}