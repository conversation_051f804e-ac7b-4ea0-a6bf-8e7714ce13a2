import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/top_up_list_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/top_up_list_entity.g.dart';

@JsonSerializable()
class TopUpListEntityList {
  List<TopUpListEntity> list = [];

  TopUpListEntityList();

  factory TopUpListEntityList.fromJson(Map<String, dynamic> json) => $TopUpListEntityListFromJson(json);

  Map<String, dynamic> toJson() => $TopUpListEntityListToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class TopUpListEntity {
  late String payWayName = '';
  late String payWayCode = '';
  late String payWayTag = '';
  late String icon = '';
  dynamic exchangeRate;
  late bool recommended = false;
  late List<TopUpListPayTypeList> payTypeList = [];

  TopUpListEntity();

  factory TopUpListEntity.fromJson(Map<String, dynamic> json) => $TopUpListEntityFromJson(json);

  Map<String, dynamic> toJson() => $TopUpListEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class TopUpListPayTypeList {
  late String payTypeName = '';
  late int payTypeId = 0;
  late double? present = 0;
  late String controllerTips = '';
  late double amountMaxLimit;
  late double amountMinLimit;
  late int sort = 0;
  late String amountList = "";
  late bool fixedAmount = false;
  late bool redirectWalletLobby = false;
  late int offlineChatPayType = -1;
  late String amountLimit = "0";
  String? lobbyLink = "";
  double? balance;
  late String payChannelTag = '';

  TopUpListPayTypeList();

  factory TopUpListPayTypeList.fromJson(Map<String, dynamic> json) => $TopUpListPayTypeListFromJson(json);

  Map<String, dynamic> toJson() => $TopUpListPayTypeListToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
