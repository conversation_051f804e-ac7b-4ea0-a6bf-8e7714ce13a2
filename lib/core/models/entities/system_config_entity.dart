
// lib/core/models/entities/system_config_entity.dart
import 'package:wd/core/constants/constants.dart';
import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/system_config_entity.g.dart';
import 'dart:convert';

export 'package:wd/generated/json/system_config_entity.g.dart';

@JsonSerializable()
class SystemConfigEntity {

  @JSONField(name: "invite_domain") // 邀请域名
  late String inviteDomain = '';
  @JSONField(name: "login_captcha") // 登录验证码 0无需验证码、1图形、2网易
  late String loginCaptcha = '2';
  @JSONField(name: "register_captcha") // 注册验证码 0无需验证码、1图形、2网易
  late String registerCaptcha = '2';
  @JSONField(name: "chat_domain") // 聊天域名（弃用）
  late String chatDomain = '';
  @JSONField(name: "video_domain") // 原来视频域名 (弃用)
  late String videoDomain = '';
  @JSONField(name: "service_url") // 客服链接
  late String serviceUrl = '';
  @JSONField(name: "app_download_url") // app下载链接
  late String appDownloadUrl = kAppDownloadLinkStr;
  @JSONField(name: "service_open_type") // 客服链接跳转方式 1内跳， 2外跳
  late String serviceOpenType = '1';
  @JSONField(name: "video_watch_limit") // 视频规则
  late String videoWatchLimit = '';
  @JSONField(name: "video_watch_rule") // 打码量对应的观看天数规则图片
  late String videoWatchRule = '';
  @JSONField(name: "aws_access_domain") // 游戏相关图片
  late String gamePicBaseUrl = '';
  @JSONField(name: "currency_code") // 币种代码
  late String currencyCode = '';
  @JSONField(name: "currency_symbol") // 币种符号
  late String currencySymbol = '';
  late String wtdCheckPhone = '1'; // 提款是否验证手机号 1不验证 2验证
  late LanguageType languageType = LanguageType();  // 国际化语言配置
  late LoadLoginAndRegWay loadLoginAndRegWay = LoadLoginAndRegWay()
    ..login = "1,2"
    ..register = "1,2"
    ..defLogin = "1"
    ..defRegister = "1";

  SystemConfigEntity();

  factory SystemConfigEntity.fromJson(Map<String, dynamic> json) => $SystemConfigEntityFromJson(json);

  Map<String, dynamic> toJson() => $SystemConfigEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }


  String? getValueByKey(String key) {
    final Map<String, String> configMap = {
      'invite_domain': inviteDomain,
      'login_captcha': loginCaptcha,
      'register_captcha': registerCaptcha,
      'chat_domain': chatDomain,
      'video_domain': videoDomain,
      'service_url': serviceUrl,
      'app_download_url': appDownloadUrl,
      'service_open_type': serviceOpenType,
      'video_watch_limit': videoWatchLimit,
      'video_watch_rule': videoWatchRule,
      'loadLoginAndRegWay.login': loadLoginAndRegWay.login,
      'loadLoginAndRegWay.register': loadLoginAndRegWay.register,
      'loadLoginAndRegWay.defLogin': loadLoginAndRegWay.defLogin,
      'loadLoginAndRegWay.defRegister': loadLoginAndRegWay.defRegister,
      'wtdCheckPhone': wtdCheckPhone,
    };

    return configMap[key];
  }
}

@JsonSerializable()
class LoadLoginAndRegWay {
  late String login; // 登录: 1.手机号 2.账号,全选用英文,分隔
  late String register; // 注册: 1.手机号 2.账号,全选用英文,分隔
  late String defLogin; // 默认登录: 1.手机号 2.账号
  late String defRegister; // 默认注册: 1.手机号 2.账号

  LoadLoginAndRegWay();

  factory LoadLoginAndRegWay.fromJson(Map<String, dynamic> json) => $LoadLoginAndRegWayFromJson(json);

  Map<String, dynamic> toJson() => $LoadLoginAndRegWayToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}


@JsonSerializable()
class LanguageType {
  String defaultLanguage = 'en_US';
  List<LanguageConfig> list = [];

  LanguageType();

  factory LanguageType.fromJson(Map<String, dynamic> json) => $LanguageTypeFromJson(json);

  Map<String, dynamic> toJson() => $LanguageTypeToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class LanguageConfig {
  String dictLabel = '';
  String dictValue = '';
  int dictSort = 0;

  LanguageConfig();

  factory LanguageConfig.fromJson(Map<String, dynamic> json) => $LanguageConfigFromJson(json);

  Map<String, dynamic> toJson() => $LanguageConfigToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}