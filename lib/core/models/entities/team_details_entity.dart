import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/team_details_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/team_details_entity.g.dart';

@JsonSerializable()
class TeamDetailsEntity {
  List<TeamDetailsRecords>? records;
  int? total;
  int? size;
  int? current;
  List<TeamDetailsOrders>? orders;
  TeamDetailsOptimizeCountSql? optimizeCountSql;
  TeamDetailsSearchCount? searchCount;
  bool? optimizeJoinOfCountSql;
  int? maxLimit;
  String? countId;
  int? pages;

  TeamDetailsEntity();

  factory TeamDetailsEntity.fromJson(Map<String, dynamic> json) =>
      $TeamDetailsEntityFromJson(json);

  Map<String, dynamic> toJson() => $TeamDetailsEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class TeamDetailsRecords {
  String? subUserNo;
  double? amount;
  double? commissionAmount;
  double? withdrawAmount;
  double? profitAmount;

  TeamDetailsRecords();

  factory TeamDetailsRecords.fromJson(Map<String, dynamic> json) =>
      $TeamDetailsRecordsFromJson(json);

  Map<String, dynamic> toJson() => $TeamDetailsRecordsToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class TeamDetailsOrders {
  String? column;
  bool? asc;

  TeamDetailsOrders();

  factory TeamDetailsOrders.fromJson(Map<String, dynamic> json) =>
      $TeamDetailsOrdersFromJson(json);

  Map<String, dynamic> toJson() => $TeamDetailsOrdersToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class TeamDetailsOptimizeCountSql {
  TeamDetailsOptimizeCountSql();

  factory TeamDetailsOptimizeCountSql.fromJson(Map<String, dynamic> json) =>
      $TeamDetailsOptimizeCountSqlFromJson(json);

  Map<String, dynamic> toJson() => $TeamDetailsOptimizeCountSqlToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class TeamDetailsSearchCount {
  TeamDetailsSearchCount();

  factory TeamDetailsSearchCount.fromJson(Map<String, dynamic> json) =>
      $TeamDetailsSearchCountFromJson(json);

  Map<String, dynamic> toJson() => $TeamDetailsSearchCountToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
