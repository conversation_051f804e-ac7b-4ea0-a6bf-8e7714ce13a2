import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/video_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/video_entity.g.dart';

@JsonSerializable()
class VideoEntityList {
	List<VideoEntity> records = [];
	int total = 0;
	int size = 0;
	int current = 0;
	int pages = 0;

	VideoEntityList();

	factory VideoEntityList.fromJson(Map<String, dynamic> json) => $VideoEntityListFromJson(json);

	Map<String, dynamic> toJson() => $VideoEntityListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class VideoEntity {
	late int id = 0;
	late String videoImage = '';
	late String videoTitle = '';
	late String videoYear = '';
	late String videoTags = '';
	late String videoCountry = '';
	late String videoClarity = '';
	late String videoBottomTag = '';
	late int hide = 0;
	late List links = [];

	VideoEntity();

	factory VideoEntity.fromJson(Map<String, dynamic> json) => $VideoEntityFromJson(json);

	Map<String, dynamic> toJson() => $VideoEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}


@JsonSerializable()
class VideoVipRemainDayEntity {
	late int days = 0;
	late String expiredDate = '';

	VideoVipRemainDayEntity();

	factory VideoVipRemainDayEntity.fromJson(Map<String, dynamic> json) => $VideoVipRemainDayEntityFromJson(json);

	Map<String, dynamic> toJson() => $VideoVipRemainDayEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}