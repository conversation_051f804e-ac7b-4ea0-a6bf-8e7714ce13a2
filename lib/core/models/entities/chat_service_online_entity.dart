import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/chat_service_online_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/chat_service_online_entity.g.dart';

@JsonSerializable()
class ChatServiceOnlineList {
	List<ChatServiceOnlineEntity> list = [];

	ChatServiceOnlineList();

	factory ChatServiceOnlineList.fromJson(Map<String, dynamic> json) =>
			$ChatServiceOnlineListFromJson(json);

	Map<String, dynamic> toJson() => $ChatServiceOnlineListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ChatServiceOnlineEntity {
	late String status = '';
	@JSONField(name: 'to_Account')
	late String toAccount = '';

	ChatServiceOnlineEntity();

	factory ChatServiceOnlineEntity.fromJson(Map<String, dynamic> json) => $ChatServiceOnlineEntityFromJson(json);

	Map<String, dynamic> toJson() => $ChatServiceOnlineEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}