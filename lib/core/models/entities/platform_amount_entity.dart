import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/platform_amount_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/platform_amount_entity.g.dart';


@JsonSerializable()
class PlatformAmountEntityList {
	List<PlatformAmountEntity> list = [];

	PlatformAmountEntityList();

	factory PlatformAmountEntityList.fromJson(Map<String, dynamic> json) =>
			$PlatformAmountEntityListFromJson(json);

	Map<String, dynamic> toJson() => $PlatformAmountEntityListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}


@JsonSerializable()
class PlatformAmountEntity {
	late int platformId = 0;
	late String platformName = '';
	late double amount = 0;
	bool isLoading = false;

	PlatformAmountEntity();

	factory PlatformAmountEntity.fromJson(Map<String, dynamic> json) => $PlatformAmountEntityFromJson(json);

	Map<String, dynamic> toJson() => $PlatformAmountEntityToJson(this);
	PlatformAmountEntity copyWith({
		int? platformId,
		String? platformName,
		double? amount,
		bool? isLoading,
	}) {
		return PlatformAmountEntity()
			..platformId = platformId ?? this.platformId
			..platformName = platformName ?? this.platformName
			..amount = amount ?? this.amount
			..isLoading = isLoading ?? this.isLoading;
	}

	@override
	String toString() {
		return jsonEncode(this);
	}
}