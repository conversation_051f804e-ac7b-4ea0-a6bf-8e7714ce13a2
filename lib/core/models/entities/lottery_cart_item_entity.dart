import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/lottery_cart_item_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/lottery_cart_item_entity.g.dart';

@JsonSerializable()
class LotteryCartItemEntity {
	late double betAmount;
	late String itemType;
	late String itemObject;
	int rule = 1; // 1单选, >1 维多选
	int? id = 0;
	bool isLhcGuoGuan = false; /// 六合彩过关标识
	String option = ''; /// 多选需传 传id
	String optionName = ''; /// option对应的中文

	LotteryCartItemEntity();

	factory LotteryCartItemEntity.fromJson(Map<String, dynamic> json) => $LotteryCartItemEntityFromJson(json);

	Map<String, dynamic> toJson() => $LotteryCartItemEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}