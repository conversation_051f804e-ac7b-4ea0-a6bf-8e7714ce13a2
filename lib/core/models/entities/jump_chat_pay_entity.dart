import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/jump_chat_pay_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/jump_chat_pay_entity.g.dart';

@JsonSerializable()
class JumpChatPayEntity {
	late String userNo = '';
	late String nickName = '';

	JumpChatPayEntity();

	factory JumpChatPayEntity.fromJson(Map<String, dynamic> json) => $JumpChatPayEntityFromJson(json);

	Map<String, dynamic> toJson() => $JumpChatPayEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}