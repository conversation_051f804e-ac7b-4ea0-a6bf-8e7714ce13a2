import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/daily_check_in_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/daily_check_in_entity.g.dart';

@JsonSerializable()
class DailyCheckInEntity {
	late int totalDays = 0;
	@JSONField(name: "defaultView")
	late List<DailyCheckInItem> shortList = [];
	@JSONField(name: "allView")
	late List<DailyCheckInItem> fullList = [];

	DailyCheckInEntity();

	factory DailyCheckInEntity.fromJson(Map<String, dynamic> json) => $DailyCheckInEntityFromJson(json);

	Map<String, dynamic> toJson() => $DailyCheckInEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

/// 签到状态枚举
enum SignInState {
	signed(1, "已签到"),
	backdated(2, "已补签"),
	needBackdate(3, "需要补签"),
	unsigned(4, "未签到"),
	unavailable(5, "不能签到");

	final int value;
	final String description;

	const SignInState(this.value, this.description);

	static SignInState fromValue(int value) {
		return SignInState.values.firstWhere(
					(state) => state.value == value,
			orElse: () => SignInState.unavailable,
		);
	}
}

@JsonSerializable()
class DailyCheckInItem {
	late String dayName = '';
	late int day = 0;
	late double signInAward;
	late double reSignInAward;
	late double signInRecharge;
	late double resignRecharge;
	late int signInState = 0;
	dynamic signInDate;
	bool isFetching = false;

	DailyCheckInItem();

	factory DailyCheckInItem.fromJson(Map<String, dynamic> json) => $DailyCheckInItemFromJson(json);

	Map<String, dynamic> toJson() => $DailyCheckInItemToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}