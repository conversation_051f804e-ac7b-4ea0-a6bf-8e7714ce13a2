import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/game_login_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/game_login_entity.g.dart';

@JsonSerializable()
class GameLoginEntity {
  late int type; //1-Url  2-html,
  late String content = '';
  late int score = 0;
  late bool flag = false;

  GameLoginEntity();

  factory GameLoginEntity.fromJson(Map<String, dynamic> json) => $GameLoginEntityFromJson(json);

  Map<String, dynamic> toJson() => $GameLoginEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
