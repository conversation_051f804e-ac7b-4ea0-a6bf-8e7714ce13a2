import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/team_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/team_entity.g.dart';

@JsonSerializable()
class TeamEntity {
  int userTeamLevel = 0;
  List<TeamTeamConfig> teamConfigList = [];

  TeamEntity();

  factory TeamEntity.fromJson(Map<String, dynamic> json) => $TeamEntityFromJson(json);

  Map<String, dynamic> toJson() => $TeamEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class TeamTeamConfig {
  int teamLevel = 0;
  double betRebate = 0;
  double cashinRebate = 0;
  double maxAmount = 0;

  TeamTeamConfig();

  factory TeamTeamConfig.fromJson(Map<String, dynamic> json) => $TeamTeamConfigFromJson(json);

  Map<String, dynamic> toJson() => $TeamTeamConfigToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
