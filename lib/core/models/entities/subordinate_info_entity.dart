import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/subordinate_info_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/subordinate_info_entity.g.dart';

@JsonSerializable()
class SubordinateInfoEntity {
	late int subordinateCount = 0;
	late double commissionEarned;

	SubordinateInfoEntity();

	factory SubordinateInfoEntity.fromJson(Map<String, dynamic> json) => $SubordinateInfoEntityFromJson(json);

	Map<String, dynamic> toJson() => $SubordinateInfoEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}