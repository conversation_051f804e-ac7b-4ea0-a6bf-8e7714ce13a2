import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/lottery_current_status_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/lottery_current_status_entity.g.dart';

@JsonSerializable()
class LotteryCurrentStatusEntity {
	dynamic todayWin;
	late String close = '';
	late String open = '';
	late String order = '';
	late String serverDate = '';
	late int rebate = 0;
	late bool enable = false;
	late int countDown = 0;
	late bool start = false;

	LotteryCurrentStatusEntity();

	factory LotteryCurrentStatusEntity.fromJson(Map<String, dynamic> json) => $LotteryCurrentStatusEntityFromJson(json);

	Map<String, dynamic> toJson() => $LotteryCurrentStatusEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}