import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/third_party_wallet_info_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/third_party_wallet_info_entity.g.dart';

@JsonSerializable()
class ThirdPartyWalletInfoEntity {
	late String userNo;
	late int channelId;
	late String walletAccount;
	late double balance;

	ThirdPartyWalletInfoEntity();

	factory ThirdPartyWalletInfoEntity.fromJson(Map<String, dynamic> json) => $ThirdPartyWalletInfoEntityFromJson(json);

	Map<String, dynamic> toJson() => $ThirdPartyWalletInfoEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}