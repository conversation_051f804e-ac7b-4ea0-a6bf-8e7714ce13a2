import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/notice_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/notice_entity.g.dart';

@JsonSerializable()
class NoticeEntity {
	int id = 0;
	String siteMessageTitle = '';
	String siteMessageContent = '';
	String createTime = '';
	int siteMessageRead = 0;

	NoticeEntity();

	factory NoticeEntity.fromJson(Map<String, dynamic> json) => $NoticeEntityFromJson(json);

	Map<String, dynamic> toJson() => $NoticeEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}