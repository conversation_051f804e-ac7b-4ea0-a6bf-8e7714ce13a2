import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/lottery_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/lottery_entity.g.dart';

@JsonSerializable()
class LotteryListEntity {
	late List<LotteryGroup> list = [];

	LotteryListEntity();

	factory LotteryListEntity.fromJson(Map<String, dynamic> json) => $LotteryListEntityFromJson(json);

	Map<String, dynamic> toJson() => $LotteryListEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class LotteryGroup {
	late int id = 0;
	late String groupName = '';
	late int groupType = 0;
	late int groupStatus = 0;
	late int groupSort = 0;
	late List<Lottery> list = [];
	late String iconUrl = '';
	late bool isLogin = true;

	LotteryGroup();

	factory LotteryGroup.fromJson(Map<String, dynamic> json) => $LotteryGroupFromJson(json);

	Map<String, dynamic> toJson() => $LotteryGroupToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class Lottery {
	late int id = 0;
	late String lotteryCode = '';
	late String lotteryName = '';
	late String imgUrl = '';
	late int sortId = 0;
	dynamic lotteryStatus;
	dynamic officialStatus;
	late int gameCategoryId = 0;
	late String gameClassCode = '';
	late String gameCategoryCode = '';
	dynamic isDelete;
	bool isFav = false;

	Lottery();

	factory Lottery.fromJson(Map<String, dynamic> json) => $LotteryFromJson(json);

	Map<String, dynamic> toJson() => $LotteryToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}