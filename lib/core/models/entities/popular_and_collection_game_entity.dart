import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/popular_and_collection_game_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/popular_and_collection_game_entity.g.dart';

@JsonSerializable()
class PopularAndCollectionGameEntity {
	/// 用户收藏
	late List<PopularGame> userSavourGame = [];
	/// 热门游戏
	late List<PopularGame> popularGame = [];
	/// 热门场馆
	late List<PopularVenue> popularVenue = [];

	PopularAndCollectionGameEntity();

	factory PopularAndCollectionGameEntity.fromJson(Map<String, dynamic> json) => $PopularAndCollectionGameEntityFromJson(json);

	Map<String, dynamic> toJson() => $PopularAndCollectionGameEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class PopularVenue {
	late String name = '';
	late int platformId = 0;
	late String platformCode = '';
	late String iconUrl = '';
	late String recommendTime = '';
	late int gameType = 2; /// 1.彩票 2.三方
	late bool isLogin = false;
	dynamic clickNum;
	dynamic popularGames;

	PopularVenue();

	factory PopularVenue.fromJson(Map<String, dynamic> json) => $PopularVenueFromJson(json);

	Map<String, dynamic> toJson() => $PopularVenueToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class PopularGame {
	late int id = 0;
	late String gameClassCode = '';
	late String gameClassName = '';
	late String categoryCode = '';
	late String gameCode = '';
	late String gameName = '';
	late int platformId = 0;
	late String platformName = '';
	late String platformCode = '';
	late String iconUrl = '';
	late int gameType = 0;
	late String recommendTime = '';
	late bool isLogin = false;
	dynamic clickNum;

	bool savour = false;

	PopularGame();

	factory PopularGame.fromJson(Map<String, dynamic> json) => $PopularGameFromJson(json);

	Map<String, dynamic> toJson() => $PopularGameToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}