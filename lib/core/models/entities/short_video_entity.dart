import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/short_video_entity.g.dart';
import 'dart:convert';
import 'package:flutter/foundation.dart';
export 'package:wd/generated/json/short_video_entity.g.dart';

@JsonSerializable()
class ShortVideoEntityList {
	List<ShortVideoEntity> list = [];

	ShortVideoEntityList();

	factory ShortVideoEntityList.fromJson(Map<String, dynamic> json) => $ShortVideoEntityListFromJson(json);

	Map<String, dynamic> toJson() => $ShortVideoEntityListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ShortVideoEntity {
	late int videoId = 0;
	late String videoTitle = '';
	late String lineTitle = '';
	late String lineUrl = '';
	late String videoImage = '';
	late int likes = 0;
	@JSONField(name: "isLiked")
	bool _isLiked = false;
	late int videoTime = 0;
	bool isNormal = true; // 区分黑料、短视频

	String testIndex = '0';
	late bool isPlaying = true;
	@JSONField(serialize: false, deserialize: false)
	late Duration duration = Duration.zero;
	@JSONField(serialize: false, deserialize: false)
	late Duration currentDuration = Duration.zero;
	@JSONField(serialize: false, deserialize: false)
	late ValueNotifier<bool> isLikedNotifier = ValueNotifier(false);

	bool get isLiked => _isLiked;
	set isLiked(bool value) {
		_isLiked = value;
		isLikedNotifier.value = value;
	}

	ShortVideoEntity() {
		isLikedNotifier.value = _isLiked;
	}

	factory ShortVideoEntity.fromJson(Map<String, dynamic> json) {
		final entity = $ShortVideoEntityFromJson(json);
		
		entity.isLikedNotifier.value = entity._isLiked;
		return entity;
	}

	Map<String, dynamic> toJson() => $ShortVideoEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}