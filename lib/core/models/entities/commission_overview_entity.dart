import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/commission_overview_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/commission_overview_entity.g.dart';

@JsonSerializable()
class CommissionOverviewEntity {
  double totalCommission = 0;
  double commission = 0;
  double yesterdayCommission = 0;

  CommissionOverviewEntity();

  factory CommissionOverviewEntity.fromJson(Map<String, dynamic> json) => $CommissionOverviewEntityFromJson(json);

  Map<String, dynamic> toJson() => $CommissionOverviewEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
