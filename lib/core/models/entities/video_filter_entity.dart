import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/video_filter_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/video_filter_entity.g.dart';

@JsonSerializable()
class VideoFilterEntityList {
	List<VideoFilterEntity> list = [];

	VideoFilterEntityList();

	factory VideoFilterEntityList.fromJson(Map<String, dynamic> json) => $VideoFilterEntityListFromJson(json);

	Map<String, dynamic> toJson() => $VideoFilterEntityListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class VideoFilterEntity {
	late List<String> values = [];
	late String title = '';
	late String type = '';

	VideoFilterEntity();

	factory VideoFilterEntity.fromJson(Map<String, dynamic> json) => $VideoFilterEntityFromJson(json);

	Map<String, dynamic> toJson() => $VideoFilterEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}