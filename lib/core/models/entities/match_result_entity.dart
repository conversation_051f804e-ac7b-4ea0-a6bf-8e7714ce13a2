import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/match_result_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/match_result_entity.g.dart';

@JsonSerializable()
class MatchResultEntity {
	late String issue = '';
	late String issueTime = '';
	late String result = '';
	late List<dynamic> recentOneHandredCount = [];

	MatchResultEntity();

	factory MatchResultEntity.fromJson(Map<String, dynamic> json) => $MatchResultEntityFromJson(json);

	Map<String, dynamic> toJson() => $MatchResultEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}