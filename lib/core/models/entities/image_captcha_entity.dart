import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/image_captcha_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/image_captcha_entity.g.dart';

@JsonSerializable()
class ImageCaptchaEntity {
  late String img = '';
  late String uuid = '';

  ImageCaptchaEntity();

  factory ImageCaptchaEntity.fromJson(Map<String, dynamic> json) => $ImageCaptchaEntityFromJson(json);

  Map<String, dynamic> toJson() => $ImageCaptchaEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
