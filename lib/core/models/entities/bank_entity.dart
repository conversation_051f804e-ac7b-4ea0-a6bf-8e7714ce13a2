import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/bank_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/bank_entity.g.dart';

@JsonSerializable()
class BankEntityList {
  List<BankEntity> list = [];

  BankEntityList();

  factory BankEntityList.fromJson(Map<String, dynamic> json) => $BankEntityListFromJson(json);

  Map<String, dynamic> toJson() => $BankEntityListToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class BankEntity {
  late int id = 0;
  late String bankCode = '';
  late String bankName = '';
  late String createTime = '';

  BankEntity();

  factory BankEntity.fromJson(Map<String, dynamic> json) => $BankEntityFromJson(json);

  Map<String, dynamic> toJson() => $BankEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
