import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/game_notice_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/game_notice_entity.g.dart';

@JsonSerializable()
class GameNoticeEntity {
	late String noticeTitle;
	late int intoHistory;

	GameNoticeEntity();

	factory GameNoticeEntity.fromJson(Map<String, dynamic> json) => $GameNoticeEntityFromJson(json);

	Map<String, dynamic> toJson() => $GameNoticeEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}