import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/notification_alert_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/notification_alert_entity.g.dart';

@JsonSerializable()
class NotificationAlertListEntity {
  late List<NotificationAlertEntity> list = [];

  NotificationAlertListEntity();

  factory NotificationAlertListEntity.fromJson(Map<String, dynamic> json) => $NotificationAlertListEntityFromJson(json);

  Map<String, dynamic> toJson() => $NotificationAlertListEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class NotificationAlertEntity {
  late int id = 0;
  late int noticeType = 0;
  late String noticeTitle = '';
  late String noticeContent = '';
  late String beginTime = '';
  late String endTime = '';
  late int receiveType = 0;
  late int noticeSeq = 0;
  late int noticeStatus = 0;
  late String createTime = '';
  late int intoHistory = 0;


  late int jumpStatus = 0;   // 10内部跳转链接 12外部跳转链接 13跳相关场馆 14不跳转
  late String jumpUrl = '';
  late int gameBelong = 0;
  late int venueId = 0;
  late String platformCode = '';
  late int gameId = 0;

  NotificationAlertEntity();

  factory NotificationAlertEntity.fromJson(Map<String, dynamic> json) => $NotificationAlertEntityFromJson(json);

  Map<String, dynamic> toJson() => $NotificationAlertEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
