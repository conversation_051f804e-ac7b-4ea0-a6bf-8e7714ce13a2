import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/chat_type_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/chat_type_entity.g.dart';

@JsonSerializable()
class ChatTypeEntity {
	late int type = 0;
	late String url = '';

	ChatTypeEntity();

	factory ChatTypeEntity.fromJson(Map<String, dynamic> json) => $ChatTypeEntityFromJson(json);

	Map<String, dynamic> toJson() => $ChatTypeEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}