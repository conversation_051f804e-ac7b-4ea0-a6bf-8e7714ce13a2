import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/login_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/login_entity.g.dart';

@JsonSerializable()
class LoginEntity {
  late String token = '';
  late LoginTokenUser tokenUser;

  LoginEntity();

  factory LoginEntity.fromJson(Map<String, dynamic> json) => $LoginEntityFromJson(json);

  Map<String, dynamic> toJson() => $LoginEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class LoginTokenUser {
  late String token = '';
  late int userId = 0;
  late String userNo = '';
  late int agentId = 0;
  late int generalAgentId = 0;
  late int userType = 0;
  late int enableTransferAmount = 0;
  late int initPwd = 0;
  late String operator = '';

  LoginTokenUser();

  factory LoginTokenUser.fromJson(Map<String, dynamic> json) => $LoginTokenUserFromJson(json);

  Map<String, dynamic> toJson() => $LoginTokenUserToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
