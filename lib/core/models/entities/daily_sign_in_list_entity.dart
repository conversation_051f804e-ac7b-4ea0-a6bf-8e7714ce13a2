import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/daily_sign_in_list_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/daily_sign_in_list_entity.g.dart';

@JsonSerializable()
class DailySignInList {
	late List<DailySignInEntity> list = [];

	DailySignInList();

	factory DailySignInList.fromJson(Map<String, dynamic> json) => $DailySignInListFromJson(json);

	Map<String, dynamic> toJson() => $DailySignInListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class DailySignInEntity {
	late int id = 0;
	late String paramName = '';
	late int paramNum = 0;
	late int auditMultiple = 0;
	late bool isSign = false;
	late DateTime dateTime;

	DailySignInEntity();

	factory DailySignInEntity.fromJson(Map<String, dynamic> json) => $DailySignInEntityFromJson(json);

	Map<String, dynamic> toJson() => $DailySignInEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}