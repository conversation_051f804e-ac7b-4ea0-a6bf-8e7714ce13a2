import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/app_version_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/app_version_entity.g.dart';

@JsonSerializable()
class AppVersionEntity {
	int id = 0;
	String version = '';
	bool forceUpdate = false;
	String releaseNotes = '';
	String url = '';
	String createTime = '';

	AppVersionEntity();

	factory AppVersionEntity.fromJson(Map<String, dynamic> json) => $AppVersionEntityFromJson(json);

	Map<String, dynamic> toJson() => $AppVersionEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}