import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/bet_record_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/bet_record_entity.g.dart';

@JsonSerializable()
class BetRecordEntity {
	late BetRecordPage page;
	late int betAmountToday = 0;
	late int totalWinToday = 0;
	late int totalSendAmount = 0;

	BetRecordEntity();

	factory BetRecordEntity.fromJson(Map<String, dynamic> json) => $BetRecordEntityFromJson(json);

	Map<String, dynamic> toJson() => $BetRecordEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class BetRecordPage {
	late List<BetRecordPageRecords> records = [];
	late int total = 0;
	late int size = 0;
	late int current = 0;
	late List<BetRecordPageOrders> orders = [];
	late BetRecordPageOptimizeCountSql optimizeCountSql;
	late BetRecordPageSearchCount searchCount;
	late bool optimizeJoinOfCountSql = false;
	late int maxLimit = 0;
	late String countId = '';
	late int pages = 0;

	BetRecordPage();

	factory BetRecordPage.fromJson(Map<String, dynamic> json) => $BetRecordPageFromJson(json);

	Map<String, dynamic> toJson() => $BetRecordPageToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class BetRecordPageRecords {
	late String betOrderNo = '';
	late String betTime = '';
	late String lotteryName = '';
	late String periodId = '';
	late String itemType = '';
	late String itemObject = '';
	late String userNo = '';
	late int odds = 0;
	late int betAmount = 0;
	late int winAmount = 0;
	late int orderStatus = 0;
	late int returnRate = 0;
	late int returnAmount = 0;
	late String openResult = '';
	late int sendAmount = 0;
	late String lotteryOptions = '';
	late int gameResult = 0;
	late String gameCategoryCode = '';
	late int accountMoney = 0;
	late String betIp = '';
	late String ipAddress = '';
	late String requestUrl = '';

	BetRecordPageRecords();

	factory BetRecordPageRecords.fromJson(Map<String, dynamic> json) => $BetRecordPageRecordsFromJson(json);

	Map<String, dynamic> toJson() => $BetRecordPageRecordsToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class BetRecordPageOrders {
	late String column = '';
	late bool asc = false;

	BetRecordPageOrders();

	factory BetRecordPageOrders.fromJson(Map<String, dynamic> json) => $BetRecordPageOrdersFromJson(json);

	Map<String, dynamic> toJson() => $BetRecordPageOrdersToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class BetRecordPageOptimizeCountSql {


	BetRecordPageOptimizeCountSql();

	factory BetRecordPageOptimizeCountSql.fromJson(Map<String, dynamic> json) => $BetRecordPageOptimizeCountSqlFromJson(json);

	Map<String, dynamic> toJson() => $BetRecordPageOptimizeCountSqlToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class BetRecordPageSearchCount {


	BetRecordPageSearchCount();

	factory BetRecordPageSearchCount.fromJson(Map<String, dynamic> json) => $BetRecordPageSearchCountFromJson(json);

	Map<String, dynamic> toJson() => $BetRecordPageSearchCountToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}