import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/commission_bet_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/commission_bet_entity.g.dart';

@JsonSerializable()
class CommissionBetEntity {
  List<CommissionBetList>? list;

  CommissionBetEntity();

  factory CommissionBetEntity.fromJson(Map<String, dynamic> json) =>
      $CommissionBetEntityFromJson(json);

  Map<String, dynamic> toJson() => $CommissionBetEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class CommissionBetList {
  String? type;
  List<CommissionBetListThirdList>? thirdList;

  CommissionBetList();

  factory CommissionBetList.fromJson(Map<String, dynamic> json) =>
      $CommissionBetListFromJson(json);

  Map<String, dynamic> toJson() => $CommissionBetListToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class CommissionBetListThirdList {
  String? platformName;
  List<CommissionBetListThirdListList>? list;

  CommissionBetListThirdList();

  factory CommissionBetListThirdList.fromJson(Map<String, dynamic> json) =>
      $CommissionBetListThirdListFromJson(json);

  Map<String, dynamic> toJson() => $CommissionBetListThirdListToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class CommissionBetListThirdListList {
  String? gameClassCode;
  String? gameClassName;
  String? categoryCode;
  String? platformName;
  int? childLevel;
  int? teamLevel;
  double? commissionRate;
  double? commissionCap;

  CommissionBetListThirdListList();

  factory CommissionBetListThirdListList.fromJson(Map<String, dynamic> json) =>
      $CommissionBetListThirdListListFromJson(json);

  Map<String, dynamic> toJson() => $CommissionBetListThirdListListToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
