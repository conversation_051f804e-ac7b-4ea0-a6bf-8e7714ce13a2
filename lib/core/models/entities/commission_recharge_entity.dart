import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/commission_recharge_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/commission_recharge_entity.g.dart';

@JsonSerializable()
class CommissionRechargeEntity {
  List<CommissionRechargeList>? list;

  CommissionRechargeEntity();

  factory CommissionRechargeEntity.fromJson(Map<String, dynamic> json) =>
      $CommissionRechargeEntityFromJson(json);

  Map<String, dynamic> toJson() => $CommissionRechargeEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class CommissionRechargeList {
  int? teamLevel;
  double? commissionRate;

  CommissionRechargeList();

  factory CommissionRechargeList.fromJson(Map<String, dynamic> json) =>
      $CommissionRechargeListFromJson(json);

  Map<String, dynamic> toJson() => $CommissionRechargeListToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
