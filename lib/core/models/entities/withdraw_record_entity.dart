import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/withdraw_record_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/withdraw_record_entity.g.dart';

@JsonSerializable()
class WithdrawRecordEntity {
	late List<WithdrawRecord> records = [];
	late int total = 0;
	late int size = 0;
	late int current = 0;
	late int pages = 0;

	WithdrawRecordEntity();

	factory WithdrawRecordEntity.fromJson(Map<String, dynamic> json) => $WithdrawRecordEntityFromJson(json);

	Map<String, dynamic> toJson() => $WithdrawRecordEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class WithdrawRecord {
	late int id = 0;
	late String transactionNo = '';
	late int userId = 0;
	late String userNo = '';
	late String realName = '';
	late String  cardNo = '';
	late String  bankName = '';
	late int orderInitialAmount = 0;
	late double orderAmount = 0;
	dynamic totalBetAmount;
	dynamic betAmountRate;
	late double serviceCharge;
	late double reduceAmount;
	late double serviceChargeRate;
	dynamic thanServiceChargeRate;
	late double finalAmount;
	late String cashoutWayCode = '';
	late String cashoutWayName = '';
	late String cashoutTypeCode = '';
	late String cashoutTypeName = '';
	late String requestTime = '';
	late int orderStatus = 0; // 订单状态： 0待审核 1已通过 2已取消 3已拒绝
	late String operateTime = '';
	late String operator = '';
	late int isFirst = 0;
	late String remark = '';
	late String refusalRemark = '';
	late bool read = true; // 是否已读，true是已读

	WithdrawRecord();

	factory WithdrawRecord.fromJson(Map<String, dynamic> json) => $WithdrawRecordFromJson(json);

	Map<String, dynamic> toJson() => $WithdrawRecordToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class WithdrawStatusEntity {
  bool? status = false;
  int statusCode = 0;
  String? errorMessage = '';

	WithdrawStatusEntity();

	factory WithdrawStatusEntity.fromJson(Map<String, dynamic> json) => $WithdrawStatusEntityFromJson(json);

	Map<String, dynamic> toJson() => $WithdrawStatusEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}