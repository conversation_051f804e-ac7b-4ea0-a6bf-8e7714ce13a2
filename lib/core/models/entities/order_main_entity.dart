import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/order_main_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/order_main_entity.g.dart';

@JsonSerializable()
class OrderMainEntityList {
  List<OrderMainEntity> list = [];

  OrderMainEntityList();

  factory OrderMainEntityList.fromJson(Map<String, dynamic> json) => $OrderMainEntityListFromJson(json);

  Map<String, dynamic> toJson() => $OrderMainEntityListToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class OrderMainEntity {
  late String gameClassCode = '';
  late String gameClassName = '';
  late double rebate = 0;
  late double totalBetAmount = 0;
  late double totalWin = 0;
  late double totalSendAmount = 0;

  OrderMainEntity();

  factory OrderMainEntity.fromJson(Map<String, dynamic> json) => $OrderMainEntityFromJson(json);

  Map<String, dynamic> toJson() => $OrderMainEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class OrderPlatformEntityList {
  List<OrderPlatformEntity> list = [];

  OrderPlatformEntityList();

  factory OrderPlatformEntityList.fromJson(Map<String, dynamic> json) => $OrderPlatformEntityListFromJson(json);

  Map<String, dynamic> toJson() => $OrderPlatformEntityListToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class OrderPlatformEntity {
  late int platFormId = 0;
  late String platFormName = '';
  late int rebate = 0;
  late String gameClassCode = '';
  late String categoryCode = '';
  late String gameClassName = '';
  late double totalSendAmount;
  late double totalBetAmount;
  late double totalWin;

  OrderPlatformEntity();

  factory OrderPlatformEntity.fromJson(Map<String, dynamic> json) => $OrderPlatformEntityFromJson(json);

  Map<String, dynamic> toJson() => $OrderPlatformEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
