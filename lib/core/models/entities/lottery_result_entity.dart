import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/lottery_result_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/lottery_result_entity.g.dart';

@JsonSerializable()
class LotteryResultEntity {
	late List<LotteryResultRecords> records = [];
	late int total = 0;
	late int size = 0;
	late int current = 0;
	late int pages = 0;

	LotteryResultEntity();

	factory LotteryResultEntity.fromJson(Map<String, dynamic> json) => $LotteryResultEntityFromJson(json);

	Map<String, dynamic> toJson() => $LotteryResultEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class LotteryResultRecords {
	late int id = 0;
	late int lotteryId = 0;
	late String belongDate = '';
	late String periodId = '';
	late String beginTime = '';
	late String endTime = '';
	late String openTime = '';
	late int totalBetCount = 0;
	late double totalBetAmount;
	late int totalBetPerson = 0;
	late double totalWinAmount;
	late String openResult = '';
	late int openStatus = 0;
	late String createTime = '';
	late int periodNum = 0;
	dynamic handler;

	LotteryResultRecords();

	factory LotteryResultRecords.fromJson(Map<String, dynamic> json) => $LotteryResultRecordsFromJson(json);

	Map<String, dynamic> toJson() => $LotteryResultRecordsToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}