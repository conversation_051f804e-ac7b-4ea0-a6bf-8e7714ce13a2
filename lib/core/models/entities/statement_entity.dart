import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/statement_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/statement_entity.g.dart';

@JsonSerializable()
class StatementEntity {
  late List<StatementRecords> records = [];
  late int total = 0;
  late int size = 0;
  late int current = 0;
  late int pages = 0;

  StatementEntity();

  factory StatementEntity.fromJson(Map<String, dynamic> json) => $StatementEntityFromJson(json);

  Map<String, dynamic> toJson() => $StatementEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class StatementRecords {
  late String id = '';
  late int userId = 0;
  late String userNo = '';
  late int agentId = 0;
  late int generalAgentId = 0;
  late String fundChangeWayCode = '';
  late String fundChangeWayName = '';
  late String fundChangeTypeCode = '';
  late String fundChangeTypeName = '';
  late double beforeAccountAmount = 0;
  late double changeAmount;
  late double afterAccountAmount = 0;
  dynamic transactionNo;
  late String remark = '';
  dynamic operator;
  late String createTime = '';
  dynamic currencyCode;
  late int platformId = 0;
  dynamic lotteryId;
  dynamic lotteryName;
  dynamic belongDate;
  dynamic createTimeReport;

  StatementRecords();

  factory StatementRecords.fromJson(Map<String, dynamic> json) => $StatementRecordsFromJson(json);

  Map<String, dynamic> toJson() => $StatementRecordsToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
