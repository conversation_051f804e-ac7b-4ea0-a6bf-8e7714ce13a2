import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/order_channel_list_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/order_channel_list_entity.g.dart';

@JsonSerializable()
class OrderChannelListEntity {
  OrderChannelListPage page = OrderChannelListPage();
  double totalBetAmount = 0;
  double totalWinToday = 0;
  double totalSendAmount = 0;
  double totalRevenue = 0;

  OrderChannelListEntity();

  factory OrderChannelListEntity.fromJson(Map<String, dynamic> json) => $OrderChannelListEntityFromJson(json);

  Map<String, dynamic> toJson() => $OrderChannelListEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class OrderChannelListPage {
  late List<OrderChannelListPageRecords> records = [];
  late int total = 0;
  late int size = 0;
  late int current = 0;
  late int pages = 0;

  OrderChannelListPage();

  factory OrderChannelListPage.fromJson(Map<String, dynamic> json) => $OrderChannelListPageFromJson(json);

  Map<String, dynamic> toJson() => $OrderChannelListPageToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class OrderChannelListPageRecords {
  late int id = 0;
  late String channelUniqueId = '';
  late int thirdPlatformId = 0;
  late String thirdPlatformName = '';
  late int userId = 0;
  late String userNo = '';
  late int agentId = 0;
  late int generalAgentId = 0;
  late String gameClassCode = '';
  late String gameClassName = '';
  late String categoryCode = '';
  late String categoryName = '';
  late String gameCode = '';
  late String channelAccount = '';
  late String startTime = '';
  late String endTime = '';
  late String gameName = '';
  late String roomName = '';
  dynamic tableName;
  late double sendAmount;
  late double winAmount;
  late double betAmount;
  late double revenue;
  late String matchNumber = '';
  late double returnRate;
  late double returnAmount;
  late int gameResult = 0;
  dynamic recordOriginal;
  late String pullTime = '';

  OrderChannelListPageRecords();

  factory OrderChannelListPageRecords.fromJson(Map<String, dynamic> json) => $OrderChannelListPageRecordsFromJson(json);

  Map<String, dynamic> toJson() => $OrderChannelListPageRecordsToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
