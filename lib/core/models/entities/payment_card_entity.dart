import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/payment_card_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/payment_card_entity.g.dart';

@JsonSerializable()
class PaymentCardEntityList {
  List<PaymentCardEntity> list = [];

  PaymentCardEntityList();

  factory PaymentCardEntityList.fromJson(Map<String, dynamic> json) => $PaymentCardEntityListFromJson(json);

  Map<String, dynamic> toJson() => $PaymentCardEntityListToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class PaymentCardEntity {
  late int id = 0;
  late int userId = 0;
  late String userNo = '';
  late String bankCode = '';
  late String bankName = '';
  late String province = '';
  late String city = '';
  late String branchBankAddress = '';
  late String realName = '';
  late String cardNo = '';
  late String createTime = '';
  late int withdrawnCount = 0;
  late int useStatus = 0; // 0.启用1.停用
  late int type = 1;
  dynamic mainNetwork;

  PaymentCardEntity();

  factory PaymentCardEntity.fromJson(Map<String, dynamic> json) => $PaymentCardEntityFromJson(json);

  Map<String, dynamic> toJson() => $PaymentCardEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
