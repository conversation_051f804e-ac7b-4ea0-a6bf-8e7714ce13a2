import 'package:equatable/equatable.dart';
import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/lottery_odds_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/lottery_odds_entity.g.dart';

@JsonSerializable()
class LotteryOddsList {
	late List<LotteryOdds> list = [];

	LotteryOddsList();

	factory LotteryOddsList.fromJson(Map<String, dynamic> json) => $LotteryOddsListFromJson(json);

	Map<String, dynamic> toJson() => $LotteryOddsListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class LotteryOdds extends Equatable {
	late int id = 0;
	double? odds;
	late String itemType = '';
	late String itemObject = '';
	late int limitValue = 0;
	late int returnRate = 0;
	bool isSelected = false;

	LotteryOdds();

	factory LotteryOdds.fromJson(Map<String, dynamic> json) => $LotteryOddsFromJson(json);

	Map<String, dynamic> toJson() => $LotteryOddsToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}

  @override
  List<Object?> get props => [odds, isSelected];
}


class LotteryOddsSection {
	String name = '';
	int rule = 1;
	int? id;
	late String type = '';
	Map<String, String> infoMap = {};
	int multipleLimit = 10; // 单彩种最多选中数量
	int orderCount = 0; // 选中注单数量
	int selItemCount = 0; // 选中的item数量
	List<LotteryOdds> list = [];

	LotteryOddsSection({required this.name, required this.rule, this.multipleLimit = 10});

}

@JsonSerializable()
class LotteryOddsGroup {
	late String type = '';
	late String name = '';
	bool isSelected = false;
	int orderCount = 0;
	int? id;
	int selIndex = 0;
	late Map<String, LotteryOddsSection> sectionMap = {};

	LotteryOddsGroup();

	factory LotteryOddsGroup.fromJson(Map<String, dynamic> json) => $LotteryOddsGroupFromJson(json);

	Map<String, dynamic> toJson() => $LotteryOddsGroupToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}


