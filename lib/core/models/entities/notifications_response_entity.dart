import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/notifications_response_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/notifications_response_entity.g.dart';

@JsonSerializable()
class NotificationsResponseEntity {
  late List<NotificationsRecords> records = [];
  late int total = 0;
  late int size = 0;
  late int current = 0;
  late int pages = 0;

  NotificationsResponseEntity();

  factory NotificationsResponseEntity.fromJson(Map<String, dynamic> json) => $NotificationsResponseEntityFromJson(json);

  Map<String, dynamic> toJson() => $NotificationsResponseEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class NotificationsRecords {
  late int id = 0;
  late String siteMessageTitle = '';
  late String siteMessageContent = '';
  late String createTime = '';
  late int siteMessageRead = 0;

  NotificationsRecords();

  factory NotificationsRecords.fromJson(Map<String, dynamic> json) => $NotificationsRecordsFromJson(json);

  Map<String, dynamic> toJson() => $NotificationsRecordsToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
