import 'package:wd/core/utils/log_util.dart';

import 'home_banner_entity.dart';
import 'notification_alert_entity.dart';

enum JumpType {
  unknown(-999, '未知类型'),
  internalLink(10, '内部跳转链接'),
  externalLink(12, '外部跳转链接'),
  venueLink(13, '跳相关场馆'),
  gameLink(233, '跳相关游戏'),
  noJump(14, '不跳转');

  final int value;
  final String label;

  const JumpType(this.value, this.label);

  static JumpType fromValue(int value) {
    return JumpType.values.firstWhere(
      (e) => e.value == value,
      orElse: () => unknown,
    );
  }
}

class JumpModel {
  JumpModel({
    required this.type,
    required this.params,
    this.extraData,
  });

  final JumpType type; // 对应 jumpStatus 10.内部跳转链接 12.外部跳转链接 13.跳相关场馆 14.不跳转
  final String params; // venueId 或 jumpUrl 等，统一为 string
  final dynamic extraData; // 附加数据，如【type == 13的时候需要判断 gameBelong(新增)游戏或场馆所属: 1.彩票 2.三方游戏】

  factory JumpModel.fromHomeBannerEntity(HomeBannerEntity model) {
    final type = JumpType.fromValue(model.jumpStatus);

    switch (type) {
      case JumpType.internalLink:
      case JumpType.externalLink:
        if (model.jumpUrl.isEmpty) {
          throw ArgumentError('jumpUrl is required for ${type.label}');
        }
        return JumpModel(type: type, params: model.jumpUrl);

      case JumpType.gameLink:
      case JumpType.venueLink:
        if (model.gameId == 0) {
          if (model.venueId == 0) {
            throw ArgumentError('venueId is required for ${type.label}');
          }
          return JumpModel(
            type: type,
            params: model.platformCode.toString(),
          );
        } else {
          return JumpModel(
            type: JumpType.gameLink,
            params: model.gameId.toString(),
            extraData: model.gameBelong,
          );
        }

      case JumpType.unknown:
      case JumpType.noJump:
        return JumpModel(type: type, params: '');
    }
  }
  factory JumpModel.fromNotificationAlertEntity(NotificationAlertEntity model) {
    final type = JumpType.fromValue(model.jumpStatus);

    switch (type) {
      case JumpType.internalLink:
      case JumpType.externalLink:
        if (model.jumpUrl.isEmpty) {
          throw ArgumentError('jumpUrl is required for ${type.label}');
        }
        return JumpModel(type: type, params: model.jumpUrl, extraData: model.noticeTitle);

      case JumpType.gameLink:
      case JumpType.venueLink:
        if (model.gameId == 0) {
          if (model.venueId == 0) {
            throw ArgumentError('venueId is required for ${type.label}');
          }
          return JumpModel(
            type: type,
            params: model.platformCode
          );
        } else {
          return JumpModel(
            type: JumpType.gameLink,
            params: model.gameId.toString(),
            extraData: model.gameBelong,
          );
        }

      case JumpType.unknown:
      case JumpType.noJump:
        return JumpModel(type: type, params: '');
    }
  }

  Map<String, dynamic> toJson() => {
        'type': type.value,
        'params': params,
        'extraData': extraData,
      };
}
