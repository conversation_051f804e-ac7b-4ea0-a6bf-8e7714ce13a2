import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/activity_category_entity.g.dart';
import 'dart:convert';


@JsonSerializable()
class ActivityCategoryListEntity {
	late List<ActivityCategory> list = [];

	ActivityCategoryListEntity();

	factory ActivityCategoryListEntity.fromJson(Map<String, dynamic> json) => $ActivityCategoryListEntityFromJson(json);

	Map<String, dynamic> toJson() => $ActivityCategoryListEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}


@JsonSerializable()
class ActivityCategory {
	late int category = 0;
	late String categoryName = '';
	late String icon = '';

	ActivityCategory();

	factory ActivityCategory.fromJson(Map<String, dynamic> json) => $ActivityCategoryFromJson(json);

	Map<String, dynamic> toJson() => $ActivityCategoryToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}