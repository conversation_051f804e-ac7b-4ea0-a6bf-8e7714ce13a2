import 'dart:convert';

///  0:未开启,1:开启(红包没有抢完),2:已经抢完,3:没有抢完,但是被关闭了,4:已经过期,5:已经领取
enum RedEnvelopeStatus {
  notStarted(0), // 未开启    0:not open, - created but not published
  open(1), // 开启(可以抢)     1:open(red packet not grabbed) - published
  finished(2), // 已抢完      2:already grabbed, - grabbed by all users
  closed(3), // 已关闭        3:not grabbed but closed, -  admin closed the envelope if its mistake
  expired(4), // 已过期   4:expired, - expired get when user grab after the time is up
  userGrab(5), // 该用户已抢    5:user grabbed - user grabbed the envelope (user specified)
  notExist(500), // 红包不存在 500:red packet not exist
  unknown(-999); // 未知


  final int code;

  const RedEnvelopeStatus(this.code);

  // 通过 code 获取枚举值的方法
  static RedEnvelopeStatus fromCode(int code) {
    return RedEnvelopeStatus.values.firstWhere(
          (status) => status.code == code,
      orElse: () => RedEnvelopeStatus.notExist, // 返回默认值，而不是 null
    );
  }
}

class RedEnvelopeGrabInfo {
  RedEnvelopeStatus status;
  List<int> grabUserIdList;

  RedEnvelopeGrabInfo({
    required this.status,
    this.grabUserIdList = const [],
  });

  // 将 RedEnvelopeGrabInfo 转换为 JSON 字符串
  String toJsonString() {
    final Map<String, dynamic> data = {
      'status': status
          .toString()
          .split('.')
          .last, // 将枚举转换为字符串
      'grabUserIdList': grabUserIdList,
    };
    return jsonEncode(data);
  }

  // 从 JSON 字符串转换为 RedEnvelopeGrabInfo 对象
  factory RedEnvelopeGrabInfo.fromJsonString(String? jsonString) {
    if (jsonString == null || jsonString.isEmpty) {
      // 返回一个默认的实例，如果传入的 jsonString 是 null 或者为空
      return RedEnvelopeGrabInfo(
        status: RedEnvelopeStatus.unknown,
        grabUserIdList: [],
      );
    }

    final Map<String, dynamic> data = jsonDecode(jsonString);
    return RedEnvelopeGrabInfo(
      status: RedEnvelopeStatus.values.firstWhere(
            (e) =>
        e
            .toString()
            .split('.')
            .last == data['status'],
        orElse: () => RedEnvelopeStatus.unknown,
      ),
      grabUserIdList: List<int>.from(data['grabUserIdList'] ?? []),
    );
  }
}