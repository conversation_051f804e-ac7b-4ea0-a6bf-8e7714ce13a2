import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/winner_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/winner_entity.g.dart';

@JsonSerializable()
class WinnerEntityList {
	late List<WinnerEntity> list = [];

	WinnerEntityList();

	factory WinnerEntityList.fromJson(Map<String, dynamic> json) => $WinnerEntityListFromJson(json);

	Map<String, dynamic> toJson() => $WinnerEntityListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class WinnerEntity {
	late String nickName = '';
	late String sendAmount = '';
	late String gameName = '';
	late String gameId = '';
	late String thirdPlatformId = '';

	WinnerEntity();

	factory WinnerEntity.fromJson(Map<String, dynamic> json) => $WinnerEntityFromJson(json);

	Map<String, dynamic> toJson() => $WinnerEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}