import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/home_banner_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/home_banner_entity.g.dart';

@JsonSerializable()
class HomeBannerListEntity {
  late List<HomeBannerEntity> list = [];

  HomeBannerListEntity();

  factory HomeBannerListEntity.fromJson(Map<String, dynamic> json) => $HomeBannerListEntityFromJson(json);

  Map<String, dynamic> toJson() => $HomeBannerListEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class HomeBannerEntity {
  late int id = 0;
  late String bannerUrl = '';
  late int bannerSort = 0;

  late int jumpStatus = 0;
  late String jumpUrl = '';
  late int gameBelong = 0;
  late int venueId = 0;
  late String platformCode = '';
  late int gameId = 0;

  HomeBannerEntity();

  factory HomeBannerEntity.fromJson(Map<String, dynamic> json) => $HomeBannerEntityFromJson(json);

  Map<String, dynamic> toJson() => $HomeBannerEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
