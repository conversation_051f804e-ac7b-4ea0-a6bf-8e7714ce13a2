import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/statement_filter_way_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/statement_filter_way_entity.g.dart';


@JsonSerializable()
class StatementFilterWayList {
	late List<StatementFilterWay> list = [];

	StatementFilterWayList();

	factory StatementFilterWayList.fromJson(Map<String, dynamic> json) => $StatementFilterWayListFromJson(json);

	Map<String, dynamic> toJson() => $StatementFilterWayListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class StatementFilterWay {
	late int id = 0;
	late String changeWayCode = '';
	late String changeWayName = '';
	bool isSel = false;

	StatementFilterWay();

	factory StatementFilterWay.fromJson(Map<String, dynamic> json) => $StatementFilterWayFromJson(json);

	Map<String, dynamic> toJson() => $StatementFilterWayToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}


@JsonSerializable()
class StatementFilterTypeList {
	late List<StatementFilterType> list = [];

	StatementFilterTypeList();

	factory StatementFilterTypeList.fromJson(Map<String, dynamic> json) => $StatementFilterTypeListFromJson(json);

	Map<String, dynamic> toJson() => $StatementFilterTypeListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class StatementFilterType {
	late int id = 0;
	late String changeTypeCode = '';
	late String changeTypeName = '';
	late String changeWayCode = '';
	bool isSel = false;

	StatementFilterType();

	factory StatementFilterType.fromJson(Map<String, dynamic> json) => $StatementFilterTypeFromJson(json);

	Map<String, dynamic> toJson() => $StatementFilterTypeToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}