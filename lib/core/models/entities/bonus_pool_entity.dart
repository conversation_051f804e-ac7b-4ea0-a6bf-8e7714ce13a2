import 'package:wd/generated/json/base/json_field.dart';
import 'package:wd/generated/json/bonus_pool_entity.g.dart';
import 'dart:convert';
export 'package:wd/generated/json/bonus_pool_entity.g.dart';

@JsonSerializable()
class BonusPoolEntity {
  String? dzBonus = '';
  String? byBonus = '';

  BonusPoolEntity();

  factory BonusPoolEntity.fromJson(Map<String, dynamic> json) =>
      $BonusPoolEntityFromJson(json);

  Map<String, dynamic> toJson() => $BonusPoolEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
