import 'package:equatable/equatable.dart';
import 'package:wd/core/models/entities/game_entity.dart';
import 'package:wd/core/models/entities/game_v2_entity.dart';
import 'package:wd/core/models/entities/popular_and_collection_game_entity.dart';
import 'package:wd/core/utils/game_home_util.dart';
import 'package:wd/core/utils/log_util.dart';

class PopularSectionViewModel extends Equatable {
  final List<GameV2> gameList;
  final List<GamePlatformV2> venueList;
  final double sectionHeight;

  PopularSectionViewModel({
    this.gameList = const [],
    this.venueList = const [],
  }) : sectionHeight = GameHomeUtil.calculatePopularSectionHeight(
          gameLength: gameList.length,
          venueLength: venueList.length,
        );

  // 实现深复制
  PopularSectionViewModel clone() {
    return PopularSectionViewModel(
      gameList: List<GameV2>.from(gameList),  // 如果 Game 是值对象，直接复制即可
      venueList: List<GamePlatformV2>.from(venueList), // 如果 GamePlatform 是值对象，直接复制即可
    );
  }

  @override
  List<Object?> get props => [gameList, venueList, sectionHeight]; // 正确实现 props
}
