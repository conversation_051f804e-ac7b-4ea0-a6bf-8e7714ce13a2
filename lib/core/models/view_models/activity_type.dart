

import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/models/entities/activity_category_entity.dart';
import 'package:wd/core/models/entities/activity_list_entity.dart';

class ActivityGameTypeViewModel {

  late int category = 0;
  late String categoryName = '';
  late String icon = '';
  int pageNo = 1;
  List<ActivityRecords> list = [];
  NetState netState = NetState.loadingState;
  SimplyNetStatus refreshStatus = SimplyNetStatus.idle;
  bool? isNoMoreDataState;

  ActivityGameTypeViewModel({
    required this.category,
    required this.categoryName,
    required this.icon,
  });

  factory ActivityGameTypeViewModel.fromActivityCategory(ActivityCategory model) {
    return ActivityGameTypeViewModel(
      category: model.category,
      categoryName: model.categoryName,
      icon: model.icon,
    );
  }
}

class ActivityTaskTypeViewModel {

  late int category = 0;
  late String categoryName = '';
  late String icon = '';
  List<ActivityTask> list = [];
  NetState netState = NetState.initializeState;

  ActivityTaskTypeViewModel({
    required this.category,
    required this.categoryName,
    required this.icon,
  });

  factory ActivityTaskTypeViewModel.fromActivityCategory(ActivityCategory model) {
    return ActivityTaskTypeViewModel(
      category: model.category,
      categoryName: model.categoryName,
      icon: model.icon,
    );
  }
}