
class SponsorItem {
  final String name;
  final String bannerImage;
  final String detailImage;

  SponsorItem({
    required this.name,
    required this.bannerImage,
    required this.detailImage,
  });

  factory SponsorItem.fromJson(Map<String, dynamic> json) {
    return SponsorItem(
      name: json['name'],
      bannerImage: json['bannerImage'],
      detailImage: json['detailImage'],
    );
  }
}
