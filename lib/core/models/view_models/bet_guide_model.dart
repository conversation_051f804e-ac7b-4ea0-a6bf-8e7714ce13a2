class BetGuideItem {
  final String id;
  final String title;
  final String link;
  final String content;

  BetGuideItem({
    required this.id,
    required this.title,
    required this.link,
    required this.content,
  });

  factory BetGuideItem.fromJson(Map<String, dynamic> json) {
    return BetGuideItem(
      id: json['id'],
      title: json['title'],
      link: json['link'],
      content: json['content'],
    );
  }
}