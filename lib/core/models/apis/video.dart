import 'package:wd/core/models/entities/short_video_entity.dart';
import 'package:wd/core/models/entities/video_entity.dart';
import 'package:wd/core/models/entities/video_filter_entity.dart';
import 'package:wd/core/models/entities/video_hot_movies_entity.dart';
import 'package:wd/core/models/entities/video_list_entity.dart';
import 'package:wd/core/singletons/user_cubit.dart';
import 'package:wd/core/utils/global_config.dart';
import 'package:wd/core/utils/http/https.dart';
import 'package:wd/injection_container.dart';

import '../entities/filter_video_list_entity.dart';
import '../entities/video_hot_tag_entity.dart';

class VideoApi {
  static bool get isHideVideoPage =>
      !GlobalConfig.needShowVideoPage() || !(sl<UserCubit>().state.userInfo?.tiktokTabVisible ?? true);

  /// 获取首页视频列表
  static Future<List<ShortVideoEntity>> fetchShortVideoList(
      {required bool isNormal, bool forceRefresh = false}) async {
    if (isHideVideoPage) {
      return <ShortVideoEntity>[];
    }
    ResponseModel res = await Http().request<ShortVideoEntityList>(
      ApiConstants.shortVideoList,
      needSignIn: false,
      needEncrypt: true,
      params: {
        'videoType': isNormal ? "2" : "1", // 1 黑料 2短视频
        'forceRefresh': forceRefresh,
      },
    );
    if (res.isSuccess && res.data != null) {
      List<ShortVideoEntity> list =
          List<ShortVideoEntity>.from(res.data.list).map((e) => e..isNormal = isNormal).toList();
      return list;
    }
    return <ShortVideoEntity>[];
  }

  /// 视频-喜欢、取消喜欢
  static Future<bool> operaVideoLike({required bool isLike, required int videoId}) async {
    if (isHideVideoPage) return false;
    ResponseModel res = await Http().request(
      isLike ? ApiConstants.videoOperaLike : ApiConstants.videoOperaUnlike,
      params: {'videoId': videoId},
    );
    return res.isSuccess;
  }

  /// 视频-已看
  static Future<bool> submitWatchedVideoId({required int videoId, required bool isNormal}) async {
    if (isHideVideoPage) return false;
    ResponseModel res = await Http().request(
      ApiConstants.videoWatched,
      needEncrypt: true,
      params: {
        'videoId': videoId,
        'videoType': isNormal ? "2" : "1",
        // 'deviceId': SystemUtil.deviceId,
      },
    );
    return res.isSuccess;
  }

  /// 获取首页视频列表
  static Future<VideoListEntity> fetchHomeVideoList({
    required int pageNo,
    required String videoCategory,
    String? keyword,
  }) async {
    if (isHideVideoPage) {
      return VideoListEntity();
    }
    ResponseModel res = await Http().request<VideoListEntity>(ApiConstants.videoList,
        params: {
          "pageNo": pageNo,
          "pageSize": 20,
          "videoCategory": videoCategory,
          if (keyword != null) "videoTitle": keyword
        },
        needSignIn: false,
        needEncrypt: true);

    if (res.isSuccess && res.data != null) {
      return res.data;
    } else {
      return VideoListEntity();
    }
  }

  /// 获取首页视频列表的筛选条件
  static Future<List<VideoFilterEntity>> fetchHomeVideoFilterList() async {
    if (isHideVideoPage) {
      return <VideoFilterEntity>[];
    }

    ResponseModel res = await Http()
        .request<VideoFilterEntityList>(ApiConstants.videoFilterList, method: HttpMethod.get, needSignIn: false);
    if (res.isSuccess && res.data != null) {
      List<VideoFilterEntity> list = res.data.list;
      return list;
    } else {
      return [];
    }
  }

  /// 获取首页视频的线路列表
  static Future<VideoDetailEntity?> fetchVideoDetail({required int id}) async {
    if (isHideVideoPage) {
      return null;
    }

    ResponseModel res = await Http().request<VideoDetailEntity>(
      ApiConstants.videoDetail,
      needEncrypt: true,
      needSignIn: false,
      params: {"id": id},
    );

    if (res.isSuccess && res.data != null) {
      return res.data;
    } else {
      return null;
    }
  }

  /// 获取热门视频标签
  static Future<VideoHotTagEntity?> fetchVideoHotTags() async {
    if (isHideVideoPage) {
      return null;
    }

    ResponseModel res = await Http().request<VideoHotTagEntity>(
      ApiConstants.videoHotTags,
      method: HttpMethod.get,
      needSignIn: false,
    );
    if (res.isSuccess && res.data != null) {
      return res.data;
    } else {
      return null;
    }
  }

  /// 获取视频vip剩余天数
  static Future<VideoVipRemainDayEntity?> fetchRemainingVipDays() async {
    ResponseModel res = await Http().request<VideoVipRemainDayEntity>(
      ApiConstants.videoVIPRemainDays,
      method: HttpMethod.get,
      
      isFormUrlEncoded: true,
    );

    if (res.isSuccess && res.data != null) {
      return res.data;
    } else {
      return null;
    }
  }

  /// 获取 电影热门分类分组前6部电影
  static Future<VideoHotMoviesEntity> fetchHotVideo({
    required int pageNo,
  }) async {
    if (isHideVideoPage) {
      return VideoHotMoviesEntity();
    }

    ResponseModel res = await Http().request<VideoHotMoviesEntity>(
      ApiConstants.videoHotMovies,
      params: {
        "pageNo": pageNo,
        "pageSize": 20,
      },
      needEncrypt: true,
      needSignIn: false,
    );
    if (res.isSuccess && res.data != null) {
      return res.data;
    } else {
      return VideoHotMoviesEntity();
    }
  }

  /// 获取热门视频列表
  static Future<FilterVideoListEntity> fetchHotVideoList({
    required int pageNo,
    String? videoCategory,
    String? videoTitle,
    String? videoYear,
    String? videoCountry,
    String? isHotMovies,
  }) async {
    if (isHideVideoPage) {
      return FilterVideoListEntity();
    }

    ResponseModel res = await Http().request<FilterVideoListEntity>(
      ApiConstants.videoHotMoviesList,
      needEncrypt: true,
      needSignIn: false,
      params: {
        "pageNo": pageNo,
        "pageSize": 18,
        if (videoCategory != null && videoCategory.isNotEmpty) "videoCategory": videoCategory,
        if (videoTitle != null && videoTitle.isNotEmpty) "videoTitle": videoTitle,
        if (videoYear != null && videoYear.isNotEmpty) "videoYear": videoYear,
        if (videoCountry != null && videoCountry.isNotEmpty) "videoCountry": videoCountry,
        if (isHotMovies != null && isHotMovies.isNotEmpty) "isHotMovies": isHotMovies,
      },
    );
    if (res.isSuccess && res.data != null) {
      return res.data;
    } else {
      return FilterVideoListEntity();
    }
  }

  /// 提交兑换码
  static Future<bool> submitVideoCoupon({coupon}) async {
    ResponseModel res = await Http().request(
      ApiConstants.submitVideoCoupon,
      method: HttpMethod.get,
      params: {'redemptionCode': coupon, },
      isFormUrlEncoded: true,
    );

    return res.isSuccess;
  }
}
