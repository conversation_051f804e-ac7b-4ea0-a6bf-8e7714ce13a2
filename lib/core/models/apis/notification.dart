import 'package:wd/core/models/entities/notification_alert_entity.dart';
import 'package:wd/core/models/entities/notifications_response_entity.dart';
import 'package:wd/core/utils/http/https.dart';

class NotificationApi {
  static Future<bool> uploadDeviceToken(String token) async {
    ResponseModel response = await Http().request(
      method: HttpMethod.get,
      ApiConstants.uploadRemotePushDeviceToken,
      needSignIn: false,
      needShowToast: false,
      params: {"jgToken": token},
    );
    return response.isSuccess;
  }

  static Future<List<NotificationAlertEntity>> fetchAlertNotificationList() async {
    ResponseModel response = await Http().request<NotificationAlertListEntity>(
      ApiConstants.notificationAlert,
      needSignIn: false,
    );
    if (response.isSuccess && response.data != null) {
      return response.data.list;
    } else {
      return [];
    }
  }

  static Future<({List<NotificationsRecords> notifications, int total, String? errorString})> fetchAllNotificationList(
      int pageNo) async {
    ResponseModel res = await Http().request<NotificationsResponseEntity>(
      ApiConstants.noticeList,
      params: {"pageNo": pageNo, "pageSize": 20},
    );
    if (res.isSuccess && res.data != null) {
      List<NotificationsRecords> notifications = res.data.records;

      return (notifications: notifications, total: int.parse(res.data.total.toString()), errorString: null);
    } else {
      return (notifications: <NotificationsRecords>[], total: 0, errorString: res.msg);
    }
  }

  static Future<bool?> updateNotificationReadStatus(int notificationId) async {
    ResponseModel? res = await Http().request(
      ApiConstants.updateNoticeRead,
      params: {'id': notificationId},
      isFormUrlEncoded: true,
    );
    if (res.isSuccess) {
      return true;
    } else {
      return false;
    }
  }

  static Future<bool?> updateAllNotificationReadStatus() async {
    ResponseModel? res = await Http().request(
      ApiConstants.updateAllNoticeRead,
      isFormUrlEncoded: true,
    );
    if (res.isSuccess) {
      return true;
    } else {
      return false;
    }
  }

  static Future<int?> getUnreadCount() async {
    ResponseModel res = await Http().request<int>(
      ApiConstants.noticeUnreadCount,
    );
    if (res.isSuccess) {
      return res.data;
    } else {
      return null;
    }
  }
}
