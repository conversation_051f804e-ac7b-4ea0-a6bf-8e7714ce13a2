import 'package:easy_localization/easy_localization.dart';
import 'package:ntp/ntp.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:timezone/timezone.dart' as tz;

class TimeFetcher {
  static const _ntpServers = {
    'google': ['time.google.com', 'time1.google.com', 'time2.google.com', 'time3.google.com'],
    'aliyun': ['ntp.aliyun.com', 'ntp1.aliyun.com', 'ntp2.aliyun.com', 'ntp3.aliyun.com'],
  };

  static Future<DateTime> _tryNtpServer(String server, tz.Location beijingLocation) async {
    final ntpTime = await NTP.now(
      lookUpAddress: server,
      timeout: const Duration(seconds: 3),
    );
    final utcTime = tz.TZDateTime.from(ntpTime, tz.UTC);
    return tz.TZDateTime.from(utcTime, beijingLocation);
  }

  static Future<DateTime> fetchBeijingTime() async {
    try {
      tz.initializeTimeZones();
    } catch (_) {
      return DateTime.now().add(const Duration(hours: 8));
    }

    tz.Location? beijingLocation;
    try {
      beijingLocation = tz.getLocation('Asia/Shanghai');
    } catch (_) {
      return DateTime.now().add(const Duration(hours: 8));
    }

    try {
      final futures = await Future.wait([
        _tryNtpServer(_ntpServers['google']![0], beijingLocation)
            .timeout(const Duration(seconds: 3))
            .then((_) => 'google')
            .catchError((_) => 'failed_google'),
        _tryNtpServer(_ntpServers['aliyun']![0], beijingLocation)
            .timeout(const Duration(seconds: 3))
            .then((_) => 'aliyun')
            .catchError((_) => 'failed_aliyun'),
      ]);

      final servers = futures.contains('google') ? _ntpServers['google']! : _ntpServers['aliyun']!;

      for (final server in servers) {
        try {
          return await _tryNtpServer(server, beijingLocation);
        } catch (_) {
          continue;
        }
      }

      throw Exception('All NTP servers failed');
    } catch (_) {
      try {
        final localTime = DateTime.now();
        return tz.TZDateTime.from(localTime, beijingLocation);
      } catch (_) {
        return DateTime.now().add(const Duration(hours: 8));
      }
    }
  }

  static String getDayOfWeek(int dayId) {
    Map<int, String> dayOfWeekMap = {
      1: 'monday',
      2: 'tuesday',
      3: 'wednesday',
      4: 'thursday',
      5: 'friday',
      6: 'saturday',
      7: 'sunday',
    };

    // 通过 dayId 获取对应的 key，然后使用 tr() 方法进行翻译
    return dayOfWeekMap[dayId]?.tr() ?? '未知';
  }

  static int getDayId(DateTime dateTime) {
    return dateTime.weekday; // 1 = Monday, ..., 7 = Sunday
  }

  /// 获取本周日期的函数
  static List<DateTime> getWeekDates(DateTime currentDate) {
    // 获取今天是星期几（星期一是0，星期天是6）
    int weekday = currentDate.weekday;

    // 计算本周一的日期（weekday是1-7，星期一为1）
    DateTime monday = currentDate.subtract(Duration(days: weekday - 1));

    // 创建本周的日期列表（周一到周日）
    List<DateTime> weekDates = List.generate(7, (index) {
      return monday.add(Duration(days: index));
    });

    return weekDates;
  }

  // 日期格式化, 将日期转为 月.日，如12.31
  static String formatDateToMMdd(DateTime date, {String separator = '.'}) {
    return '${date.month}$separator${date.day}';
  }

  static String formatDateToDDMMYYYY(String date, {String separator = '/'}) {
    DateTime dateTime = DateTime.parse(date);
    String day = dateTime.day.toString().padLeft(2, '0');
    String month = dateTime.month.toString().padLeft(2, '0');
    String year = dateTime.year.toString();
    return '$day$separator$month$separator$year';
  }
}
