import 'package:wd/core/models/entities/chat_red_envelope.dart';
import 'package:wd/core/models/entities/chat_service_online_entity.dart';
import 'package:wd/core/models/entities/chat_type_entity.dart';
import 'package:wd/core/utils/http/https.dart';

class ChatApi {
  /// 检查聊天跳转类型
  static Future<ChatTypeEntity?> checkRouterType() async {
    ResponseModel? res = await Http().request<ChatTypeEntity>(ApiConstants.chatType, needSignIn: false);
    if (res.isSuccess) {
      return res.data;
    }
    return null;
  }

  /// 检查红包状态
  static Future<({String? errorStr, RedEnvelopeStatus status})> checkRedEnvelopeStatus(
      {required int redEnvelopeId}) async {
    ResponseModel? res = await Http().request(ApiConstants.redEnvelopeStatus,
        needShowToast: false, params: {'id': redEnvelopeId},
      isFormUrlEncoded: true,);
    if (res.isSuccess) {
      return (errorStr: null, status: RedEnvelopeStatus.fromCode(res.data["status"] ?? -999));
    } else if (res.code == "400") {
      return (errorStr: null, status: RedEnvelopeStatus.expired);
    }
    return (errorStr: res.msg, status: RedEnvelopeStatus.unknown);
  }

  /// 领红包
  static Future<double?> grabRedEnvelope({
    required int redEnvelopeId,
    String? seq,
  }) async {
    ResponseModel? res = await Http().request<double>(ApiConstants.redEnvelopeGet, params: {
      'id': redEnvelopeId,
      'seq': seq,
    },
      isFormUrlEncoded: true,);
    if (res.isSuccess) {
      return res.data;
    }
    return null;
  }

  /// 获取用户腾讯im的所有在线客服列表
  static Future<List<ChatServiceOnlineEntity>> fetchTencentAllOnlineList() async {
    final res = await Http().request<ChatServiceOnlineList>(
      ApiConstants.tcAllOnlineSupport,
      needShowToast: false,
    );
    if (res.isSuccess && res.data != null) {
      return res.data!.list;
    }
    return [];
  }

  /// 通知后端检查客服在线状态
  static Future<bool> notifyOnlineServiceClick(String userNo) async {
    final res = await Http().request<ChatServiceOnlineList>(
      ApiConstants.tcCheckServiceOnlineStatus,
      params: {"supportUserNo": userNo},
      needShowToast: false,
    );
    return res.isSuccess;
  }
}
