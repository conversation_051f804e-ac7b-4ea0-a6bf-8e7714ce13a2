import 'package:wd/core/models/entities/home_banner_entity.dart';
import 'package:wd/core/utils/http/https.dart';

class HomeApi {
  /// 获取首页轮播图列表
  static Future<List<HomeBannerEntity>> fetchHomeBannerList() async {
    final res = await Http().request<HomeBannerListEntity>(ApiConstants.homeBannerList, needSignIn: false);
    if (res.isSuccess && res.data != null) {
      return res.data!.list;
    }
    return [];
  }
}
