import 'dart:async';
import 'package:flutter/material.dart';


Future<bool> isAvifSupported() async {
  final completer = Completer<bool>();

  try {
    final image = Image.asset('assets/images/common/isAvifSupport.avif');

    final stream = image.image.resolve(const ImageConfiguration());

    stream.addListener(
      ImageStreamListener(
            (ImageInfo info, bool _) {
          if (!completer.isCompleted) {
            completer.complete(true);
          }
        },
        onError: (Object error, StackTrace? stackTrace) {
          if (!completer.isCompleted) {
            completer.complete(false);
          }
        },
      ),
    );
  } catch (e, stack) {
    if (!completer.isCompleted) {
      completer.complete(false);
    }
  }

  return completer.future;
}
