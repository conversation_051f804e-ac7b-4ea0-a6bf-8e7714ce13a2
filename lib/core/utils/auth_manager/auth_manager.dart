import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:wd/core/utils/log_util.dart';
import 'package:wd/core/utils/log_util.dart';
import 'package:wd/core/utils/log_util.dart';
import 'package:wd/core/utils/log_util.dart';
import 'package:wd/core/utils/log_util.dart';
import 'package:wd/core/utils/log_util.dart';
import 'package:wd/core/utils/log_util.dart';
import 'package:wd/core/utils/log_util.dart';
import 'package:wd/core/utils/log_util.dart';
import 'package:wd/core/utils/log_util.dart';
import 'package:wd/core/utils/log_util.dart';
import 'package:wd/core/utils/log_util.dart';
import 'package:wd/core/utils/log_util.dart';
import 'package:wd/core/utils/log_util.dart';
import 'package:wd/core/utils/log_util.dart';
import 'package:wd/core/utils/log_util.dart';
import 'package:wd/core/utils/log_util.dart';
import 'package:google_sign_in/google_sign_in.dart';

enum LoginProvider {
  google,
  facebook,
  apple,
}

class AuthManager {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final GoogleSignIn _googleSignIn = GoogleSignIn();

  /// 统一登录入口
  static Future<String?> signIn(LoginProvider provider) async {
    switch (provider) {
      case LoginProvider.google:
        return _signInWithGoogle();

      case LoginProvider.facebook:
      // 以后在这里写 Facebook 登录
        throw UnimplementedError("Facebook 登录未实现");

      case LoginProvider.apple:
      // 以后在这里写 Apple 登录
        throw UnimplementedError("Apple 登录未实现");

      default:
        throw Exception("不支持的登录方式");
    }
  }

  static Future<String?> _signInWithGoogle() async {
    try {
      final googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        throw Exception("用户取消了 Google 登录");
      }

      final googleAuth = await googleUser.authentication;

      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      final userCredential = await _auth.signInWithCredential(credential);

      return await userCredential.user?.getIdToken();
    } catch (e) {
      return null;
    }

  }
}
