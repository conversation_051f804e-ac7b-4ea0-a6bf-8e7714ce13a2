import 'dart:math';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:intl/intl.dart';

extension NumLikesExtension on num {
  /// 返回缩写后的字符串，比如：
  /// 1250 → 1.3K (en)
  /// 12500 → 1.3万 (zh)
  ///
  /// [locale]：
  ///   - 'en' → 英文缩写 K/M/B
  ///   - 'zh' → 中文缩写 万/亿
  String likesString({String locale = 'en'}) {
    if (locale == 'zh') {
      return _likesStringZh;
    } else {
      return _likesStringEn;
    }
  }

  /// 英文缩写逻辑
  /// 支持:
  ///   - B = Billion (10^9)
  ///   - M = Million (10^6)
  ///   - K = Thousand (10^3)
  String get _likesStringEn {
    final value = this;

    // 定义单位列表，越大的单位放在越前面
    final units = [
      {'value': 1e9, 'suffix': 'B'},
      {'value': 1e6, 'suffix': 'M'},
      {'value': 1e3, 'suffix': 'K'},
    ];

    for (var unit in units) {
      final num unitValue = unit['value'] as num;
      final String suffix = unit['suffix'] as String;

      if (value >= unitValue) {
        double result = value / unitValue;

        // 保留 1 位小数，四舍五入
        // 避免出现像 1.199999999 或 2.000000001 的精度问题
        result = (result * 10).round() / 10;

        // 如果结果是整数，就不显示小数
        return result == result.toInt()
            ? "${result.toInt()}$suffix"
            : "${result.toStringAsFixed(1)}$suffix";
      }
    }

    // 小于 1000，则直接返回数字
    return value.toString();
  }

  /// 中文缩写逻辑
  /// 支持：
  ///   - 亿 = 10^8
  ///   - 万 = 10^4
  String get _likesStringZh {
    final value = this;

    if (value >= 1e8) {
      double result = value / 1e8;
      // 保留 1 位小数，四舍五入
      result = (result * 10).round() / 10;
      return result == result.toInt()
          ? "${result.toInt()}亿"
          : "${result.toStringAsFixed(1)}亿";
    } else if (value >= 1e4) {
      double result = value / 1e4;
      result = (result * 10).round() / 10;
      return result == result.toInt()
          ? "${result.toInt()}万"
          : "${result.toStringAsFixed(1)}万";
    } else {
      return value.toString();
    }
  }
}


extension MoneyExtension on num {
  /// 返回格式化的金额字符串
  String get formattedMoney {
    final number = _removeInvalidZeros(this);
    return _formatWithThousandsSeparator(number);
  }

  String get removeZeros {
    return _removeInvalidZeros(this);
  }

  /// 删除多余的0
  String _removeInvalidZeros(num value) {
    // 保留两位小数，并移除末尾多余的0
    return value.toStringAsFixed(2).replaceAll(RegExp(r'([.]*0+)(?!.*\d)'), '');
  }

  /// 格式化为千分位
  String _formatWithThousandsSeparator(String value) {
    final numberFormat = NumberFormat('#,##0', 'en_US');

    numberFormat.minimumFractionDigits = 0;
    numberFormat.maximumFractionDigits = 2;

    return numberFormat.format(double.parse(value));
  }
}

class StringUtil {
  /// 随机生成字符串
  static String generateRandomString(int length) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    final random = Random();
    return List.generate(length, (index) => chars[random.nextInt(chars.length)]).join();
  }

  /// 检查字符串是否包含特殊字符或空格
  static bool containsSpecialCharsOrSpaces(String text) {
    return text.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]')) || text.contains(' ');
  }

  /// 验证用户输入是否为有效的 TRC20 地址
  static bool validateTRC20Address(String address) {
    RegExp trc20Regex = RegExp(r'^T[a-zA-Z0-9]{33}$');
    return trc20Regex.hasMatch(address) && address.length == 34;
  }

  /// 检查字符串是否包含数字
  static bool containsNumbers(String text) {
    return text.contains(RegExp(r'[0-9]'));
  }


  /// 根据宽度计算文字高度
  static double calculateTextHeight({
    required String text,
    required TextStyle style,
    required double maxWidth,
    int maxLines = 0,
  }) {
    final TextPainter textPainter = TextPainter(
      text: TextSpan(text: text, style: style),
      textDirection: ui.TextDirection.ltr,
      maxLines: maxLines,
      strutStyle: StrutStyle.fromTextStyle(
        style,
        forceStrutHeight: true,
      ),
    );

    textPainter.layout(
      minWidth: 0,
      maxWidth: maxWidth,
    );

    // 使用行度量计算总高度
    final List<ui.LineMetrics> lines = textPainter.computeLineMetrics();
    double totalHeight = 0;
    for (var line in lines) {
      totalHeight += line.height;
    }

    // 如果没有行（空文本），返回单行高度
    return totalHeight > 0 ? totalHeight : style.fontSize ?? 0;
  }

  static String formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = duration.inHours;
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));

    // 如果存在小时，格式为 hh:mm:ss；否则格式为 mm:ss
    if (hours > 0) {
      return '$hours:$minutes:$seconds';
    } else {
      return '$minutes:$seconds';
    }
  }

  /// 是否是空
  static isEmpty(dynamic res) {
    if (res == null) return true;

    if (res is String || res is Iterable || res is Map) {
      return res.isEmpty;
    }

    return false;
  }

  /// 补全url
  static String fixUrl(String url) {
    final uri = Uri.tryParse(url);
    if (uri == null) return 'https://$url'; // 解析失败也补全
    if (uri.hasScheme) return url;
    return 'https://$url';
  }

  /// 格式化数字字符串：
  /// - 100000000 以上显示为“x亿”
  /// - 10000 以上显示为“x万”
  /// - 去除无用的 .0 或 .00
  String formatLargeNumber(String text) {
    // 判断是否为纯数字（含小数）
    final isNumeric = RegExp(r'^\d+(\.\d+)?$').hasMatch(text);

    if (!isNumeric) return text;

    final number = double.parse(text);
    if (number >= 100000000) {
      final value = number / 100000000;
      return '${value.toStringAsFixed(2).replaceFirst(RegExp(r'\.?0+$'), '')}亿';
    } else if (number >= 10000) {
      final value = number / 10000;
      return '${value.toStringAsFixed(2).replaceFirst(RegExp(r'\.?0+$'), '')}万';
    } else {
      return number.toStringAsFixed(2).replaceFirst(RegExp(r'\.?0+$'), '');
    }
  }
}

extension DateTimeExtension on DateTime {
  /// Returns a formatted date string in yyyy-MM-dd format.
  String get formattedDate {
    final dateFormat = DateFormat('yyyy-MM-dd');
    return dateFormat.format(this);
  }
}

extension StringToDateTimeExtension on String {
  /// Converts a string in ISO 8601 format to a DateTime object.
  DateTime toDateTime() {
    return DateTime.parse(this);
  }
}

extension MobileNumberFormatter on String {
  String toMaskedFormat() {
    if (length < 6) {
      return this;
    }
    final start = substring(0, 3);
    final end = substring(length - 4);
    final maskedPart = '*' * (length - 7);
    return '$start$maskedPart$end';
  }
}

extension StringExtension on String {
  String formatCardNumber() {
    if (length < 4) {
      return this;
    }
    String firstFour = substring(0, 4);
    String lastFour = substring(length - 3);
    return '$firstFour **** **** **** $lastFour';
  }
}

extension HtmlStringExtension on String {
  bool get isHtmlString {
    final htmlReg = RegExp(
      r'<\s*([a-z][a-z0-9]*)\b[^>]*>|<\s*/\s*([a-z][a-z0-9]*)\b[^>]*>',
      caseSensitive: false,
    );
    return htmlReg.hasMatch(this);
  }
}
