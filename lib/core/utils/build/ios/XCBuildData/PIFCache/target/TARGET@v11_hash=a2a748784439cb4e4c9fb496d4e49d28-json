{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986b4f11d4911ec028c124a740ae0f0125", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/Mantle/Mantle-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Mantle/Mantle-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Mantle/Mantle.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Mantle", "PRODUCT_NAME": "Mantle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9897e63720c02be39b38fde98af03d2c99", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98081f93b07320faff31c6fc96f759a809", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/Mantle/Mantle-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Mantle/Mantle-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Mantle/Mantle.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Mantle", "PRODUCT_NAME": "Mantle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985d79cfdbead16f1e6690dee8018a1add", "name": "Debug-YL"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98081f93b07320faff31c6fc96f759a809", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/Mantle/Mantle-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Mantle/Mantle-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Mantle/Mantle.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Mantle", "PRODUCT_NAME": "Mantle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98495491ec8fab675b8efaa66e0400b9aa", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98081f93b07320faff31c6fc96f759a809", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/Mantle/Mantle-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Mantle/Mantle-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Mantle/Mantle.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Mantle", "PRODUCT_NAME": "Mantle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98091d1edb279d4059f08e81906df027d2", "name": "Release"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98081f93b07320faff31c6fc96f759a809", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/Mantle/Mantle-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Mantle/Mantle-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Mantle/Mantle.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Mantle", "PRODUCT_NAME": "Mantle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b976fae3faf43605f98c6bae602dcb64", "name": "Release-JS"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98081f93b07320faff31c6fc96f759a809", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/Mantle/Mantle-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Mantle/Mantle-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Mantle/Mantle.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Mantle", "PRODUCT_NAME": "Mantle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9813ac4dc50bd75bb28e4ccb09b8eb69d2", "name": "Release-JS2"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98081f93b07320faff31c6fc96f759a809", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/Mantle/Mantle-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/Mantle/Mantle-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/Mantle/Mantle.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Mantle", "PRODUCT_NAME": "Mantle", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f3ad5ca42b71c2fb1080f4591368af9e", "name": "Release-YL"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98abf85d44a7cbed3a5a77994ef6e1359f", "guid": "bfdfe7dc352907fc980b868725387e98b66f7a06edb46e75e6af7446d8e2816c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d56ee03905822867e8d43bccdf0e1241", "guid": "bfdfe7dc352907fc980b868725387e98bf3bda05ac4752eb3d088da70be94d98", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984199fb8e18dbba25ba999ad6dd32e47f", "guid": "bfdfe7dc352907fc980b868725387e982febb9c9b2e572fead2e619258dced38", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a579f5614547f3e7e1ffad5185ba9315", "guid": "bfdfe7dc352907fc980b868725387e98e95dce9129f7a446a1248f02a0f14577", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98277bf7072b02655fc55b25277c1bcd1b", "guid": "bfdfe7dc352907fc980b868725387e9841c1dba4926c4aa9b5e5fa2892ce5a66", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832f72f110b1ea484962d82b5e85c308b", "guid": "bfdfe7dc352907fc980b868725387e98d236e644e355160b153dd85f68ddd1a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980acd0ae87d8ecd300f0735eb3cdb1464", "guid": "bfdfe7dc352907fc980b868725387e987d70a673c74c1806d3a86717e6cd131d", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848b132d3b5586ba3e8eb7ce0bd3b9625", "guid": "bfdfe7dc352907fc980b868725387e983e8c3d7fea2b7b117d354db0db11072c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f88be659c094f6eb7d963b3f73c2213", "guid": "bfdfe7dc352907fc980b868725387e9848126fa0de370a54b0c0dedc570e71a0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852ac1d733610943cb2989279e07f40af", "guid": "bfdfe7dc352907fc980b868725387e98ad2853d717e137f937330dd4efab8681", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f03e8f988607d8aaace9b7c6a1f61e8a", "guid": "bfdfe7dc352907fc980b868725387e988e197dbf2cc445e381e0923a510f07b4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982139fd4fdecbe4cff53ab98a6c38169b", "guid": "bfdfe7dc352907fc980b868725387e981253b902420e51791e966e96bb03cd9c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98483b2fcf24a1ce75723dc7c3a82a047d", "guid": "bfdfe7dc352907fc980b868725387e98d561ee42bccab606259136b94051f307", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98346cade6fc65c6fd1b402c6006e7f591", "guid": "bfdfe7dc352907fc980b868725387e986383af94e945b071aec9c4a52af2740b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98300b78432f4b4c255d02bcce4c6808f0", "guid": "bfdfe7dc352907fc980b868725387e983b20073247ed612506449cbf973e9eb0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824da1ca648e9c0e13ad55cb954b65474", "guid": "bfdfe7dc352907fc980b868725387e987fd4c5f69f6482109bbd8d77cb110538", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8deadb350d9ce8851cbec6074bcf71f", "guid": "bfdfe7dc352907fc980b868725387e98083f0361b6cb8353a77223de605e65a2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6a336fe99c55145f4e52273a4b2aa4b", "guid": "bfdfe7dc352907fc980b868725387e987b36231d5dc39776592c054bdde8d907", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987eb346dac5995c0f12236ba4c75b7424", "guid": "bfdfe7dc352907fc980b868725387e98079216c8d9b03334d8f0b1f65e47d5d6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a042aacbfdf99f60e67b4e40c09f1dc", "guid": "bfdfe7dc352907fc980b868725387e98243c0358235ee4bda9e3ac1503bd4009", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986c994dc60319ee9b5235589fcc11814b", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983ffe99a85c44976447de942d0c419b07", "guid": "bfdfe7dc352907fc980b868725387e989ceed72330780e49d044c9c59f3de319"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989201e1c0c87b72b7220db646b92e6ebe", "guid": "bfdfe7dc352907fc980b868725387e98d08d8f1cd505e3e8c039a431430d10c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d34d495c5b5610609cd30e9c7a47ec7c", "guid": "bfdfe7dc352907fc980b868725387e980ad1a4b333c4ba819c525b37b5f6553a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc779b07d3115311c5a3a2093de6c0fc", "guid": "bfdfe7dc352907fc980b868725387e989f2485720316d844a8cfa06bbe5a084c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817555ad50f8a6109ed7bf56fe60e08ac", "guid": "bfdfe7dc352907fc980b868725387e9860181d34281c9811a90ddbefc44b2bc8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0bb00ef1fc2b819f78bb7269fb74d1b", "guid": "bfdfe7dc352907fc980b868725387e98a52b4637030e9025d53239c2f09033cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ae7972883bfb557e9477d4f83753d33", "guid": "bfdfe7dc352907fc980b868725387e9870a67587bb0491fd515df109821aa1ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986794ca1ba6cd340e7ca5529fe3f6fe9d", "guid": "bfdfe7dc352907fc980b868725387e9894c65cf0b34f66c95b411097b143c9b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0cdfa6582bbd48a26dc50402b5e672f", "guid": "bfdfe7dc352907fc980b868725387e98bc3fbb0f2a90fb4b87b34c380779f582"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de473e347199bd8e098a093ca711e5bc", "guid": "bfdfe7dc352907fc980b868725387e98db0be93af23c26eacb8fa70fa3027024"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9c07d0ed9cc0bf6a20bfd754bfb4b04", "guid": "bfdfe7dc352907fc980b868725387e98d8cba235515fbdfe435b3a48fed8092d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad9ac0d0f9b3a1b15bbba6c5017f6bf3", "guid": "bfdfe7dc352907fc980b868725387e98710bcb8b1a25b5804cd16945ae85cd4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a0cf5db464da4c1f9a4f85dd727d110", "guid": "bfdfe7dc352907fc980b868725387e9891ad4766b50db41f88ce99ef78e71642"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea954321776515278671368159e86f0b", "guid": "bfdfe7dc352907fc980b868725387e98b6ca6169fc922b833b622b58e92304fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c109cea1f17b6b10d3d1e680c6f56c09", "guid": "bfdfe7dc352907fc980b868725387e983befb9f945c7292846ab46e84c141e66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4f470a37e6387bf7773e90b00f4a112", "guid": "bfdfe7dc352907fc980b868725387e98b3bf0529cbe09e4ebeca2e2c947dbcbe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b715e3567111f0c899847d1880d279ca", "guid": "bfdfe7dc352907fc980b868725387e98bc779a423443dc9303bc00c32764ca04"}], "guid": "bfdfe7dc352907fc980b868725387e980a3ba6d24e16c37f4584f68aa6a5f915", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988338730910fc8b3bf8d9a66916e6c151", "guid": "bfdfe7dc352907fc980b868725387e98db21096a1b4ec4ea524f0cb1efbe21fe"}], "guid": "bfdfe7dc352907fc980b868725387e98cb8c93e2351591c7f775a24d0e23e0aa", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e987e0fb6144a05effda4ffc96ac1c2c447", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e982742fb9f3ddc293e43c65bab714408c6", "name": "Mantle", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986716a62ee19f61fc991dd3f4aa3e1163", "name": "Mantle.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-YL", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-JS", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-JS2", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-YL", "provisioningStyle": 1}], "type": "standard"}