{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98856945caa0087fbb282373f47cd13868", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980451eba243663cfa03efc5ef1eac7654", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98549c52efbc2ce79ce0cadc5a4b2ffe33", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988dfd7d6dd3f9130b7bf55f185fb6a1ca", "name": "Debug-YL"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98549c52efbc2ce79ce0cadc5a4b2ffe33", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d072926f6d9482feeeacbd9422b1429d", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98549c52efbc2ce79ce0cadc5a4b2ffe33", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d25a97fcb94f6362cc82cb3d3fb005da", "name": "Release"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98549c52efbc2ce79ce0cadc5a4b2ffe33", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b84f6a2aa9145f22d8d2c1be778c8e22", "name": "Release-JS"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98549c52efbc2ce79ce0cadc5a4b2ffe33", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987aa4745f1afc25f28e9666592f02d4cb", "name": "Release-JS2"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98549c52efbc2ce79ce0cadc5a4b2ffe33", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985abd0b4929da6429ff6f26184ef9005a", "name": "Release-YL"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9815d6f56ad2e51d83a70a84b1349e8a00", "guid": "bfdfe7dc352907fc980b868725387e98a8c8da4ab930673ae7a07bc29e7544d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6e9f234731c80c24e0e2a72a8119c87", "guid": "bfdfe7dc352907fc980b868725387e987aca89fcc86b29df2389ed7cf85824b1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855d0fe32f6f387cfdc0c43fff224677c", "guid": "bfdfe7dc352907fc980b868725387e98757dcaad9a092946394b02b20517bc82"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846fb204e0491ba4876417f09d3af1433", "guid": "bfdfe7dc352907fc980b868725387e98ac2223d1a5d220774d1b01d77faea4d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5273adc62f2098dbf0b329bfb7a99e3", "guid": "bfdfe7dc352907fc980b868725387e98701619bde0e7bf34a6bf8881775e607c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f04c44b2f472f2c269c1c0d8f6418e01", "guid": "bfdfe7dc352907fc980b868725387e98adf3ff3737690b6814ad8b9ab7972e0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981dd9385749b046986055caf5da2fe0b1", "guid": "bfdfe7dc352907fc980b868725387e98a0efdbfdaf6ae7198f44b8c95763edf5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a28e09280c09b33bc70b6a22af6dda1", "guid": "bfdfe7dc352907fc980b868725387e98379c21205b579ba54c676aac33bb7e2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98387d805518a496e16d1c8494e04ee423", "guid": "bfdfe7dc352907fc980b868725387e986332932847a76b2087e05f7d2f3e455d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b98bf2ed7d89d9852c2ba51f14f13752", "guid": "bfdfe7dc352907fc980b868725387e98b1b8e71ebba12e34c89aa3b27237248d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98114940bcd1624e7384c49b49edb2837c", "guid": "bfdfe7dc352907fc980b868725387e985b035d912042d5bbc611e6cdc75031a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd1a3bb7df1bc2ba149a1c36bc58c2f9", "guid": "bfdfe7dc352907fc980b868725387e98e730028e42fb7219935a661cd5f228aa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812cf835748de92e06b56a292f17be421", "guid": "bfdfe7dc352907fc980b868725387e9885efd2ea07f1ad466114f227182fd981"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f202cf1c0e38c92df937f473aa8863d", "guid": "bfdfe7dc352907fc980b868725387e981eaaa4d6dba859b2792cf840b998cca2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca8d87fa2499bc52d70cb05cc5f54a56", "guid": "bfdfe7dc352907fc980b868725387e98e40ad2e725c7dc751eeb17bc4a3a9d4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f6b172fc7b7950a3cf95d26277d3ded", "guid": "bfdfe7dc352907fc980b868725387e987c4a6d62f52bbe9c5b6de41ffc2d1a04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98750aaa9803c336a7f1bfc9c77e8d8e57", "guid": "bfdfe7dc352907fc980b868725387e98ead16a0523f50130e39e9cfdcf38987a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da790ad3838bce6b411de31893718a63", "guid": "bfdfe7dc352907fc980b868725387e98faba6ff396d8113596fd0f126d4c6573", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98650abf7fc6086f98fd90b728773c58f6", "guid": "bfdfe7dc352907fc980b868725387e9805bb761b3244e3ed8481e2f24029d2e4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800ec09e0819da39f1327c8748694e1cb", "guid": "bfdfe7dc352907fc980b868725387e98705fd9aa1c3f3b91c87849bda53aceca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be0a9b9d4af618c98c69d2094749f4ab", "guid": "bfdfe7dc352907fc980b868725387e9851b04332485414fb1cc3b2ff0f74000e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb66d09bed7ec4051d51a07cb9a1010d", "guid": "bfdfe7dc352907fc980b868725387e98974ba2b93156b807336a07fb1d8e73ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98744f8af06d93d316c8f46737fb850d76", "guid": "bfdfe7dc352907fc980b868725387e98d4b2558e4f592080b81639fed402386c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9856e5fbb5789a91835001f3cf5967e012", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9886389c86e3775c3a182fe1053a11a7da", "guid": "bfdfe7dc352907fc980b868725387e983326b7c085a45cc3d5122e79a7d5d683"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860a7d1e465e017651855b1dda1234299", "guid": "bfdfe7dc352907fc980b868725387e9842f792afc36d87c88998996084c1239c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5f2988c5bd60c56151e8dd10d2f01f5", "guid": "bfdfe7dc352907fc980b868725387e989bed176ae051f1474c6667071da42302"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898affc0e37c8ebe74ef0e14857aac1aa", "guid": "bfdfe7dc352907fc980b868725387e98ff584a3233fadf170a28079931889aed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6047f188f55c21e52d6ec802d1ca269", "guid": "bfdfe7dc352907fc980b868725387e98217c68c5d14a8d299b1788c0f2dd70ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a5f2b8f75d49276ffaeaa13e4dba989", "guid": "bfdfe7dc352907fc980b868725387e98491d1a556908ee33825eab68ab5ccafe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5923bf679813606c7f502a25692c2fb", "guid": "bfdfe7dc352907fc980b868725387e98c026996c7e3fa3ce6d4bb888c6f517ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a2889ea35ec958f0d7189f8d7bb9a1c", "guid": "bfdfe7dc352907fc980b868725387e983fe39349e471d7f74215a7aa59df6b35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fec939016d6c52403d5dc2d6f7503588", "guid": "bfdfe7dc352907fc980b868725387e98668fca8f4ddcffec8c0a1f1b27415311"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ee7106c2e7a78f6fbf6673edb3064ed", "guid": "bfdfe7dc352907fc980b868725387e98a8273b63cf8ada79e13b9c01679f9f60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f33899f1d3d45a2996967cf14a4a781", "guid": "bfdfe7dc352907fc980b868725387e98dd0e1371c6c94fb2840014bf53eb2fff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5b90faa17b7258485c750ede384ee53", "guid": "bfdfe7dc352907fc980b868725387e9813829fc56c4b584fb0b6dce37df864d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eafc04bbd3a79d19138965db7dd5dc1a", "guid": "bfdfe7dc352907fc980b868725387e984c95e55eff652a31a0a46a49339dee4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5d186047d0b79bd22e82f4070b09878", "guid": "bfdfe7dc352907fc980b868725387e98887a6af102159a3559b9f6bd11ca34e0"}], "guid": "bfdfe7dc352907fc980b868725387e98551ce6512d60d381c7695388eee0f9d5", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988338730910fc8b3bf8d9a66916e6c151", "guid": "bfdfe7dc352907fc980b868725387e983f8e6e8e64af1944d006fe3ce24928a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870148b0a96bb4f7c7fd953ab7ead94ea", "guid": "bfdfe7dc352907fc980b868725387e98c8a04163449fc70e7bee22a32322eeca"}], "guid": "bfdfe7dc352907fc980b868725387e98e948bd8031d741f2bf5054ef1dd152a0", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e983a6960c70e6b439108c323371889254c", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e9812e4cb3e5c4bfeac79f4c01ad4307526", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-YL", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-JS", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-JS2", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-YL", "provisioningStyle": 1}], "type": "standard"}