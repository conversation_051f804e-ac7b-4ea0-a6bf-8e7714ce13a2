{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fe04253c19491fe9e3f6cf32fc08c33d", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/tencent_cloud_chat_sdk/tencent_cloud_chat_sdk-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/tencent_cloud_chat_sdk/tencent_cloud_chat_sdk-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/tencent_cloud_chat_sdk/tencent_cloud_chat_sdk.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "tencent_cloud_chat_sdk", "PRODUCT_NAME": "tencent_cloud_chat_sdk", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ab0785fd24cc5deeb7ca2a618ab0106b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98941040cf18e752d2a995fd4ad7f5a99a", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/tencent_cloud_chat_sdk/tencent_cloud_chat_sdk-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/tencent_cloud_chat_sdk/tencent_cloud_chat_sdk-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/tencent_cloud_chat_sdk/tencent_cloud_chat_sdk.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "tencent_cloud_chat_sdk", "PRODUCT_NAME": "tencent_cloud_chat_sdk", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ab6ffd0079f0a78f72d1ad8c640de7e1", "name": "Debug-YL"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98941040cf18e752d2a995fd4ad7f5a99a", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/tencent_cloud_chat_sdk/tencent_cloud_chat_sdk-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/tencent_cloud_chat_sdk/tencent_cloud_chat_sdk-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/tencent_cloud_chat_sdk/tencent_cloud_chat_sdk.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "tencent_cloud_chat_sdk", "PRODUCT_NAME": "tencent_cloud_chat_sdk", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98faaa82821fe2c94f7131833b346c6761", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98941040cf18e752d2a995fd4ad7f5a99a", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/tencent_cloud_chat_sdk/tencent_cloud_chat_sdk-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/tencent_cloud_chat_sdk/tencent_cloud_chat_sdk-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/tencent_cloud_chat_sdk/tencent_cloud_chat_sdk.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "tencent_cloud_chat_sdk", "PRODUCT_NAME": "tencent_cloud_chat_sdk", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980152aff400100c346aab2a4ae76bd82e", "name": "Release"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98941040cf18e752d2a995fd4ad7f5a99a", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/tencent_cloud_chat_sdk/tencent_cloud_chat_sdk-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/tencent_cloud_chat_sdk/tencent_cloud_chat_sdk-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/tencent_cloud_chat_sdk/tencent_cloud_chat_sdk.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "tencent_cloud_chat_sdk", "PRODUCT_NAME": "tencent_cloud_chat_sdk", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985500d5194a7bdd53506e7c94dc3b878f", "name": "Release-JS"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98941040cf18e752d2a995fd4ad7f5a99a", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/tencent_cloud_chat_sdk/tencent_cloud_chat_sdk-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/tencent_cloud_chat_sdk/tencent_cloud_chat_sdk-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/tencent_cloud_chat_sdk/tencent_cloud_chat_sdk.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "tencent_cloud_chat_sdk", "PRODUCT_NAME": "tencent_cloud_chat_sdk", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988069512bf8f9067585d16b025d563f5f", "name": "Release-JS2"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98941040cf18e752d2a995fd4ad7f5a99a", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/tencent_cloud_chat_sdk/tencent_cloud_chat_sdk-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/tencent_cloud_chat_sdk/tencent_cloud_chat_sdk-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/tencent_cloud_chat_sdk/tencent_cloud_chat_sdk.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "tencent_cloud_chat_sdk", "PRODUCT_NAME": "tencent_cloud_chat_sdk", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986fc8d08f015ffe5709d93c343fee0194", "name": "Release-YL"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98dbf496e88ffae303f63be6bb91f2324a", "guid": "bfdfe7dc352907fc980b868725387e9854f37337c3f94b797431e7da9b29e686", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98c9eeb485171ef9a707639310212f8431", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981776e87e91d9175f75d0668d10ebd721", "guid": "bfdfe7dc352907fc980b868725387e98cfba3febd45ed45afd40f76ac0bb8126"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c52357eef67986f80270db291e94f644", "guid": "bfdfe7dc352907fc980b868725387e98df026778744f7f3ae27a5153e690e6c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ae8e6b039ed25969f1c75a1b6a5946a", "guid": "bfdfe7dc352907fc980b868725387e98463e3ebc4e5ac10a04fd0d8be68f55d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ee1dfde430592669bb7072bc42c43c8", "guid": "bfdfe7dc352907fc980b868725387e98dac1a6db20647b81dcc76c8be8651841"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98994006f1db967e30f256f5594c13e407", "guid": "bfdfe7dc352907fc980b868725387e9884aaa4f9b8e8cf3686dab06ed62dd109"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98248977c1084c8613f102fc5ff82f2bc7", "guid": "bfdfe7dc352907fc980b868725387e985afc6f0cc190de61234f58df718199ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98878b1a71c8a2dfd57828ed5acb91449c", "guid": "bfdfe7dc352907fc980b868725387e983d334baf97e88d7177c0cbcce224d9f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837619390cf6a5e335a991d75c86166a9", "guid": "bfdfe7dc352907fc980b868725387e98a0845a54328f331e7b09608b1dc6c3a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d684b8ab456774b9c13ed2d3e793b6b9", "guid": "bfdfe7dc352907fc980b868725387e98dad913a55cde3b8247322131268e8292"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf18655c6505c86e46d631af97340451", "guid": "bfdfe7dc352907fc980b868725387e98bace97cd2b49fbfebf84d96c2efe9c61"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e47d31bb6f4fe750e03adcb5ea591b23", "guid": "bfdfe7dc352907fc980b868725387e9843c118e1ed96c42492e2efbbd79404fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804d7bddb98c75faaf428b1d1b230b850", "guid": "bfdfe7dc352907fc980b868725387e981115d710a54e0658bd25220361636e58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f259314e04dfa18faea9deb0755c2da5", "guid": "bfdfe7dc352907fc980b868725387e989df22d3ebe642398903e864a3054d7cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b794fb70e8a81fc701dffba94871dd02", "guid": "bfdfe7dc352907fc980b868725387e98aae3512f78fa9102cbdd90ae1414fcb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bf115e1c6923b8466d40d89e631083f", "guid": "bfdfe7dc352907fc980b868725387e981ad5a3c2d3eb1344170d96ed980180f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894ec2e04b79a1f5033569871c22f0473", "guid": "bfdfe7dc352907fc980b868725387e987ca9eb2a21e78d3e7f8376dca924472e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986781a957def4577bb0f4ea053bc11571", "guid": "bfdfe7dc352907fc980b868725387e985ab9aa71209552cb647bc100d6078021"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b526e19a131378fad324139f0f077d27", "guid": "bfdfe7dc352907fc980b868725387e9872091643de517520281bbbe2fe4d5a96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980117b094bff3e69a868c4bab27c21b18", "guid": "bfdfe7dc352907fc980b868725387e98e7443af848b4a6cb52ba34300f66894b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b84a625bc74f43a383fd4c4db675291d", "guid": "bfdfe7dc352907fc980b868725387e988886149fd63c7a0861c40549da3df20a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a9ae1152f2d9135c78a9672d2f3b953", "guid": "bfdfe7dc352907fc980b868725387e98508c65a88514e8a19365838a6defff2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b8fa026a82c6169cf214e87b4e120bc", "guid": "bfdfe7dc352907fc980b868725387e98535b6dd90eb65d96569421bc73c485fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d32a82e10240fa05973636da9630a3ba", "guid": "bfdfe7dc352907fc980b868725387e98cccba4f2ae6e8e5bc841f8b3eb634afd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e4ced79563b597f96821bd3725dd8cd", "guid": "bfdfe7dc352907fc980b868725387e98343b8f2c7b5de27b89429afa8e47fd4a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa9674db7eaab7ba3f94a1809e871b18", "guid": "bfdfe7dc352907fc980b868725387e98bd2efad85abbc1af53bb335d16214939"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2241c8c81da1f43d3433c52a43d1c6b", "guid": "bfdfe7dc352907fc980b868725387e98f12ebda9673ac149e53c7efc9eb47d63"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865247af82f747fe864de0a5abd4ec64e", "guid": "bfdfe7dc352907fc980b868725387e98ead55a78cf7975122d69b69e46052000"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f25a57491b9cafd6ed198b7f54744fd", "guid": "bfdfe7dc352907fc980b868725387e98ea386ecd5e5c42843b90c111bdd31825"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986457935164ebfafbcc80522552f5ca0a", "guid": "bfdfe7dc352907fc980b868725387e981f8d74d739591057879194025cfd56a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98112d1f59616354d2340c0f5f5df9f741", "guid": "bfdfe7dc352907fc980b868725387e98425d3f82941d68a6c4e0ac701406114b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1a59f86c8ed2a0789c2a60757eb9530", "guid": "bfdfe7dc352907fc980b868725387e9888bc84a2b36985bcce4a9316c0430fe4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805d163f4550747cfcaf848d2488fcd82", "guid": "bfdfe7dc352907fc980b868725387e984fdca17bb189798c781ac89340a4e4c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816661bc01cca9a39382acca610589658", "guid": "bfdfe7dc352907fc980b868725387e98b2e20da2bcbbc6b2282692740aefd4a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e4beee0639cd70b2e075a3a086252e7", "guid": "bfdfe7dc352907fc980b868725387e989cb60945569b7974b2a6d6e2837a982b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987bd793cde502534b79be0a7d6d6535b0", "guid": "bfdfe7dc352907fc980b868725387e987a439cc255f01929bebba096052a06e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da5dcc2551014e04022562aecad42f55", "guid": "bfdfe7dc352907fc980b868725387e985701daf2d9b4e7bdb36db55db229b543"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2128b616fb1cbb7a4144aba85be568c", "guid": "bfdfe7dc352907fc980b868725387e9833943ba34b5600c96b4c2f3b37bce192"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a54c618b8095296abba1075b383dbd39", "guid": "bfdfe7dc352907fc980b868725387e9854ae18c06da7884bafbde92886765e8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873eac55ef96f90188a18e91f17e1ae4e", "guid": "bfdfe7dc352907fc980b868725387e98df1d219351a53868bca47f5548ccafcf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98678a3f495fc65ad30b69931b3c27b8ee", "guid": "bfdfe7dc352907fc980b868725387e98728df7c354f74bc563ad1b94aa585316"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2377cc95a2b40c3b1d7a13df04e0b2a", "guid": "bfdfe7dc352907fc980b868725387e98a6980dcc5330c4e4ef30e24047c867f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897f724d9b5214afe6a6c0f174e11fee8", "guid": "bfdfe7dc352907fc980b868725387e98b0bbae3fe654c4eefeffedb311af4d4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7fd6d68c89c111884b100060212dbce", "guid": "bfdfe7dc352907fc980b868725387e980ac92a80b1321b3dfbf3dd6cde84c67d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aca1dad44dcaec75496acffb446cb459", "guid": "bfdfe7dc352907fc980b868725387e98845f344e9f78390779ca4bac5a8e1e65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988be6414e4bb0a984223aea448c186b18", "guid": "bfdfe7dc352907fc980b868725387e98866729104fc3a7af8bab27d0b417a867"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867d4d3a0985fd9cdc44c28ef29694ea1", "guid": "bfdfe7dc352907fc980b868725387e982c649c3b185ecb8189bac31591ab0ac7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bea571fa39d942d91079aece20cc544", "guid": "bfdfe7dc352907fc980b868725387e989e196ffd70f02ff18b22779c8c87bcb8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4bb73cc58b28ee4edb76abec797819d", "guid": "bfdfe7dc352907fc980b868725387e98671c7d748b7ca291e07529fe4991b4f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882ec77a0ddfdb82b7e9ef243b550dd9f", "guid": "bfdfe7dc352907fc980b868725387e98c346d2b5530ed2d306111c6095216340"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831c4a445c90dbcbb93858c4a2f9f4d23", "guid": "bfdfe7dc352907fc980b868725387e9863c2febe17cf45911359a949b241bd00"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5122e79fc1d680f3e7758fd6e451a9d", "guid": "bfdfe7dc352907fc980b868725387e98d18a3470e86cfdd9ea6d7d4aa3e4096d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff006d868bcd20cd214ad32611767663", "guid": "bfdfe7dc352907fc980b868725387e98ddcd85f9fe1cfc0cbaa6ce37326b278a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ad8cb8e2f934db7ef8772ea6935c0e5", "guid": "bfdfe7dc352907fc980b868725387e9820a7fd38968fdfb7cbc3448474586ac9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cdf538b09849e3b31752b6f16b874d6", "guid": "bfdfe7dc352907fc980b868725387e98d488874f68515fbd4530d5de406da722"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859a0095e6388d0d30a58f8b7adf613eb", "guid": "bfdfe7dc352907fc980b868725387e985145815e4ac3811c2ba86d69b302186e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc77c23eb6d6e1b31634f400e50a6b6b", "guid": "bfdfe7dc352907fc980b868725387e983133447cef99b2ad1e6fa56cf8205175"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988651b2394ff1b10a0968c5c12fd2291b", "guid": "bfdfe7dc352907fc980b868725387e98bca1c2173234060f9d643dc9a96bed7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d253cf4a6b2ecbbe3cf8d6c3187558a", "guid": "bfdfe7dc352907fc980b868725387e98d4af0f0cc04118c83bdd3e05a924c44e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886bd3680f23aa3dd36f53d7e14fa1a2c", "guid": "bfdfe7dc352907fc980b868725387e98dfd732507d319cad918146b8b4d2dd1c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9204f1f8cfadcfb40752cad3d206dcd", "guid": "bfdfe7dc352907fc980b868725387e98ee863e1217f01190f18da130eef0251a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5644f78ce5acd2a673f07dc452b62e8", "guid": "bfdfe7dc352907fc980b868725387e98a9867886b1ca44aaf98aeebe090a58c2"}], "guid": "bfdfe7dc352907fc980b868725387e986db6a296ce6270ba0d63cbf9fe20bf3f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988338730910fc8b3bf8d9a66916e6c151", "guid": "bfdfe7dc352907fc980b868725387e98425e5ee4daa3a977de317fde40118e51"}], "guid": "bfdfe7dc352907fc980b868725387e9878671e8cb9a3188f1f4e163e5fca1e9a", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9847a943e12dad057c1dbd2d8f74bd22b1", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98df71c66ecc82a9796c50f296fb0a215b", "name": "HydraAsync"}, {"guid": "bfdfe7dc352907fc980b868725387e98f1e4294f9d6a6e169e02b24c5d04222a", "name": "TXIMSDK_Plus_iOS_XCFramework"}], "guid": "bfdfe7dc352907fc980b868725387e98ba204d184f78a46e2605c4a18658ab11", "name": "tencent_cloud_chat_sdk", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ec6577985ee917b8779818a66bcd1b13", "name": "tencent_cloud_chat_sdk.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-YL", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-JS", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-JS2", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-YL", "provisioningStyle": 1}], "type": "standard"}