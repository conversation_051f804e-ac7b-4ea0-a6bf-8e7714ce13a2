{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981f33641320a8ae6b15a3c838e2ce47cf", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98429a8cca4e787b4d0762abdb8a6535a3", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980208e249de22a35f42fcc9ca28bf6183", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bf73ae52f1f312e5a53a27b3af563878", "name": "Debug-YL"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980208e249de22a35f42fcc9ca28bf6183", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b9f108e813acd6408f919e7bde03166f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980208e249de22a35f42fcc9ca28bf6183", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9816cec11bdeada59c3546865329e158dd", "name": "Release"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980208e249de22a35f42fcc9ca28bf6183", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98970cc1e5b93d92945f39c06ae58f6f80", "name": "Release-JS"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980208e249de22a35f42fcc9ca28bf6183", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fa4db8b23a12092d750d08c5803a6ed1", "name": "Release-JS2"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980208e249de22a35f42fcc9ca28bf6183", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98988e9d13dd5161e03e639c3d2009f2a5", "name": "Release-YL"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9877a70a7780766b112e30388e8ddc29bd", "guid": "bfdfe7dc352907fc980b868725387e98ceffcec2f521e9374928d9eb07eba2bd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890dce810bc067d3d4b9c3aa9860d7b79", "guid": "bfdfe7dc352907fc980b868725387e9883aa35ab757f44301102fb43a1a45630", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de20e2793c1b35ba0164e0f172f7fed3", "guid": "bfdfe7dc352907fc980b868725387e98ab9ddf74d5e4e80dfb3736c68c37cf82", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841aa26e825d06f3e884822923fad37a6", "guid": "bfdfe7dc352907fc980b868725387e98624c88b25157f8dfd624e982d06b8fb2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98716dc7559749111e5e1da80eea43b16e", "guid": "bfdfe7dc352907fc980b868725387e982a917ff2fff7772eaa85d849b6a944c9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4b988cf57bf8fc1d1a6a6099941b49f", "guid": "bfdfe7dc352907fc980b868725387e989a19e3d6318841d24e41a510b12460a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899fc56c1b4f421530ce7627b2355c833", "guid": "bfdfe7dc352907fc980b868725387e98811b1b93b615f05f7628e7bc48c60d0b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3e5704e50eda3f22be492e3bbbd351b", "guid": "bfdfe7dc352907fc980b868725387e98fd6f9b3faf8251ad1740ef7f5f8bf34c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb6efc455fdbcd4fd3bac671726b13e1", "guid": "bfdfe7dc352907fc980b868725387e983fe9c2ac98ed706399b4bb8e109ed5c0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d776534c41cba7da3313ca95edfb4882", "guid": "bfdfe7dc352907fc980b868725387e98401cd8deaf2dc9af537f768ddfa9d47b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859a1ae4f47bdcb51e5a7f01790033354", "guid": "bfdfe7dc352907fc980b868725387e98eb40f986726835cde39cc74abc22051b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d7f611cb8c862c388caf7c00eac6696", "guid": "bfdfe7dc352907fc980b868725387e98da782df8e1be67f0bb88b4cfbb3f8240", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea2f9f00cea5d42f0373f8012650baaa", "guid": "bfdfe7dc352907fc980b868725387e985fbf32de6d8dbe9a647df7bc4693e386", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841c45bc6e2e50f5dd4b651be437a3ca4", "guid": "bfdfe7dc352907fc980b868725387e98d1b4ae233ae783918443fd6b8eabc4a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875a663b86ccb9dacb201c8019f373087", "guid": "bfdfe7dc352907fc980b868725387e987fa242890b346a662f56ee27fbffb112", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851e9f25ce003dfc98b8dc85d505a3609", "guid": "bfdfe7dc352907fc980b868725387e98b72a2b7c5c39a3b5569923b2b3714100", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e222a520d7c150fe99efd658b714b15", "guid": "bfdfe7dc352907fc980b868725387e98a1a0ec8eb3c9a3482e0393deb504d722", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a46be2efd964f05c6b886adaca74c2f2", "guid": "bfdfe7dc352907fc980b868725387e983aae01b35c364abe4e4c55d6799e6470", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f90adb02e026561b094e569260c89ade", "guid": "bfdfe7dc352907fc980b868725387e9857bb5fc6149ffeae3fec62371b109ef9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846dca87aa256abcd39936ef5ece9df60", "guid": "bfdfe7dc352907fc980b868725387e98269275080b9096ba963327e29ce7981b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98496b73a7eb2f8ff751c9cf6cae17f2c8", "guid": "bfdfe7dc352907fc980b868725387e98dae295dc5e8a434fcc81876d6c0ee588", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989361a5d2a93825b23fe5fa625de1454f", "guid": "bfdfe7dc352907fc980b868725387e982349cda0ead55afc7c158e1a43f6bdea", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de539042419a9cbf240e353802360e7b", "guid": "bfdfe7dc352907fc980b868725387e98a5f1074505c59c41111fa9c84b0892ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a43e0a5acd15cfc4a1e0d5a41e57bb7", "guid": "bfdfe7dc352907fc980b868725387e98513f78a6316ecf2d6547f14cef195b9b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842491d55b40cc949eefcb347b59d4172", "guid": "bfdfe7dc352907fc980b868725387e9824beec21505943dfefe909371f014ec0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4d146eb5f53ff2f2763032b85c97aee", "guid": "bfdfe7dc352907fc980b868725387e98a039b7e7b66ee809c66c18dea310fafc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e78f207a96701666028bcffdaf94492", "guid": "bfdfe7dc352907fc980b868725387e984e52deae8bfac7de935e1725022db60e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825bd65458c7c14d1f6933dbc2eab5f82", "guid": "bfdfe7dc352907fc980b868725387e98c2bbbc6311c37d70ff0c302a5b7633d6", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9848ab6b3b02cd03edd96cdfc0ad11743c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b7eb9c0270e7ed8d6cb39eb9a3e2d767", "guid": "bfdfe7dc352907fc980b868725387e98f131a00649f2742f4be25bd6d7ffb327"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98994c045735d06270e0b12c9ae26ab6b4", "guid": "bfdfe7dc352907fc980b868725387e9806e53e1e22e75119e4c1fd34859a028b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98378fcf725f75d6f8caf58bb5f99e7bb2", "guid": "bfdfe7dc352907fc980b868725387e98a36466dd80dd46cf7e4c17ec76c38c1c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98197017e84be29ddc026d3b4b57086fc2", "guid": "bfdfe7dc352907fc980b868725387e982be2c46ec590b9bf2ab08a0dc427f26e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98166047d0120081f4ebc1b2d4c2ddcb08", "guid": "bfdfe7dc352907fc980b868725387e98a4373fa2e649a84849f452dd9e50d038"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c8c62eab766bba98695f422611c5c00", "guid": "bfdfe7dc352907fc980b868725387e98195df3004cf3a897425843a0087aed8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f50d2da3ba45178e6aeb26b6a4c7414", "guid": "bfdfe7dc352907fc980b868725387e988e6c257ca3892ba72c91f92d1dba6f06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885c5b55e900835e7173ddb873b92de7d", "guid": "bfdfe7dc352907fc980b868725387e984f09bb35979532070d739903161477e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803d20315b63436e54c4ad4782f714820", "guid": "bfdfe7dc352907fc980b868725387e98ba7ccfa11839080e2f64c48e4771f2c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98253fb4bb2caea4378d9a37fd9a625aec", "guid": "bfdfe7dc352907fc980b868725387e980e85394b321c6074364fc42e6f241573"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98510b46756cada27e9fdffa2be15ec961", "guid": "bfdfe7dc352907fc980b868725387e98bf0b3d38e21af2b1ceb8419719f8fd68"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b4b6dc06dd2b7accd5bcb22c26d692f", "guid": "bfdfe7dc352907fc980b868725387e98ef97c2d44fbe62d3b779d8fd1271935e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983dbd97e6a2eb131fef3f4ba1908159e7", "guid": "bfdfe7dc352907fc980b868725387e98fcd95624517815c6a9dcaef2961b5632"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c195db48251085513cb1588ad255b7a", "guid": "bfdfe7dc352907fc980b868725387e980082e057e21387c16c73681d6c698688"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98820a236c2f207450c6c032316cb4707a", "guid": "bfdfe7dc352907fc980b868725387e9820c473fbcf1c178799256bd5daf7dcf7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98059f70ba53f08b9c87faa04921230422", "guid": "bfdfe7dc352907fc980b868725387e98be5ad195b070446d90c076ef0485fe50"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988884c4d8ab38f7168794da4708e3789a", "guid": "bfdfe7dc352907fc980b868725387e9828cc482a39d2a5721bb55d1e9b6e2647"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe8859870a9297315db49fa23bf58465", "guid": "bfdfe7dc352907fc980b868725387e9868aa459c9ade354ae347b0d1ba608f4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803c4cfcf5d9cb3befbfeee058e3c8853", "guid": "bfdfe7dc352907fc980b868725387e9827d129427fb0dc003967f5ae6fc159f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98667049578abb106842e691b65cf57417", "guid": "bfdfe7dc352907fc980b868725387e98f6c4862f40f16de521683864d1cf872e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af574b973fb77256b751b095e5cd2b72", "guid": "bfdfe7dc352907fc980b868725387e98e87a0e21f00e14a31dacc0aee663b1a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a8d742cda1c41d246d6b5425b6dd898", "guid": "bfdfe7dc352907fc980b868725387e98a29b485cd0a178f18845e49c2670dbe2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ddaf387e8da717947bfa297f61510bc", "guid": "bfdfe7dc352907fc980b868725387e98eb72e35169af50a907bfabf761393845"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98305018167b02c44b64ddbe78ddb038b8", "guid": "bfdfe7dc352907fc980b868725387e988296710ca2efd79c6a997bec01567adf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4eec6e4d168fa74c16af5e9e4e5eedf", "guid": "bfdfe7dc352907fc980b868725387e98031fa6d03737fd76ecbe6fc2fe33a8d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983dd2efb6d0179d10dba9ddb62de19834", "guid": "bfdfe7dc352907fc980b868725387e983f050e3b7e5725681f05a20089e6be88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848b11c7cb5f7a77e79308c670f0d7d6c", "guid": "bfdfe7dc352907fc980b868725387e98d4fbd7621c843cbedcdc284d3e345c51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb6ee252a92e4afced33ad164fa64e05", "guid": "bfdfe7dc352907fc980b868725387e98c62ec76aed692e8f7ea96646b6efd7ea"}], "guid": "bfdfe7dc352907fc980b868725387e989e43de25d986ce63975f3d1381f920b0", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988338730910fc8b3bf8d9a66916e6c151", "guid": "bfdfe7dc352907fc980b868725387e98e7800aa8e0215abe814a98f4c197f0ff"}], "guid": "bfdfe7dc352907fc980b868725387e9846df8766b1233deaeb5838f9ddc57d94", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98cdd30cdfce05d97af01a95d565e133df", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e982742fb9f3ddc293e43c65bab714408c6", "name": "Mantle"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9837027570fe3a09bb9c7d0d2620332306", "name": "SDWebImageWebPCoder"}], "guid": "bfdfe7dc352907fc980b868725387e982ec175b6b4d6149d1cce89f5f0b3694a", "name": "flutter_image_compress_common", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b928db338a2b0bf59a36f34e391aa676", "name": "flutter_image_compress_common.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-YL", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-JS", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-JS2", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-YL", "provisioningStyle": 1}], "type": "standard"}