{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ca0a5264e53a05d9222fa0718270b817", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e982d5221575c22479817347e65544f24af", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985a9ffbbd1ba48ce76a13fca031b49cd6", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98d23022d0ddc85883bd4534268dc94173", "name": "Debug-YL"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985a9ffbbd1ba48ce76a13fca031b49cd6", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9848c067105da0ae22a8072c9ecab973a9", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985a9ffbbd1ba48ce76a13fca031b49cd6", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98f743043e52f2a8b72998ba81eb24d451", "name": "Release"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985a9ffbbd1ba48ce76a13fca031b49cd6", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9898b4a4fedf219f11a4a126f14c288826", "name": "Release-JS"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985a9ffbbd1ba48ce76a13fca031b49cd6", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98aa792822ffb1d6947d0d1e22c41734f6", "name": "Release-JS2"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985a9ffbbd1ba48ce76a13fca031b49cd6", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e981489861bfa2f420a228e71f52250a71f", "name": "Release-YL"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e988a3048af7add727aecf95199dae655a9", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e985f103316f01892dc1df1e1109baa0961", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981027cb4cdde57c68012bf4842d97e34a", "guid": "bfdfe7dc352907fc980b868725387e98b11aa66281f17927de044b121f8203cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fdb676eeb05d3dc8e82433aafd2b326b", "guid": "bfdfe7dc352907fc980b868725387e9867187b789ea7e9e08b6f3e5afb460464"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e66a486143a461077a668b13c167a16", "guid": "bfdfe7dc352907fc980b868725387e98116092b9de88ed4004459ba7848c6f13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98732ceb3dbebecfde35a7b8173833f5cb", "guid": "bfdfe7dc352907fc980b868725387e98564a3daf78d49d8048a52a793c762f78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980566c5055111bd5008023a144df9a671", "guid": "bfdfe7dc352907fc980b868725387e982fe66c8bebbdd2a3ae6830bda2ba0da5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98254e3e2e1bf65b979fde2dc77ebce891", "guid": "bfdfe7dc352907fc980b868725387e98f93b4064ac33e0ee4482d41b02f63325"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a724324e20ffc03ad27322ea9b46118e", "guid": "bfdfe7dc352907fc980b868725387e98397ed413dfdd69cbbcb93468a6348189"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f5f7aa39e1e10505e00dcfd7f0774e4", "guid": "bfdfe7dc352907fc980b868725387e98477ec48df8b6ff8e0b2a02478c966cb2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efc011ce5bb0213cc4d3febfa4563642", "guid": "bfdfe7dc352907fc980b868725387e98d8bf4a3eafe46820541e21418b17ee89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b36fdde2ce60bac02ce038824029e189", "guid": "bfdfe7dc352907fc980b868725387e9826b2fc805dcdd22950c08e458904cd70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cfb3810d0a61f080dcf7d0032b8affe", "guid": "bfdfe7dc352907fc980b868725387e986728e4dfa7a82fe055497447f0dada64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fd9b22bf983c44a1ae67f460059d174", "guid": "bfdfe7dc352907fc980b868725387e98a36abd345776d2ea17fd4fcaee1f63d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98985bf40cfd4fcc52222e0e9f41ef6827", "guid": "bfdfe7dc352907fc980b868725387e981afe184980b30d0538b5985e627cea0e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8b41946b0afa5342a04e41822d4a564", "guid": "bfdfe7dc352907fc980b868725387e98434d1a5f3b9e18a97df3aedc0f43d0f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c96b6d98ccf360798a3686d07196df69", "guid": "bfdfe7dc352907fc980b868725387e98657672e60158edfb76550c09ee520161"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981eadded7ea2dc41275533c71c2d6c254", "guid": "bfdfe7dc352907fc980b868725387e98842732ce027c96d8378db883eba30b67"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d09fddaf68dd266a3173448b9cfb22d", "guid": "bfdfe7dc352907fc980b868725387e98405c6eb9d53a21bdeab96d17eed04e7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808a5a0702c021df72612b0cf95d7ef44", "guid": "bfdfe7dc352907fc980b868725387e98b2371d4332792b7e0f3f0b33164c8e4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dba431a79e505f879de8deea6ece739e", "guid": "bfdfe7dc352907fc980b868725387e98e8bb97da2d8db07c4b3ed26b12ad7217"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1f08b0a73bad26a507fceeee6c4d652", "guid": "bfdfe7dc352907fc980b868725387e98cc964fab47e3de34e47096bdfb31ad47"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd6ef4a042873931b4218bb099c082cf", "guid": "bfdfe7dc352907fc980b868725387e9825e8b1f884f07e35cffb2c9460c44efd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7e72a6a80c9f0ce3b0689d5ea0edcd9", "guid": "bfdfe7dc352907fc980b868725387e98e53447ec6e4b247747b55c95c88ab686"}], "guid": "bfdfe7dc352907fc980b868725387e9868e527c27158f0b265b914128e49f3af", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ab5e1f747dfe477b655528b07584898d", "name": "DKImagePickerController.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-YL", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-JS", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-JS2", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-YL", "provisioningStyle": 0}], "type": "standard"}