{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d686f985b13d0f7a1f42d27627448c56", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/libwebp/libwebp-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/libwebp/libwebp-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/libwebp/libwebp.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "libwebp", "PRODUCT_NAME": "libwebp", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981149f73e6e8f4026fa580989b58ead57", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98aa7cfeb23541065b2222b4e04b50b738", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/libwebp/libwebp-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/libwebp/libwebp-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/libwebp/libwebp.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "libwebp", "PRODUCT_NAME": "libwebp", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cf17588c39ac3cf6a1001cf12d4efbbd", "name": "Debug-YL"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98aa7cfeb23541065b2222b4e04b50b738", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/libwebp/libwebp-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/libwebp/libwebp-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/libwebp/libwebp.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "libwebp", "PRODUCT_NAME": "libwebp", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c98fa236714545c330f0f23df67f0f63", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98aa7cfeb23541065b2222b4e04b50b738", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/libwebp/libwebp-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/libwebp/libwebp-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/libwebp/libwebp.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "libwebp", "PRODUCT_NAME": "libwebp", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c2a2c5ed91b83e8ecf76f1539f3c897f", "name": "Release"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98aa7cfeb23541065b2222b4e04b50b738", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/libwebp/libwebp-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/libwebp/libwebp-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/libwebp/libwebp.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "libwebp", "PRODUCT_NAME": "libwebp", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9865b40dcafc09a9a3bec283c7b8c27af2", "name": "Release-JS"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98aa7cfeb23541065b2222b4e04b50b738", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/libwebp/libwebp-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/libwebp/libwebp-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/libwebp/libwebp.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "libwebp", "PRODUCT_NAME": "libwebp", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9816c3ebd9c470ab0d88b5b6c7d6dc7465", "name": "Release-JS2"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98aa7cfeb23541065b2222b4e04b50b738", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/libwebp/libwebp-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/libwebp/libwebp-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/libwebp/libwebp.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "libwebp", "PRODUCT_NAME": "libwebp", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981956f93c3734ba7fa0cf947a58c8714e", "name": "Release-YL"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ac9b28e6554c03ee5a279a246659f8d5", "guid": "bfdfe7dc352907fc980b868725387e98e8599d35587c6fde4f69f6248ccc7f9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c1738f6436fc268f776ef3b0f56b2ac", "guid": "bfdfe7dc352907fc980b868725387e982f461501f31de7f2f391f6dccb33987d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f10232528a83fb8788e679bcbdf53ece", "guid": "bfdfe7dc352907fc980b868725387e98f86cd4384b56d0e9b8fa86f3a801e4bd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec85c51333ce57075952dd13c1bccc7b", "guid": "bfdfe7dc352907fc980b868725387e989156c00891f6247d3bdc7e558245af52"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988066265095199f5b5001ba691e90cf55", "guid": "bfdfe7dc352907fc980b868725387e984230fe9b93512edebdea2809aadaf27d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984db4b4519414f678cae4a62ea992aab7", "guid": "bfdfe7dc352907fc980b868725387e98f869f01678dd7560ea791a21877b4ad9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c67e06b49728fee5cacd60790946a94", "guid": "bfdfe7dc352907fc980b868725387e9895da53c934151f24ed34db51254565fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad26e752e7703e6c71ef5c6e6105e837", "guid": "bfdfe7dc352907fc980b868725387e9852c522e537aca2fb6c2ee66e0a60da0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98822cf12af917f819f4a6184a03fa1f17", "guid": "bfdfe7dc352907fc980b868725387e98c7960b7f2eb60b24a45ea10bbc6795f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcaf1a9265b3996f9368dcd5a1ed9d9d", "guid": "bfdfe7dc352907fc980b868725387e986dec355c86b7ba98cd10198eb1a131a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ca46646b76df3f26e86a9bbbb5e3d32", "guid": "bfdfe7dc352907fc980b868725387e980d116fb169a3eed1efc73404bf468976"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b2ad9cbb679b792bdcd445fa96968da", "guid": "bfdfe7dc352907fc980b868725387e985b96c3135f2cbf57976803c3166e22c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882f1eeeef0028f5d966111f02f8f4d06", "guid": "bfdfe7dc352907fc980b868725387e98251f79c9e577bb917103d7737f875300", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f33569b6b88cd5bfb0d2abc1cac8c7ba", "guid": "bfdfe7dc352907fc980b868725387e98d95b32e30baf99118016cbf7e3ff49df", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c78658a0b6ca85f31dbb972631ff63ac", "guid": "bfdfe7dc352907fc980b868725387e9863711d18aa36345d26316a1dd6752033"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba35baad91fddf1975d708dac7bd16de", "guid": "bfdfe7dc352907fc980b868725387e9829994b6b213b5a94e945768fef5b59cb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0172b12928c3caab9d1d8acd506b2b8", "guid": "bfdfe7dc352907fc980b868725387e9847eb1f09fd549d125d9f275e3c27e981"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e33ee507b922d7ee4b915b09e9bc8f51", "guid": "bfdfe7dc352907fc980b868725387e98983b80970319699325e78c93c631e232"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ecb6cb4f79457b87ad77ce6e5c9daa7", "guid": "bfdfe7dc352907fc980b868725387e987d62dd2131070c33af4d30b2a551da9e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba4aa01c38562e6fd43fefa3c11dd494", "guid": "bfdfe7dc352907fc980b868725387e984f19c464480ddf69bea3153c1c40344a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab6a60d80dd15f6feb7e2351ee14b795", "guid": "bfdfe7dc352907fc980b868725387e98468916386ff68695f8f41d645de53247"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834e37afec9e26a5e751777a6f4a4f3be", "guid": "bfdfe7dc352907fc980b868725387e9819780a90765cb4be52ffc8c0aa43246c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c6f3ebcd0246a0f268c3c1621482741", "guid": "bfdfe7dc352907fc980b868725387e98435ef0b8522d82797df26edabc1d41f4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98196211d3c6f32963be30d875097893c7", "guid": "bfdfe7dc352907fc980b868725387e980ed9258560345c2e4346e9fcaca7e77b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abfee760d5b80126bab2afd3879969ab", "guid": "bfdfe7dc352907fc980b868725387e98c483e5af36ee13961c00eb27699bf873"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d1b92fab8bd4d76b7060e45477943d8", "guid": "bfdfe7dc352907fc980b868725387e983eaa5af6e3a06345a145499eca0ee600"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e410a0e1b423d4974bcd846c6cf0c8f0", "guid": "bfdfe7dc352907fc980b868725387e98b1c8b936e82443e63eeaa9c5a81ac4e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897a7e8c56ae9819a9949fbc53a19779d", "guid": "bfdfe7dc352907fc980b868725387e9877241490bfec3ee24e7dbc48260c30b6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ca2f4276ad66eca6a5dbec18b17297a", "guid": "bfdfe7dc352907fc980b868725387e9890df41ac40f73cdc7c8abcc3d65ac0cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856d36bf79719fe51522a2d2da42e4fea", "guid": "bfdfe7dc352907fc980b868725387e9812a6d7749523dd7ede7e02d30a3cfc7e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2afc7e78fd6ad7e80d1c2de77b37360", "guid": "bfdfe7dc352907fc980b868725387e989ccba5eb006353d2d9d468d986f8e7e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843bf30a0c2bd01bc390da7b5f9ab5a22", "guid": "bfdfe7dc352907fc980b868725387e98a38bf7db9f2ccabd0a7722be08ba1444"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e618f43d33b92dd50e45d0f23fd87958", "guid": "bfdfe7dc352907fc980b868725387e98befa5735fcacc3cd05a888ee934bad07"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e2152debd9dd5f7d68e0dd7d8604a11", "guid": "bfdfe7dc352907fc980b868725387e9836a241b182d68e7671546f0b7b93301f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847eea769f1226213efda849ef6738566", "guid": "bfdfe7dc352907fc980b868725387e986383262aaa18a7f9ca84687ce27c8f53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838a522ce7ca4c660a8076890f29f3f58", "guid": "bfdfe7dc352907fc980b868725387e98f7b4303a792bfcf262145f7789d15fd0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a512219216a56ec17541bfd06486987c", "guid": "bfdfe7dc352907fc980b868725387e985b0f14deee5ac7a055e1220fbf4499fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98058cd167bfd57b51aa57741f6ccabfd6", "guid": "bfdfe7dc352907fc980b868725387e98d97728b3f1236086a56bb14be5c0f887", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f2fe73815fc75a675862393de5feb2e", "guid": "bfdfe7dc352907fc980b868725387e980830107027d7bea7c53a0aad565ad185"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea17e31ec15c21f1091ccf488349499e", "guid": "bfdfe7dc352907fc980b868725387e981b0629a1264878175910c4b3d2d67617"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876199f7ee3d8d5872acd52517b17103c", "guid": "bfdfe7dc352907fc980b868725387e98450c5ac8b8fdc35ede3828a183c9d492"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98500e1bca559bf36f658728b31dc38431", "guid": "bfdfe7dc352907fc980b868725387e9882f84d21a1e23c0227db2ae6f592dc68"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bef6d2d0bd84ea1284978a0c80e170b", "guid": "bfdfe7dc352907fc980b868725387e982241908d8b9f20a9aa163dfc7cb33dc7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4648e355a1da72074fcf68a78a9801f", "guid": "bfdfe7dc352907fc980b868725387e9857809803faf3d465b3c21c42c53a7853", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d09681bc72ecc38c2bafc59658cb37d", "guid": "bfdfe7dc352907fc980b868725387e981c1b6f3ff28d79d41a025040797116df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c330b6b540d6798b7e929c7cdb2b457b", "guid": "bfdfe7dc352907fc980b868725387e9803ea69e8569f071c73753498207e3f48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896a16ddfad34b06d60b739c518dc7b1f", "guid": "bfdfe7dc352907fc980b868725387e98acab19f41155a6b158b49fcd27311f97"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad68e216f7262c3bb1d67f46f502f033", "guid": "bfdfe7dc352907fc980b868725387e985806f853726c4875b54160b83a752187"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ede376d8d76170cb416a67d1ba952cf8", "guid": "bfdfe7dc352907fc980b868725387e98213ef83e746623933a9306270903a8f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983de987f1e1adf22dbd367fe921c55718", "guid": "bfdfe7dc352907fc980b868725387e982ee89ab4a7684f9803ffca19275ddff0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c2447c7f59a2469a49603c79c2cb3f6", "guid": "bfdfe7dc352907fc980b868725387e982b98c2793b6ce95b0e644a5bcac2d601"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d85d8885880eb829ba0fefb69f623e25", "guid": "bfdfe7dc352907fc980b868725387e989006b0aabde639698936bb8278112b18"}], "guid": "bfdfe7dc352907fc980b868725387e985e53925700ca991337e8d29783245081", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98b8352b517ad3c86332de79556cb6c55a", "guid": "bfdfe7dc352907fc980b868725387e9885f48d97bff748939162fa3f93e2cbf8"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9844a1f970036a82718d50aea9804c187e", "guid": "bfdfe7dc352907fc980b868725387e981771a6e9315d94a80a7d6fc159b6418a"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9856773d69ab8feded3290933fc9149d13", "guid": "bfdfe7dc352907fc980b868725387e983177e89aa989078a44c19cefc37c900d"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e989eed0892c8c3dbadc987b43855dc1307", "guid": "bfdfe7dc352907fc980b868725387e988dc2273b1d9dde6cbeb92d2e37f9a498"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e983cd70c359a509f33662906df70358976", "guid": "bfdfe7dc352907fc980b868725387e980ff86539495be87497849ff96e7b0c62"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98e99948c4dd451e6776cf5f53b8b2bf12", "guid": "bfdfe7dc352907fc980b868725387e98d522314fef951ce809bbfd20dce7cce1"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98f28309e1cecc3da9c89cc33d14e699b5", "guid": "bfdfe7dc352907fc980b868725387e989b824c18919da24b9d495ae02722850a"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98690943efffea4ade5e77c0261912b68d", "guid": "bfdfe7dc352907fc980b868725387e9829f6256dd53f2ea2730314469283156b"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e984519368db068cd64da1db10eb982031e", "guid": "bfdfe7dc352907fc980b868725387e98083044498451f26e313d79bd408a657c"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98d1f7c5fdfded9f1a9a8b20e6be562da3", "guid": "bfdfe7dc352907fc980b868725387e9833fa275ccdddfdf538f4d5930bb5f4d4"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e983b53e3a17ab438d011921a554d2abdac", "guid": "bfdfe7dc352907fc980b868725387e98268df6057deb2fa0617fdf2274d32d63"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98f56c026ae0a3dcf6d313e40ae61d5ee4", "guid": "bfdfe7dc352907fc980b868725387e9857fccbd908ddd708caed5f10beae71e9"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98291da070bab36f517e2310b3a6b6681a", "guid": "bfdfe7dc352907fc980b868725387e98752fca4e98a4f498ee1ece69007ebe9b"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e988ffd80e73e5fcfa5171e2f710d89aa23", "guid": "bfdfe7dc352907fc980b868725387e980f8fbb0a61fdd1a235bf9274ff971037"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98e872680d2f9f439d8be757dccbd84bac", "guid": "bfdfe7dc352907fc980b868725387e98bdc6a84ee1e2be8356e83f90837b057a"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98ea34f1597acc80faf70d56b06ca9c7e8", "guid": "bfdfe7dc352907fc980b868725387e98156818bef26d82cdb18b56dda5799b8d"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98a6dfc0b52339e146f2646c254e021e2e", "guid": "bfdfe7dc352907fc980b868725387e985da04ef87f377afbb177f13a126dface"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9825b0bb19778dcd0fe329bbb02c22ac94", "guid": "bfdfe7dc352907fc980b868725387e98d1ab96b0cdc1760d458d3e421df13f93"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9856e53c7b68f120973ac48bc63c6675c5", "guid": "bfdfe7dc352907fc980b868725387e98cbbd6395e8dad339278ce283e074d62c"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98d2e18044ebc2e328b52329f969dda7cb", "guid": "bfdfe7dc352907fc980b868725387e9841e9accbc1daa912708c14dec79b7a5f"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9848294fad653ac3d1a51321cbf4374a0a", "guid": "bfdfe7dc352907fc980b868725387e98dddf993991acc806f81d45df02d4e749"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98643515956577d075faaf86370076a3c9", "guid": "bfdfe7dc352907fc980b868725387e983add2e1a589b1a7e9818cc8d64464740"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98c4a9e4e06fc75d8f4862e6e6f71e74e4", "guid": "bfdfe7dc352907fc980b868725387e98ea141b1876c4b74161c1cecff739c027"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98929ac50781e0543fd7e9c395eb6dca1b", "guid": "bfdfe7dc352907fc980b868725387e9824aabf35acb9eeeecea76cdc1c99fbf3"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98a22d18fbf32661f3188f0f77f7eb1aa1", "guid": "bfdfe7dc352907fc980b868725387e98b7765ae928beef1b80b81771453daf1a"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98dc933a48c84ecc4accefbd83d387ee01", "guid": "bfdfe7dc352907fc980b868725387e985b7f73e033787ec0906d42a3b4ac4b1e"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e983c248f36fa760e55d619f1861cd836e3", "guid": "bfdfe7dc352907fc980b868725387e98eea6139dc5b0798b403ef09751b73623"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9853f4a344b10607b751cb834679acd1f5", "guid": "bfdfe7dc352907fc980b868725387e9877bd50d626f843891cef51d1ad3ddcfd"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e986d9f7562bed2d0006ea833cc3f4bdb3b", "guid": "bfdfe7dc352907fc980b868725387e98985e110f2d24b327f19588aff58278fc"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98acf28df6be6fbbd0a453baea1af44a7c", "guid": "bfdfe7dc352907fc980b868725387e98bf3aeab0cd743ed4de370e5f6a703003"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9854f0087c8624d8b854665b72ce9ce718", "guid": "bfdfe7dc352907fc980b868725387e98da52364064fe85f91f57056e46a0a784"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98322daf1b1e7f9f5f99fc96ddac11581f", "guid": "bfdfe7dc352907fc980b868725387e98034f5af49fb1a764ac36f95511698f4d"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98a10268419132c798cbcf0c057ed15fd1", "guid": "bfdfe7dc352907fc980b868725387e987a35f72593b65b60bb79f46f902a5ff3"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9876c318c626164e4204f954c6453356e1", "guid": "bfdfe7dc352907fc980b868725387e986af2f4e37063266935d3cdd6ab4552b4"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98fb7ac52d77d38ba0dfcb2dba192959fd", "guid": "bfdfe7dc352907fc980b868725387e98fef0d8b3ae006e66f8dea803083d27fc"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e983141ef3c00b430a34653c5ea5ee96b39", "guid": "bfdfe7dc352907fc980b868725387e98d13a6711363b4cce23e87eab2c6096b7"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98880633b1ac0bcdf0acc89c898fe0dbf7", "guid": "bfdfe7dc352907fc980b868725387e98a23419eeb0f5420bdd7b2536042d2f41"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9877b854e8c130258ba5f13dbbef4fb614", "guid": "bfdfe7dc352907fc980b868725387e98ce6807d5dc744e405adbf61902df2195"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98206ab5c50eff7cbb31856fe26f00118d", "guid": "bfdfe7dc352907fc980b868725387e98061ae34b1b74790fc8e9db6052ca3ea1"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98cdcee6f0fc5491e2700e0e6408fc5619", "guid": "bfdfe7dc352907fc980b868725387e9870d0148b3b38664014eac6db6585e732"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e988b0086010e0362ff879ed13858f85e64", "guid": "bfdfe7dc352907fc980b868725387e989100dc85d4f39e244931de8f07c03165"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e986c5b2494dfd476a9d0750162d3b4f4b8", "guid": "bfdfe7dc352907fc980b868725387e9829d9b90e0e39d5d055031eab909d2b55"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98143c52fb6b89b0bbfecf856285147ea7", "guid": "bfdfe7dc352907fc980b868725387e980704b97f573831067ce0585e1506d6e8"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98124628253dd71342556d4a02c263bd9b", "guid": "bfdfe7dc352907fc980b868725387e98edc7cb91b2bd127dd93215035934c407"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98d36d1696f55e91c05ffac269572d00b4", "guid": "bfdfe7dc352907fc980b868725387e98c723fceff49af3cf9a11fe6aa1b93bc7"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9811ab1cb20c72b8df3daf297ed88a02df", "guid": "bfdfe7dc352907fc980b868725387e9809f538bc369eb315366868f9c050a91d"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98a394ab7ef09f7b93f2ce59b3c801ae6c", "guid": "bfdfe7dc352907fc980b868725387e98035e8505342e909c4a4cfc7db78a55e1"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e980853889c6e67bd46d68b2cb001b1db09", "guid": "bfdfe7dc352907fc980b868725387e983ec6dcc0e51b84ba09fd249c2a24d157"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9828514e38e745aef2cdb19aed7b3c28b1", "guid": "bfdfe7dc352907fc980b868725387e984ccc6eaa58ab872b867a6e4976f816af"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98c92a67a006f95dee7eb24fdf6dc0283b", "guid": "bfdfe7dc352907fc980b868725387e98838d4c8baaed0a607d9603c5df8e68ae"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98e5907c95f26ce7aeca4cb5e5c05330a7", "guid": "bfdfe7dc352907fc980b868725387e9808e62f4d57c14d0206755532e9a9947a"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e989a308c37a82e2f0b023a482bb5159a82", "guid": "bfdfe7dc352907fc980b868725387e9845bc4a502f96f1a9b7e96ee1b4debd74"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98756e66278349f789acf904531488761a", "guid": "bfdfe7dc352907fc980b868725387e98c40f1b32d237ec8fd8795f5efe5fcfec"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98762d2b3c6bc56eaa222ca473380df1ec", "guid": "bfdfe7dc352907fc980b868725387e98827656771f9160a3c66a9add0800fb5a"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9860c9afef1a378750a72db75c923b94b5", "guid": "bfdfe7dc352907fc980b868725387e980d06ccb7e8339da5afab976552fe82ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da0fac1892cd2e3ebac4d395be4a2c60", "guid": "bfdfe7dc352907fc980b868725387e98d8ab44fb04fc5601978e655f272d8a1b"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e989d81f80a62686d1e2d235c199fb7d52d", "guid": "bfdfe7dc352907fc980b868725387e987940811b73488d8b0305eacabf49712e"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98e26cb6cc856250cf3673aec432a21ee3", "guid": "bfdfe7dc352907fc980b868725387e9893b05745cf5eb88e98b0821ed1d0adbf"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98b741a9fe8fd2a8b11bcec16abb7bd70c", "guid": "bfdfe7dc352907fc980b868725387e98d8dc421f7e1302de2b680fc79fee5a75"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e980502fceb482452c8098936f10e1a04c5", "guid": "bfdfe7dc352907fc980b868725387e98d04d590ed7f9cdd6320b5c81cfad7218"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e981fe9644c5afe905e4c4edcbecf903e47", "guid": "bfdfe7dc352907fc980b868725387e980a9000018b2b93490f53ce122d68afa5"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e982abbd3836d279c2cf618316a1da7ac3a", "guid": "bfdfe7dc352907fc980b868725387e9811987151830b8464aa7b5e196d6a9b59"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e983670669e18920ecdda775bb42f431ae7", "guid": "bfdfe7dc352907fc980b868725387e982aa528bee718d056d6a1245456881701"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e980e1f603943c7fe06e5c310c8a6d04d7d", "guid": "bfdfe7dc352907fc980b868725387e982d20a45289c7a66c9fd5e97d783c6404"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98a2849761ea1aa05d80a5426a9c7a7738", "guid": "bfdfe7dc352907fc980b868725387e98e5dd4116e7d3ff45f7670a60a8bb273b"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e987c8d11946dde4cc477aa4b99281fdbd0", "guid": "bfdfe7dc352907fc980b868725387e98fa9296ffaeec075f7323a7a8ed54e338"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98067314cf17096fd194cfd61e5b91162b", "guid": "bfdfe7dc352907fc980b868725387e985fef4dc085e036c005959d80c32bc331"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98a300d09f9a9d8f46751c723d346725fc", "guid": "bfdfe7dc352907fc980b868725387e982ded9f0d81effe9af3d4f14de6e7e6d5"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98a8500f4eb00e24a70b95c864188a7e2c", "guid": "bfdfe7dc352907fc980b868725387e984eaf56b6e5c02443ed195a8b2982b96d"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e983d4393e8a63abaefeb62637309661023", "guid": "bfdfe7dc352907fc980b868725387e985c3160acacb063aea0dd0821d1048baf"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98c9c0584675f35dc81c23dc2701972e4d", "guid": "bfdfe7dc352907fc980b868725387e98ef192a0750f56fe0af3a8df2ea338e15"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98d2bc6e8e43e2e22ba167741349e05530", "guid": "bfdfe7dc352907fc980b868725387e98477cdc920a14f1573eeafe2a50236a27"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98dda43dd9bc7567e5806785d8bf7f5846", "guid": "bfdfe7dc352907fc980b868725387e98d37788d53b1d7ee4f8622230e43b51b3"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98b2350f4a661f93753d1b2dc0f093f08b", "guid": "bfdfe7dc352907fc980b868725387e9895895579eff9acbc16f47625549ddee4"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98164ca7427fc5aeab9bfd43fc4f63cffa", "guid": "bfdfe7dc352907fc980b868725387e986f49e8efee4ba2da74e2e20d844ecaa5"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e985d096e6005bf2984b6294221c40e10c5", "guid": "bfdfe7dc352907fc980b868725387e985901d06c25fc541353cb48d3a82350f9"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e988181e84bbcc66478d50aec20ae279f34", "guid": "bfdfe7dc352907fc980b868725387e98e3375861655ef089616ae0b1de9304ed"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e980b4e9c8dbb78ee2337981afc7818ecd7", "guid": "bfdfe7dc352907fc980b868725387e9802a3640638b2f70322eaaff38b271fc3"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e989ab591d9b8721fa5c07638d033d07666", "guid": "bfdfe7dc352907fc980b868725387e988ebabe45b2783de681095d5ba26527cc"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98b089cb08e5cb27ee12675edf754bd398", "guid": "bfdfe7dc352907fc980b868725387e98d92bc08c5affc906f60e9fb51d980e8d"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98892653f37545faf2df89ef1d5e35c41b", "guid": "bfdfe7dc352907fc980b868725387e9832a3bda3ed8bcc61d4e85a50db6491f0"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98c15f79bab9d3824efbf87af850330d85", "guid": "bfdfe7dc352907fc980b868725387e98a1dbefe0cab2c6c2d8d2cfbcea7d6c24"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98ea1e72fc39cd12f6925d395fcf326936", "guid": "bfdfe7dc352907fc980b868725387e98dfe77a7f0956994ed36e1d756ebb485b"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9873c2bb1d43e4a3ec6ada22a3e8ab1fb4", "guid": "bfdfe7dc352907fc980b868725387e9864cc0f69eb9e4c1896928fc99a77dfe3"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9844d4ed54beacd2ad6ef1040e7bf0e6ee", "guid": "bfdfe7dc352907fc980b868725387e98c4121bba154f6b4bac09f696f5fabdf4"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98932f7d4efb98fd1db20f71bf73855066", "guid": "bfdfe7dc352907fc980b868725387e9857c34ae5e13cce71ac09ffd4d2060264"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9841299935bd165320af9bea01532fa809", "guid": "bfdfe7dc352907fc980b868725387e98c623c36439d8c04bebe25bbbe2af5d58"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98481225c5dc5c0811df36031f8ad10e30", "guid": "bfdfe7dc352907fc980b868725387e98ac3d7f8d738882b1c3f90485e60937b4"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98487b1ee94eb5c9bc3917ade7b9af7177", "guid": "bfdfe7dc352907fc980b868725387e983f734c76911d5f46e00c4268084237eb"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9866b73cb0a036f2da69f4fe57fbaece6f", "guid": "bfdfe7dc352907fc980b868725387e98e7f444d6ff52da570e56de5aa6f625c6"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e987071e196bdfeed6fa63f4d691c1673e7", "guid": "bfdfe7dc352907fc980b868725387e98b1268d9a91e31b8fea85aa5fe688c3c2"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98e2a49b4caad58c58f6577dc740a0d4ca", "guid": "bfdfe7dc352907fc980b868725387e98c7dd5d1dd6437ceed67f00f4e53af7a7"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98b19bdb55aee1fd59b33462f399f0824f", "guid": "bfdfe7dc352907fc980b868725387e98d1568976acaf960a10c494f2836b8a38"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98ad91751f36d920ad1bf21f300b590b03", "guid": "bfdfe7dc352907fc980b868725387e9830730de1ed345e1e0a955076b0d5c1c9"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98bcdba81d8f546a35b2f5ae357619d96b", "guid": "bfdfe7dc352907fc980b868725387e98ee8381cc060b848da5fff8567bbdb747"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98f0abcf5f7f9b0af27f664d115a656524", "guid": "bfdfe7dc352907fc980b868725387e980115906263fa8705a669acfd51e3fd6c"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98509f99a4545581d7fae5c826395ec8eb", "guid": "bfdfe7dc352907fc980b868725387e986604c131fe6c6b335ceda42af41153f8"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9888011a777df6067879d500a352df360c", "guid": "bfdfe7dc352907fc980b868725387e98385b4aec028668e9d378314d0e113e58"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e988faba44220e5904c0f36be5df2ff0b2d", "guid": "bfdfe7dc352907fc980b868725387e98aa4fa928b4e5752daaa9d13f0d09fadd"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98e91ee43d820c6fb4911648a9153321e2", "guid": "bfdfe7dc352907fc980b868725387e98a9b300ad8b98939f1613ffdff70c29e7"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e989b3c27ca33c1a4a1a6dd745851055a14", "guid": "bfdfe7dc352907fc980b868725387e98abc52cdaf10c639a0e51c739032f4081"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98ded48a90ebecb69fd97d0fdd57dd637d", "guid": "bfdfe7dc352907fc980b868725387e98803c44099c194dc7494b03dc61cf1495"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e980dfc93cbbc67fcc00b50436f8f412f57", "guid": "bfdfe7dc352907fc980b868725387e98a33d15bd9c19e4d59592c678e3c31db7"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9842515731660af9af0994f2d3d581e243", "guid": "bfdfe7dc352907fc980b868725387e98154aa66c6a8fdb6b32b020ebf181c7f4"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e989e089e4859741f8308cb3f44f028a129", "guid": "bfdfe7dc352907fc980b868725387e982b902d95d4504d27e74ea18e21a2e5d4"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e980fa0dccdc0fe1c8be2850d299c60ec6e", "guid": "bfdfe7dc352907fc980b868725387e98b2a866d78b631da1e73767ec0aeffbd3"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e982e7c4e27d0ffda2dce078746946e87b9", "guid": "bfdfe7dc352907fc980b868725387e989423d0aec49166ae5e463b0d54aa2c20"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98f33e3ffaa38f6cd86765f56f90416a60", "guid": "bfdfe7dc352907fc980b868725387e98c8f4c2531d014c898007bcfe52a1fcf0"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98adaa9e6a18f0eda8a26ea049b513585e", "guid": "bfdfe7dc352907fc980b868725387e98df669a18d775de0f58e78dd3f9317f35"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98e1c543cb101927cfcf4d4e5d3401541b", "guid": "bfdfe7dc352907fc980b868725387e9832d87ec5b8a7b7097d3ef96eb0d98e55"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98be705cf25f14e1162565492bd00543d8", "guid": "bfdfe7dc352907fc980b868725387e98b985c191b59e5b96926032bdab0d0adf"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98a021ee1aadb231c34df2cc73792145ba", "guid": "bfdfe7dc352907fc980b868725387e984d84923fa7992574b6d91ae1e5394aab"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98e21995651cd0ab1f9550337943ec0525", "guid": "bfdfe7dc352907fc980b868725387e98b28e5992af5565b96d616054cac3cae6"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98803bbc499990c2a434c0fd5e9e1ae13c", "guid": "bfdfe7dc352907fc980b868725387e982d9af0b3a9d6b361033b2d58b47a3828"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e982a228fb0286bca71f345ff8f9ee3adbc", "guid": "bfdfe7dc352907fc980b868725387e98268aff6eef9bfba984d687a1ce578253"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e980d50a2fe1517761a7be0fb2085577c32", "guid": "bfdfe7dc352907fc980b868725387e9879eb00013310c388ca3df71cd89268a9"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98f77f46fe0cfcf4ed0fc2fef09c4cbad5", "guid": "bfdfe7dc352907fc980b868725387e9882b792ff3343486e3a45dec5b60b9aff"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e988052286950a81770788fac329f5c5a2f", "guid": "bfdfe7dc352907fc980b868725387e9816dbe22cbb6bf719c9bb18955cbea03f"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e983121403fe5095310d47b11ae6cbd70d7", "guid": "bfdfe7dc352907fc980b868725387e98d221300cf03e59a7d0bb0781193de895"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98ceea8562f5dc3074dee69e86b0eedad5", "guid": "bfdfe7dc352907fc980b868725387e98d37f88d494e95b3d2c20750172187fe1"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98b926fe5212a60f36e8f16aa66e408a33", "guid": "bfdfe7dc352907fc980b868725387e989fcee8464e21f8edefa646ff88469d40"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e982b1f14c0707c3c2cad45a03d71ce985d", "guid": "bfdfe7dc352907fc980b868725387e9886d06e7091114b1dc4efc7e89d6ae604"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98188db2b928683203e2e3e87b9908128b", "guid": "bfdfe7dc352907fc980b868725387e983d9f6988d8df11ac2b3ea53f30718452"}, {"additionalCompilerOptions": "-D_THREAD_SAFE -fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98ab949ff57be17c4ca4a9ab58f2c8f5c5", "guid": "bfdfe7dc352907fc980b868725387e98ebe2fadb021e2dda5278ce49f54ed69b"}], "guid": "bfdfe7dc352907fc980b868725387e98bd27f0035266f94c736c38500d6fd912", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988338730910fc8b3bf8d9a66916e6c151", "guid": "bfdfe7dc352907fc980b868725387e98c4c33effdd882d0824ff762dff8bd011"}], "guid": "bfdfe7dc352907fc980b868725387e981e52117603d84f49e42944f1fd3bf5d3", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98c588904660fdd81b98f0f7da43e9b745", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98459ecbbef4dbbe8a07a0c87bad0e0d1b", "name": "libwebp", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e982988ca927fde17a51bcb980424f8e9e7", "name": "libwebp.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-YL", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-JS", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-JS2", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-YL", "provisioningStyle": 1}], "type": "standard"}