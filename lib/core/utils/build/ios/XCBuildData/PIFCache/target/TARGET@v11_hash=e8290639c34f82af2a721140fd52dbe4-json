{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f3e3e15899aac1d58eaa6b75d4d86f74", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/HydraAsync/HydraAsync-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/HydraAsync/HydraAsync-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/HydraAsync/HydraAsync.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Hydra", "PRODUCT_NAME": "Hydra", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c4e00aa115c1050c0af756a67e143ef4", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9879e7771d7939ac32f7ddde7935f2f637", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/HydraAsync/HydraAsync-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/HydraAsync/HydraAsync-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/HydraAsync/HydraAsync.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Hydra", "PRODUCT_NAME": "Hydra", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984fe6d4b49854d4bf573013724acfde67", "name": "Debug-YL"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9879e7771d7939ac32f7ddde7935f2f637", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/HydraAsync/HydraAsync-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/HydraAsync/HydraAsync-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/HydraAsync/HydraAsync.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Hydra", "PRODUCT_NAME": "Hydra", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985e0e317d1da6f6265e3272861d340cb5", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9879e7771d7939ac32f7ddde7935f2f637", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/HydraAsync/HydraAsync-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/HydraAsync/HydraAsync-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/HydraAsync/HydraAsync.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Hydra", "PRODUCT_NAME": "Hydra", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989d2963b7b830ed749f56887c9b21a302", "name": "Release"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9879e7771d7939ac32f7ddde7935f2f637", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/HydraAsync/HydraAsync-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/HydraAsync/HydraAsync-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/HydraAsync/HydraAsync.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Hydra", "PRODUCT_NAME": "Hydra", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9809021db07772e6d71ba58f756a9993fe", "name": "Release-JS"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9879e7771d7939ac32f7ddde7935f2f637", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/HydraAsync/HydraAsync-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/HydraAsync/HydraAsync-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/HydraAsync/HydraAsync.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Hydra", "PRODUCT_NAME": "Hydra", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989dabf53c051d0c2247b064a1568bd59a", "name": "Release-JS2"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9879e7771d7939ac32f7ddde7935f2f637", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/HydraAsync/HydraAsync-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/HydraAsync/HydraAsync-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/HydraAsync/HydraAsync.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "Hydra", "PRODUCT_NAME": "Hydra", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988de50c44f3c72611cc046ce427c67d52", "name": "Release-YL"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984ed1590b66e6dc8f1e1ed21e54da7def", "guid": "bfdfe7dc352907fc980b868725387e98ef0d3543e1711f3ae943447c43b5f284", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9815ba5e98d8c1cd75d2cb05354a4a1532", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c237f0b4ca0beb633c628d573abcd741", "guid": "bfdfe7dc352907fc980b868725387e98a58c2393d0e2c70a9cfe0dfae9888057"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f66b0b16303b464b405e6b0ed0cb5c50", "guid": "bfdfe7dc352907fc980b868725387e98f3dba6d669657d1c146c28060487181b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98627217c2518946401739e9d051252138", "guid": "bfdfe7dc352907fc980b868725387e98db35a366efe8f6aac604f7eafd76619c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b122d3dd192054e5c24f8ccda02d2cd", "guid": "bfdfe7dc352907fc980b868725387e98d72bd85f96267fa68b2a32ed6e46b471"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98829a6488464ad1d8c2ddc68c9c1d8124", "guid": "bfdfe7dc352907fc980b868725387e98f4e365ccae26db653357abd04882564b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980721ef6b05ea0c79d2f26eb66411fde9", "guid": "bfdfe7dc352907fc980b868725387e988ebc6c94e80e38f15f8d430db5a4553e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893f55baf3360e49257ae49f4e8b5d9ce", "guid": "bfdfe7dc352907fc980b868725387e987691e45145faca0963724000403ede8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff819075d9103e91c3bf6597b95fa194", "guid": "bfdfe7dc352907fc980b868725387e9867ea8de5f932af828f3b88e296eeb3c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98174695afc7b2afdb47bf6e439e50c4ba", "guid": "bfdfe7dc352907fc980b868725387e98bcf4f38ddd6912c579416ec5c55afc4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f99f98477e706b7f12fa55656083a66", "guid": "bfdfe7dc352907fc980b868725387e98180007483e162ead246f1951ed0a19b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb7dccbbbc7de2d13145c1cb3bd08135", "guid": "bfdfe7dc352907fc980b868725387e98f07cb39a9ac5c2e47a019a31da4887af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884b78952a633bc759af29ab4a4296a93", "guid": "bfdfe7dc352907fc980b868725387e98bbb3bfbeb67049970166199e3cf8e3e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cb0b57e2c281673d9e3e0d54adca066", "guid": "bfdfe7dc352907fc980b868725387e9879cb4af0d442c742e3e62fce3ffce7c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d79cabe87db2abdd7719e45fd3b4ee4", "guid": "bfdfe7dc352907fc980b868725387e98ea170a2d992705243daf111a10a54726"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98036a69a89422e33568f041b61a760973", "guid": "bfdfe7dc352907fc980b868725387e98275d372811e39b6fcc06851bcd696e3f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980328d43b130db2cd407d760478174000", "guid": "bfdfe7dc352907fc980b868725387e983f6dab391f050042dd6386e836cab386"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fee234df1bbc0ed28bd869d77b9e690c", "guid": "bfdfe7dc352907fc980b868725387e98aa6617914b330472d4c5630fae98aa63"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c62da840deb7ae20573ca13ca37e957e", "guid": "bfdfe7dc352907fc980b868725387e989745194a4037b779908b4bddf7ade70a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856f2114b5693b72d33ca51f83f6bb78c", "guid": "bfdfe7dc352907fc980b868725387e98209b46207b6a4d61cd882a0ae7c703d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c5385761ccfb1aa9daecac5995543f2", "guid": "bfdfe7dc352907fc980b868725387e98f9260d651c4609d109bd8598d9b184d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0c8295f46feb705cbcad4a2cc8afa92", "guid": "bfdfe7dc352907fc980b868725387e98d4cb2e905ed67ec587ffb08a82e90bee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853ff4ff28aca5d077575ebe51d0eabde", "guid": "bfdfe7dc352907fc980b868725387e989c2dcede778c043ab63570835327541c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862a827ac803dae5b5df613a921c9b776", "guid": "bfdfe7dc352907fc980b868725387e98136b7ef935b4fe69cce724814b3d42e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872a9d5156819901bb3d9609c011409b0", "guid": "bfdfe7dc352907fc980b868725387e98d0c1e35503a7fb2ba793c38a2cdb4700"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c88f7e15287de85fb8fc6e459f8f937", "guid": "bfdfe7dc352907fc980b868725387e983eed0e94b166a758888d99751986b910"}], "guid": "bfdfe7dc352907fc980b868725387e9819acf4209d3e3fb7dda3f2016161f08e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988338730910fc8b3bf8d9a66916e6c151", "guid": "bfdfe7dc352907fc980b868725387e98ff5845cf5d6c64c93e43095429b2958b"}], "guid": "bfdfe7dc352907fc980b868725387e98da2a7efa34e662eb2c1ec82d12c668c6", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e985ff932d4a39d9ef0bf51f370117cfb4a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98df71c66ecc82a9796c50f296fb0a215b", "name": "HydraAsync", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9874406eff01050d7992f58c088679c282", "name": "Hydra.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-YL", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-JS", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-JS2", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-YL", "provisioningStyle": 1}], "type": "standard"}