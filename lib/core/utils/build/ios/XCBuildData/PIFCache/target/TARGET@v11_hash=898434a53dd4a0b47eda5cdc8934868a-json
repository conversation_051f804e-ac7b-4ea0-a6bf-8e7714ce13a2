{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9874ac9a2d917107790c6bbcd5a71dcaf1", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleDataTransport/GoogleDataTransport-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleDataTransport/GoogleDataTransport.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleDataTransport", "PRODUCT_NAME": "GoogleDataTransport", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b4853d7bf2b35310e99cd88708721c05", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98434808e690b520facf6809ac37644220", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleDataTransport/GoogleDataTransport-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleDataTransport/GoogleDataTransport.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleDataTransport", "PRODUCT_NAME": "GoogleDataTransport", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98da70b9a216f7a08b56aea19851eda901", "name": "Debug-YL"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98434808e690b520facf6809ac37644220", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleDataTransport/GoogleDataTransport-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleDataTransport/GoogleDataTransport.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleDataTransport", "PRODUCT_NAME": "GoogleDataTransport", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9837ab4bd8b1cab0505258d9fda657beb2", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98434808e690b520facf6809ac37644220", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleDataTransport/GoogleDataTransport-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleDataTransport/GoogleDataTransport.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleDataTransport", "PRODUCT_NAME": "GoogleDataTransport", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984b861b64cddbe08b2b430ee85f32a28d", "name": "Release"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98434808e690b520facf6809ac37644220", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleDataTransport/GoogleDataTransport-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleDataTransport/GoogleDataTransport.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleDataTransport", "PRODUCT_NAME": "GoogleDataTransport", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a3bc989429e0f6d4ee84014a803f46a0", "name": "Release-JS"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98434808e690b520facf6809ac37644220", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleDataTransport/GoogleDataTransport-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleDataTransport/GoogleDataTransport.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleDataTransport", "PRODUCT_NAME": "GoogleDataTransport", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9869b641eafcf50ab9edfbc8fadda9271d", "name": "Release-JS2"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98434808e690b520facf6809ac37644220", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleDataTransport/GoogleDataTransport-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleDataTransport/GoogleDataTransport.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleDataTransport", "PRODUCT_NAME": "GoogleDataTransport", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983a40cda0546fd4940fe701b62939fcbd", "name": "Release-YL"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986938c79607627f65bec6c58433d36eb1", "guid": "bfdfe7dc352907fc980b868725387e98258c5d4b3d4e8f298edaec495f17ee67"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e8db71e556fab7ca5a04c7e097296fd", "guid": "bfdfe7dc352907fc980b868725387e98b9dbf1097dc3d3e1fdf62fcd396e983a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c3e6cebfb1c61090d25046b37b994ca", "guid": "bfdfe7dc352907fc980b868725387e98fc54c0c091ca4be38a02be2be410a668"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989edda2ad1dcfce3ed6eae3002f4a6d2a", "guid": "bfdfe7dc352907fc980b868725387e98acb64e3cf35f6155afcf20e6eed02713"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e684a61abe94695643c446cb926805bf", "guid": "bfdfe7dc352907fc980b868725387e986f47e537e57e1706eeeed9b2691a1704"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc70a8877dcb878f83d21c4462169b5a", "guid": "bfdfe7dc352907fc980b868725387e983ce463f1764005981635b9b423fdd090"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a80f66d29626d0fcb90136423bbbe22", "guid": "bfdfe7dc352907fc980b868725387e983b1c7c04ea77b1033e4027d0a6e4ea8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f699ca0da3c2516dfc5a1f77fc7b98a0", "guid": "bfdfe7dc352907fc980b868725387e98ca7153b54877dacb90db8b37e88f02b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98488e19617f998d8766ecd35e17cbfb19", "guid": "bfdfe7dc352907fc980b868725387e98e5dadb6a3055577034dc60504ca50a9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988da335ed8bad877455de90a76ed1cb37", "guid": "bfdfe7dc352907fc980b868725387e981a931844841a3f4ee8239f45c9ef3fd3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858122fcd563335332e479a86b8e7fa37", "guid": "bfdfe7dc352907fc980b868725387e982bfbbf30d14132635b4e3fd394234e76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dec9582c337b8b2117143c967b778946", "guid": "bfdfe7dc352907fc980b868725387e98deee9223454b784a0e032672162a13df", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98629c0d1610cc973c894b8787c00c9239", "guid": "bfdfe7dc352907fc980b868725387e98f81abc30b0f5e692c8e3b1fd845cb9b3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98922af1b7f1f6fb401364f0485b2c50db", "guid": "bfdfe7dc352907fc980b868725387e98a6792bb9dda4b751eb6af46582b30b11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c91cdb0f3dc73f29b96c98391ecdc36e", "guid": "bfdfe7dc352907fc980b868725387e98fdf3016cbeab49404687a86bf0046557", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6620da02815f63c1ee0ad121762c142", "guid": "bfdfe7dc352907fc980b868725387e98f70fe4b8a2daaa916e83594c39a9084b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e883dbaeca1eed2eb8a4ed7aee0c24ee", "guid": "bfdfe7dc352907fc980b868725387e98ee31721b0a837005330a539eb1e7586b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7b5456465898372fcae0a8a47354804", "guid": "bfdfe7dc352907fc980b868725387e98314db9d7a54a3844ae2b6c07916b2fdd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98260d228ef0a11a285859de841b8057c8", "guid": "bfdfe7dc352907fc980b868725387e983ab42203afe9c8e91c69c7fad6a469f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae3976fde8b673e010fe86a16f2ea181", "guid": "bfdfe7dc352907fc980b868725387e9877ca7a82940537c3064764569eaaa31f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ab47f8bb8c9226ddfaf60376fbe170b", "guid": "bfdfe7dc352907fc980b868725387e98b794cc652a29b02d2680a10667e8c282", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e398974b77247f0fe1736e2f9f6b53a", "guid": "bfdfe7dc352907fc980b868725387e98fafb42ad87c587e360d632b896d58ecb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805ff43dd56daa396f653e253ec3f6383", "guid": "bfdfe7dc352907fc980b868725387e98af66bb700259c50762cbb9e6cfd2254c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871eab4926c0dedabd68361b19f3cdeb6", "guid": "bfdfe7dc352907fc980b868725387e98a5944afdf924e15e63ee058d55b1b91e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818bfce5072dc9d05b73d320123bbf07b", "guid": "bfdfe7dc352907fc980b868725387e98a3e9bcc80352e525fdf67434b860d67c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bbfa256abb8cc48d6ef0df94e8e2a003", "guid": "bfdfe7dc352907fc980b868725387e9855e2a923df3daf55159a2a54f0193a51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982762a59e0c2e9c89e86b4d6b169d83b3", "guid": "bfdfe7dc352907fc980b868725387e980720542c3c6d4d854191ac7f2a7f55c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b77d5d53ee94d40bf5db5f0693b499b1", "guid": "bfdfe7dc352907fc980b868725387e98b65919fd3c087a1912fe6a1f6d24e44c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9cfa275c6b78763dd8d4c60173c48c3", "guid": "bfdfe7dc352907fc980b868725387e98c42739d9fa0bb93643271d6740345ed5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3296b2e0c5a3f3a82994df05111304d", "guid": "bfdfe7dc352907fc980b868725387e98022826e4eda9b0a9c14f37b8ff79e698"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98908824437850010f3b3cb9d42f308147", "guid": "bfdfe7dc352907fc980b868725387e98c167a18929aab682a4993b337f763dab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d183ccbd06cb42321e4a48928626f84", "guid": "bfdfe7dc352907fc980b868725387e98b4f37c13df0236dd06174c7f38bf8a08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98835ca5655c91f1c9bdd86d27e8511cec", "guid": "bfdfe7dc352907fc980b868725387e9832093b63933304e3f9d83831465d8a91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98188ce3702fb46d73b14c70a40e2c801e", "guid": "bfdfe7dc352907fc980b868725387e98af5c0dde4446e49a3f44a6c3f5367e80", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d7d3baa724297fe622ee16c47b75de9", "guid": "bfdfe7dc352907fc980b868725387e98797be3932613ba5ed1cb8b191b30260c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886c827d6020e6e11b18648093acf3b04", "guid": "bfdfe7dc352907fc980b868725387e98b58aa0f5149465e56cd0d9f3a6afc32e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1e023046a6abcd470b44fd381a0e261", "guid": "bfdfe7dc352907fc980b868725387e9853f2ed12b7ce4e7025ad124137373862"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e42b2f07a479e3f0d943ad175c040b1", "guid": "bfdfe7dc352907fc980b868725387e98e2c3d9e2cab5d1cfb09accac711197cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b3a23cf700b9318e1b294e8de2021e3", "guid": "bfdfe7dc352907fc980b868725387e98b088749a7bc7ccfa6d43099ba5d50b79"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837d52942b5c7c9fd220e16fca13ea20f", "guid": "bfdfe7dc352907fc980b868725387e981bf4d15ac40a0782b3b8560646e7bd22"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984de0b853116ac536fcc77d847babea6d", "guid": "bfdfe7dc352907fc980b868725387e98060026eefb9a99b4d97cf13cd87293ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c56dfd6e86746aa528f7f55f6edc882", "guid": "bfdfe7dc352907fc980b868725387e98ae795a6abe030c51c3423fa10f447bf7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb375762d7a822963ac9b83391ff2580", "guid": "bfdfe7dc352907fc980b868725387e98c89ff8a5367e5f98560b436165941cb4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98910182300726419903c6ccac08711d31", "guid": "bfdfe7dc352907fc980b868725387e98c7eb51309276fbb56ca3a75dd446df88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98395fe33e9820064cb408722a1d80cbbc", "guid": "bfdfe7dc352907fc980b868725387e9854820d2de294b0c943ace1584ac1eaf1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5e45b9933ababacc1cb1c8f6c107f78", "guid": "bfdfe7dc352907fc980b868725387e98dc330382972a2f0af154f0cd2048c625", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bef2e941ea503a059d78f5730925c8d0", "guid": "bfdfe7dc352907fc980b868725387e98eb663fd11fb045360aa6e9bfed02640e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805762af1901b02edf4822f42d9550d36", "guid": "bfdfe7dc352907fc980b868725387e98092c4c94b25bf00a364c717b012d9d0e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985fb9ee4aa139fc5ad81855392bedefa4", "guid": "bfdfe7dc352907fc980b868725387e98eedaee32f9004848bce66c07fa5f0249"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f80509a62b672de4f088da85c697979d", "guid": "bfdfe7dc352907fc980b868725387e987f4c5b28d89777878c11553c6a6477e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bceb51bbe6aa35f6d1cc99abdc14a5b4", "guid": "bfdfe7dc352907fc980b868725387e982199f2e0b44db9643d61a956524757fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894da6b086d5dff5eb217ad6f34e1455d", "guid": "bfdfe7dc352907fc980b868725387e980b3d0e8459fa9e45a70901682453979c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98fb0550d1d2c255e683970ce3b0aaf58e", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981321a52243d6a3942e000e536f5bfac6", "guid": "bfdfe7dc352907fc980b868725387e986b09ee0dd6a38d70ed6345663eefe7ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b32b332945445d7ab2a77a64fdb5b15", "guid": "bfdfe7dc352907fc980b868725387e989601c412de760e7171d754e979a5ea37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fa4679f12dc1361c9eee8aa4bc7fea0", "guid": "bfdfe7dc352907fc980b868725387e98a11dc59a0663cabe9786d8da1ea002d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890a2d11f1baf88b6b697a616441c5bfd", "guid": "bfdfe7dc352907fc980b868725387e98410104bd4f1c1519fa1fddedf00004b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e77f724092d2015d860843ae1e1f89c6", "guid": "bfdfe7dc352907fc980b868725387e98f5152c5c59d61c011d184590ed97d1b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98831b96f63be9daef8078594846c39505", "guid": "bfdfe7dc352907fc980b868725387e9831c354c7931214a84550d7ce57147fa0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98099df6bf3d2de35411cddf566d9cf635", "guid": "bfdfe7dc352907fc980b868725387e989470ce5de3bc80142875cc51963a9015"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee153facd82786098b7fcc7737915293", "guid": "bfdfe7dc352907fc980b868725387e984815c0b54f218022260d377bfd0d93c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f58203668154cef5c96729090a5b96e8", "guid": "bfdfe7dc352907fc980b868725387e98d881b19c042cd353c6d507a770d6096c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d41c4b55a0b0f3d1f012d6805fa99cda", "guid": "bfdfe7dc352907fc980b868725387e98f0f08b111fdd1e6662ec87b3dfbe0ed9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f713ad03090e4751093f38c4c30f3a92", "guid": "bfdfe7dc352907fc980b868725387e98f6c9f19c94e46423787f3ac1fc99f772"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879356d5c8d2ecd40a67aba5d3fe3785f", "guid": "bfdfe7dc352907fc980b868725387e989ca9681924d028672644a57eb543c2d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d71cbb313790502e0e408b34141a961", "guid": "bfdfe7dc352907fc980b868725387e9800cd45b8054c2daef3ab45cc0d40a456"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e1d811ca2081829ff31d13c290f1455", "guid": "bfdfe7dc352907fc980b868725387e98aec2483b3e5ff6e5d62c150657491a2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7c2f8f2320d4209ab90c61ad6ab8663", "guid": "bfdfe7dc352907fc980b868725387e9807426feffbcdf085a622314c9931a3cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6c666a3255c386b630b47a52d3d155c", "guid": "bfdfe7dc352907fc980b868725387e987da9a29a469d53f3ff1ff042e18bd45d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cd3139fc9bf194281eb7f1d3d9e0401", "guid": "bfdfe7dc352907fc980b868725387e98ef25b30a9d40f7a69a6782ab027945f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a557ca0eaea25218da6c4f5706cf918", "guid": "bfdfe7dc352907fc980b868725387e980d5db848fc56fae875c761568c387a7b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883a03baed3c0da22b63ccedadf61c3d9", "guid": "bfdfe7dc352907fc980b868725387e987041cb1c0721c75e521691258c280677"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c014bb760a9aa35d98d972b9b823f0f7", "guid": "bfdfe7dc352907fc980b868725387e98ee23cbd9816b7885cdd621995cd95920"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838f384f21b33c52fcf83247e99e3d7c7", "guid": "bfdfe7dc352907fc980b868725387e981d78f0f418ce9dc54b45675ae80cf426"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98220ecb84446dcdca7f42dd73a3ef8366", "guid": "bfdfe7dc352907fc980b868725387e982cf6b1b42ffe06917ff63cb72b0d5d86"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4f9b6af9cbe579b04b5e0953622f5a3", "guid": "bfdfe7dc352907fc980b868725387e9817b2937c7a98a8960ea2533aa2eb6463"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824385198f0fa7d3dc42ac2151c58f0a0", "guid": "bfdfe7dc352907fc980b868725387e98a00bb323bd55362b2b7f5a36fc174960"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd6c8a30e2f8c9456f021c900479b76c", "guid": "bfdfe7dc352907fc980b868725387e98064bb56e35a0cb7851c205f7dc013caf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98842eb8a80972f79d99571e3d55d4c03c", "guid": "bfdfe7dc352907fc980b868725387e98b7df1bda8caadde954b385d5234cac60"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d8b956b3a81b3e7829055e1a16c5b4e", "guid": "bfdfe7dc352907fc980b868725387e98b262090528d2510ce8b0c390e08d133d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a68b456ff7c85d0a1e9d5f992e79dbdf", "guid": "bfdfe7dc352907fc980b868725387e9826a7fa71edb306370e0a236b9060867e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866e1e897d10384bba8b0a83e0ba05ff1", "guid": "bfdfe7dc352907fc980b868725387e98e94e1ba91eb14d9d8b6010ddc5790506"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845326383195202b49c4186d69b9cacf3", "guid": "bfdfe7dc352907fc980b868725387e98e7e38c850fb1437ae0c0ae80262c62c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb6859e0756e51cfdd40f14b57d53a4a", "guid": "bfdfe7dc352907fc980b868725387e98e6314eff5d1064b74e6813c3cf62ff86"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5558b953082f9113157535d497658cf", "guid": "bfdfe7dc352907fc980b868725387e982c56a9a0be0986eb5a222014dcc05cc4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989985d03d5665daddea557ce92f12b240", "guid": "bfdfe7dc352907fc980b868725387e98051c924218d6fd99b63a39cb840277c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a31f059b32a790332ab09120746dbfcd", "guid": "bfdfe7dc352907fc980b868725387e98a190cfce42ef41f8945a5f6dd9474d6e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b815cdd6b9716e4251f26bf5fbdb5e9f", "guid": "bfdfe7dc352907fc980b868725387e987ae3cf2c87b544d2a30f7745a8faeedc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4ef7b0adb6a31f4d1c1d5b14a0e012d", "guid": "bfdfe7dc352907fc980b868725387e98769844d87ecaebd2e2fbdfb4810bae78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bd4ba27043e6071bcad98647fc962b0", "guid": "bfdfe7dc352907fc980b868725387e98ea28163f3210d20207797b215968a1d9"}], "guid": "bfdfe7dc352907fc980b868725387e9851c8c397d3d277d861ff5a45050bb1f9", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bd93308fa6b342d20c2b1b97a6b5030c", "guid": "bfdfe7dc352907fc980b868725387e9847afb68a2750884bbd3d8e5f3fa72e03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988338730910fc8b3bf8d9a66916e6c151", "guid": "bfdfe7dc352907fc980b868725387e980424644aee2cb76a455d4997807c1a37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c90a291e15c142c2bbd124e754829509", "guid": "bfdfe7dc352907fc980b868725387e98b2a8e110ea8dd2221cd3a5919e69aa5e"}], "guid": "bfdfe7dc352907fc980b868725387e98b043f9179e8619071337137f258680a0", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e984f871771ffd614c494ec9af19d5618e0", "targetReference": "bfdfe7dc352907fc980b868725387e98bb3e3ebadbb0b9a8a4f20f605e3cb3cb"}], "guid": "bfdfe7dc352907fc980b868725387e98ae654ecb94998c23e6ee668b34d1122b", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98bb3e3ebadbb0b9a8a4f20f605e3cb3cb", "name": "GoogleDataTransport-GoogleDataTransport_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98c64019424081ed2ed9efdee0281dc680", "name": "GoogleDataTransport.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-YL", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-JS", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-JS2", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-YL", "provisioningStyle": 1}], "type": "standard"}