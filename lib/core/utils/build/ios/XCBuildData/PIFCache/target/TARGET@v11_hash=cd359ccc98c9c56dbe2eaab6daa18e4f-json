{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ca0a5264e53a05d9222fa0718270b817", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/DKImagePickerController/DKImagePickerController-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKImagePickerController", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a18bce5e14eceea5be3d74a58340cbd4", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985a9ffbbd1ba48ce76a13fca031b49cd6", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/DKImagePickerController/DKImagePickerController-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKImagePickerController", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b9cbf67989e0774643d84a7ab4e0dbd9", "name": "Debug-YL"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985a9ffbbd1ba48ce76a13fca031b49cd6", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/DKImagePickerController/DKImagePickerController-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKImagePickerController", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b535e095d7f2700e54f3a8b4b90ec8d5", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985a9ffbbd1ba48ce76a13fca031b49cd6", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/DKImagePickerController/DKImagePickerController-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKImagePickerController", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c2dfe087c32f54bfd472e8566647a9a5", "name": "Release"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985a9ffbbd1ba48ce76a13fca031b49cd6", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/DKImagePickerController/DKImagePickerController-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKImagePickerController", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981a6f70a265be62912e7e4f4c7e3067a1", "name": "Release-JS"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985a9ffbbd1ba48ce76a13fca031b49cd6", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/DKImagePickerController/DKImagePickerController-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKImagePickerController", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982d2d4d96afe100d48b6bff25117bd925", "name": "Release-JS2"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985a9ffbbd1ba48ce76a13fca031b49cd6", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/DKImagePickerController/DKImagePickerController-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKImagePickerController", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98687538f5e50d42d2bd439c77467eaa64", "name": "Release-YL"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d6edc75717780018b1a249a0e8c40659", "guid": "bfdfe7dc352907fc980b868725387e98e836ed5d154b0b80db4386ae6323f6fc", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98e82b3236baddc5873c07c5499e953be3", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98765ef2f402a859b944fa640308f9e397", "guid": "bfdfe7dc352907fc980b868725387e980af948861c177f981d46f8eb29896d5d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869b2d3e14c3748bc5f5486d91aedfd66", "guid": "bfdfe7dc352907fc980b868725387e985c5a5a4b755b26a16c6c4401ec8fdbf2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a42f4c9f42dae9868b41124dfa7a152", "guid": "bfdfe7dc352907fc980b868725387e98f8b1d4208a740883f13ec2f4f1370edb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2a0990ab19277f6f96a2d63f70b8628", "guid": "bfdfe7dc352907fc980b868725387e98fc8d56610511662c4c3f4333eef910d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cb801ffca4ab152986c034564826c21", "guid": "bfdfe7dc352907fc980b868725387e98f9d4c6a8ebf7926f1dd6facab6982e5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c07e215d4a04fb94731d57b49cd48e53", "guid": "bfdfe7dc352907fc980b868725387e98294faa3f15eb63a1c6f72352b7a6cf76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98808f0f567bce8e8a5797475a03e464ce", "guid": "bfdfe7dc352907fc980b868725387e981be36c2b7f55b69813e3f3a61432a390"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b73f3d8111f50e726be070a4a66cb25", "guid": "bfdfe7dc352907fc980b868725387e98eb0229bde5200c220c2f5d102f55aa66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98537bc867d3899218dee64aee30f47c5d", "guid": "bfdfe7dc352907fc980b868725387e98ec91c6c38d573f674847a4d2243973f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98616b274a5d5378ec32ca0cf6f775e8af", "guid": "bfdfe7dc352907fc980b868725387e980c3e9f3f297056623043b1d4e6629f2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d345ca8e849c7628b02676fbd53b3981", "guid": "bfdfe7dc352907fc980b868725387e98ed0d01a5cd41a59b1a07491b18289fca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98981e29c858a526851558ff9059b09a85", "guid": "bfdfe7dc352907fc980b868725387e984f9fc72386c3f8671cb42078cd5940fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1bc25ab6a81385354a9e3abc0cdac25", "guid": "bfdfe7dc352907fc980b868725387e980451d3d0857bcdf3b38d68810378ab28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b87ae4f96e5a5a9848723dc9e6128e2", "guid": "bfdfe7dc352907fc980b868725387e988bb3864d180b603510f0d25e95b6d1e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983dc4b7badfb5d82bba3065d4785b81a9", "guid": "bfdfe7dc352907fc980b868725387e98cb668663167ab8c15847c5d129bbe9eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e13ac9494df4cf42dddadfa6e96f6c39", "guid": "bfdfe7dc352907fc980b868725387e98dc1750f2c94177587211dee42778eccd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867e517cb2996c1368b6cbf1924b83f95", "guid": "bfdfe7dc352907fc980b868725387e98e80912324ddf6fc92c626e01a62e15fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6a808f42247fa16353be7c0ecae3a10", "guid": "bfdfe7dc352907fc980b868725387e983105aa1b8075084fda24570e6991baa1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880c35eda38470b14908c5f008704e63b", "guid": "bfdfe7dc352907fc980b868725387e989e182af3c578c3fb63d2694783cd3a8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a98e92a1370384e1c4957ee6ddfb3caf", "guid": "bfdfe7dc352907fc980b868725387e985fd6a39238978ab5e3d5cde51ff4d503"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eaafdc250eb8f17957730be01317b849", "guid": "bfdfe7dc352907fc980b868725387e98388b24689d23673c949f2b5bdbde45be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c94eed39410ceeb5ccd28ee85f0a187f", "guid": "bfdfe7dc352907fc980b868725387e98876aaebffde508bda21f4d4b8948896e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be8a63b2aba549ecb308b7333ef3843f", "guid": "bfdfe7dc352907fc980b868725387e98832f5b3fee4e422a8aa6533caf0c9cc9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863fc6c5b481ba494607f9398b4538f1a", "guid": "bfdfe7dc352907fc980b868725387e986d26102a10b600b9517a4f4973aac497"}], "guid": "bfdfe7dc352907fc980b868725387e9862d23bab8ca40548a23d8eec77df949e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988338730910fc8b3bf8d9a66916e6c151", "guid": "bfdfe7dc352907fc980b868725387e987bac126b25396d98fcce16dedeaed90d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989738302f2e2d846c3298e633aef5b938", "guid": "bfdfe7dc352907fc980b868725387e98a40aaa8aaaf41b176e66f160b6ab1d15"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870148b0a96bb4f7c7fd953ab7ead94ea", "guid": "bfdfe7dc352907fc980b868725387e982dbea6beafbec16f990b610133cf2c62"}], "guid": "bfdfe7dc352907fc980b868725387e9802008a37ccedbddc906a795e4978f8b7", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9827061a6710b4b436e20678a2419b7add", "targetReference": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3"}], "guid": "bfdfe7dc352907fc980b868725387e9880da3c61a808f17a03c5dc04644449fa", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController"}, {"guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery"}], "guid": "bfdfe7dc352907fc980b868725387e985fd5cdb9993b1816141f0c012ffa62bd", "name": "DKImagePickerController", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f752ba05adf197c3696519e901961310", "name": "DKImagePickerController.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-YL", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-JS", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-JS2", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-YL", "provisioningStyle": 1}], "type": "standard"}