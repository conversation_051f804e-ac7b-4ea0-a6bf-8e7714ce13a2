{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ac1e0a2ba24fe0e2f83f1c976c05c209", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "camera_avfoundation", "PRODUCT_NAME": "camera_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987c1ff3c9c3c8b163dfbc32fb1b6fd8aa", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c8996d2443bee237405e60e8b79d3b2f", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "camera_avfoundation", "PRODUCT_NAME": "camera_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9887722c308cf56a4c0fe0087842b73913", "name": "Debug-YL"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c8996d2443bee237405e60e8b79d3b2f", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "camera_avfoundation", "PRODUCT_NAME": "camera_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987b3cd1f6e2580a49415dac240fc76a2f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c8996d2443bee237405e60e8b79d3b2f", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "camera_avfoundation", "PRODUCT_NAME": "camera_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98749fe64ed2bccf2728d7777a9ed9f60a", "name": "Release"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c8996d2443bee237405e60e8b79d3b2f", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "camera_avfoundation", "PRODUCT_NAME": "camera_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e50a65c965a49b9461206b83fb6c809d", "name": "Release-JS"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c8996d2443bee237405e60e8b79d3b2f", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "camera_avfoundation", "PRODUCT_NAME": "camera_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a9567d2fbf767f030f7f4ccac017ad07", "name": "Release-JS2"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c8996d2443bee237405e60e8b79d3b2f", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "camera_avfoundation", "PRODUCT_NAME": "camera_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d639406a1d08ad7d4163a1c957b53b18", "name": "Release-YL"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f758fcb8025f84f6a3fd1ad473d5005c", "guid": "bfdfe7dc352907fc980b868725387e985b3bc12237385cd9f72443c331b2d6a1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b25c00bd7103a26029e91631f0341b7b", "guid": "bfdfe7dc352907fc980b868725387e98a6140f02f09e58c31515988f00c96fee", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1211b96d118394178350c74e27839f0", "guid": "bfdfe7dc352907fc980b868725387e98937fe760b2e1e212ff452ec899b8912c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ba5e412a8ed257fc58339d41519b6ae", "guid": "bfdfe7dc352907fc980b868725387e9873e1d44629883ad89b0c2b8e55e9e825", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856e853d5bfc860386f1b190195d7dfea", "guid": "bfdfe7dc352907fc980b868725387e982623e55db7a7ba09d5d2f2bdaedc5a76", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5725db453f4cb3247c3a67b5220f7ad", "guid": "bfdfe7dc352907fc980b868725387e98a168f7c91dbf30da8568abad6cf0fe74", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0a0ea69ef551950619937e70f4ef53c", "guid": "bfdfe7dc352907fc980b868725387e9876ccb06d2a12ddb42c0df3c76d66bfca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af6e00f3947d24cfe6949d7b6d49724c", "guid": "bfdfe7dc352907fc980b868725387e9802f0e9e8754e46e9e0d74f18e2279754", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885583fcee6c8faafd246bb4739e68678", "guid": "bfdfe7dc352907fc980b868725387e98e7f4d69a61630305dad2d8e1ffd0ae16", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b08925e8685a639168328ec4fca3abc", "guid": "bfdfe7dc352907fc980b868725387e98ad1487593e373b4fdc03de3ac702300f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd818fcba5968e6efd0d7d5b4524e430", "guid": "bfdfe7dc352907fc980b868725387e98e1b765c984d4525343e0f1e9017a4b98", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ac33b567d9431c1b947e200e8a115da", "guid": "bfdfe7dc352907fc980b868725387e985b033d509f5aa204d785b4876d7a3914", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98493b0587f8b54ea36b1083a5ba587528", "guid": "bfdfe7dc352907fc980b868725387e9846690dbf147ecd26782ffbbcf7a644a5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6d23de15dd8da161d563c68e1839e89", "guid": "bfdfe7dc352907fc980b868725387e986e485e62538eb2a6800c03c1cd5cf5e0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832a983f50e09ab2c27b97844ee10b90e", "guid": "bfdfe7dc352907fc980b868725387e98d3c615ce2ace54e98920575b91d9d009", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd6279005e0f1d6111137e133ad6e961", "guid": "bfdfe7dc352907fc980b868725387e985149b94cf6ac2876717e214bcd3279fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c61296acf792ddc7895859ed5ee12947", "guid": "bfdfe7dc352907fc980b868725387e98c06020b22f65592f1f5ec06ec00ea5bf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984937ad62452efc629640d488acc0f219", "guid": "bfdfe7dc352907fc980b868725387e9878b74ef737dbcb8337bc2604b3684730", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf1dc900b61a8698a353983d89654d35", "guid": "bfdfe7dc352907fc980b868725387e98a4de3ab3ac31c60b65d0bd7e5c7b1d73", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9d46e1ee9c98d5e0abb82fbc844699d", "guid": "bfdfe7dc352907fc980b868725387e98247c0bd2e2ed15ad6dfb7f66ad6b660f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d63995e942491d4dc8fe4fd90c8068e7", "guid": "bfdfe7dc352907fc980b868725387e98c4a4d11964e24ccd2186dde1cec810ee", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981894d8a686cec82e4b4310e8111c24bf", "guid": "bfdfe7dc352907fc980b868725387e985e3864558bb264f363d1849f4face226", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3c49ff38445f5b8f98475425c83478b", "guid": "bfdfe7dc352907fc980b868725387e9893dee449b6b723dbec5e5a52655af529", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980aa2625295c5f5a9ef79ea9dec35676f", "guid": "bfdfe7dc352907fc980b868725387e985397521a6c4f02f650e1190fc7da57ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989164380c39e146aec7556a72713db0bf", "guid": "bfdfe7dc352907fc980b868725387e98b099a6dc6c0f98a92b3cf9be33638980", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea1933e136e0863cd1c8f0a138582c08", "guid": "bfdfe7dc352907fc980b868725387e98dfcf1fee7559f60ec4366a029e8c43aa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab0fd17ea0154994140fbe43dc0a6bd3", "guid": "bfdfe7dc352907fc980b868725387e988a7fb72be570d46877678404d52b4e71", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98565256ef06a52ca9674afcc91fa5b1d8", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98199b56d5eb98213819833047c1e59b81", "guid": "bfdfe7dc352907fc980b868725387e9899418e891f197b880a8ed863813be42c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880d24875b6c9d17ffec780fd4af5bd4a", "guid": "bfdfe7dc352907fc980b868725387e984e78844b5221f0cd750c97b36a81162c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98addcbf2dcb6b3972b50e5c380bbe6666", "guid": "bfdfe7dc352907fc980b868725387e9812569262c29ae9f1b407d39355c05f10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868286527a12d9a05ece252213496ef28", "guid": "bfdfe7dc352907fc980b868725387e9837f41126d94e7d45a670335d6584adac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7e68f571bc2302d7c2d5bc662507671", "guid": "bfdfe7dc352907fc980b868725387e98f05d4291e661e69f21200a8afb6480d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a62248d7919d9eb3d45a93c7ce255654", "guid": "bfdfe7dc352907fc980b868725387e98e7206ba13ef6252e5bde458f3746c1d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6d523e737193348c25f4ef035ae175d", "guid": "bfdfe7dc352907fc980b868725387e985a6062a8c8f926935165dd75778200f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db6d970e3e32b74f34a959754b2bdb7b", "guid": "bfdfe7dc352907fc980b868725387e98d427716033382166276c2e5a876d6f84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841784d401eca87a502fd70a708bd7728", "guid": "bfdfe7dc352907fc980b868725387e981bb7a405fa5830882f2898a385595301"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e12b2c105d5a2aa5ed55af43b8eeae63", "guid": "bfdfe7dc352907fc980b868725387e9889003775b54e98b333730272de10fc4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b567808b1f804e0c517b1d2dde54fddb", "guid": "bfdfe7dc352907fc980b868725387e986433a308a59d5f6a362c0d88d113b3db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fdb9c3aa541b2d3057b3ba4013b490c1", "guid": "bfdfe7dc352907fc980b868725387e98c985802a8f8c23d002b81a85f8222cfe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb76d95e4f5448fba42e6e51ded7a8e5", "guid": "bfdfe7dc352907fc980b868725387e984463aca9b3a7825b56ce11847fdfda6f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98636e0e1683e0ad2571e855e3af6712a4", "guid": "bfdfe7dc352907fc980b868725387e983d8174123aa7cd54a7063991c66207b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3ddb8bb1920556186fc13dfde853473", "guid": "bfdfe7dc352907fc980b868725387e989b9ec006ba964a43d72a7c141846ac71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cacda25b3b2d761887615772fdf2de4", "guid": "bfdfe7dc352907fc980b868725387e981b69ee7918939920b7973ef672de743b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877fefe30eeb7769d53bfa3819226a00f", "guid": "bfdfe7dc352907fc980b868725387e989fa2fd7c416b2ed8994a7b3c89fb5f3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987122ccf42bb1846f31d5236671cdae75", "guid": "bfdfe7dc352907fc980b868725387e98457bf5bf11bc5263b105c0acc1af9296"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3eb9b3ab5147e1d0572f3871a556136", "guid": "bfdfe7dc352907fc980b868725387e98c1a9b0f8fcee6f1e08f4946bf4a3dbca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7d16d62cb68803bdc4665291b48f179", "guid": "bfdfe7dc352907fc980b868725387e9875f35735ba24e6e3d17a7ade42c4049c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878d2d27a1377a16120ea04db7b8cb217", "guid": "bfdfe7dc352907fc980b868725387e982136ec90fe198082da74cd940f05897d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecc7efdfdaeb01cd38870f93c78ec7f6", "guid": "bfdfe7dc352907fc980b868725387e98227586b8299ea4ee89031c85d431d4a8"}], "guid": "bfdfe7dc352907fc980b868725387e984286408ab23e89aad8b5ed0db9473258", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988338730910fc8b3bf8d9a66916e6c151", "guid": "bfdfe7dc352907fc980b868725387e986f47578dd24103a255d2f41313e33ced"}], "guid": "bfdfe7dc352907fc980b868725387e98f129a32df0f5b29d2b57e216498cdf3b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9867a26bf66995f97e7f3d0c26a481882c", "targetReference": "bfdfe7dc352907fc980b868725387e98b9038e7e871b75150c57ed973218b158"}], "guid": "bfdfe7dc352907fc980b868725387e985d2e2cd01a5d60196231aec1a3331d0a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98b9038e7e871b75150c57ed973218b158", "name": "camera_avfoundation-camera_avfoundation_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f08a09402d437c098acddc7bcf497e64", "name": "camera_avfoundation", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983903b9d6299cde09dc2b081ad04abafe", "name": "camera_avfoundation.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-YL", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-JS", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-JS2", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-YL", "provisioningStyle": 1}], "type": "standard"}