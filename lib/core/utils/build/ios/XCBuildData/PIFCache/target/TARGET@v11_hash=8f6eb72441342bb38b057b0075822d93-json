{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9813643ccd9a826a56749fb58b77f0f1be", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/photo_manager/photo_manager-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/photo_manager/photo_manager-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/photo_manager/photo_manager.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "photo_manager", "PRODUCT_NAME": "photo_manager", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a8b6d4ddde13f15012a08ad65461a455", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9824b4a68e5109b2f3a128502c40356d05", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/photo_manager/photo_manager-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/photo_manager/photo_manager-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/photo_manager/photo_manager.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "photo_manager", "PRODUCT_NAME": "photo_manager", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98068b429d30ff0c5e374d9bbeb99da7ca", "name": "Debug-YL"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9824b4a68e5109b2f3a128502c40356d05", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/photo_manager/photo_manager-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/photo_manager/photo_manager-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/photo_manager/photo_manager.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "photo_manager", "PRODUCT_NAME": "photo_manager", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981f063d6bf1436bc0dd0b544607ec3992", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9824b4a68e5109b2f3a128502c40356d05", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/photo_manager/photo_manager-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/photo_manager/photo_manager-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/photo_manager/photo_manager.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "photo_manager", "PRODUCT_NAME": "photo_manager", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98349688867a4ef902acfe6f95fb6c3566", "name": "Release"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9824b4a68e5109b2f3a128502c40356d05", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/photo_manager/photo_manager-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/photo_manager/photo_manager-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/photo_manager/photo_manager.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "photo_manager", "PRODUCT_NAME": "photo_manager", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980abd0645d2632584c7ccc0037a7bb761", "name": "Release-JS"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9824b4a68e5109b2f3a128502c40356d05", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/photo_manager/photo_manager-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/photo_manager/photo_manager-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/photo_manager/photo_manager.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "photo_manager", "PRODUCT_NAME": "photo_manager", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fc84adcb9535c57d5fa621a9b28f0907", "name": "Release-JS2"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9824b4a68e5109b2f3a128502c40356d05", "buildSettings": {"CLANG_CXX_LANGUAGE_STANDARD": "c++17", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/photo_manager/photo_manager-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/photo_manager/photo_manager-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/photo_manager/photo_manager.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "photo_manager", "PRODUCT_NAME": "photo_manager", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b1cc090d4dfdf5021b553d0670e95d88", "name": "Release-YL"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9c198c8a75e0ea7ea136382fc78abfb", "guid": "bfdfe7dc352907fc980b868725387e98e8f2050b8c1ca1ae0c7d2d667899fff4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e3530bf0261ea068cfbb4fdb831d539", "guid": "bfdfe7dc352907fc980b868725387e988ada96d08db8a5a4e908112f96d52bb2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814fe3140d382416c208786ef0828b5fa", "guid": "bfdfe7dc352907fc980b868725387e985a5647e3a927e6ec790912d79dd84b88", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6a4d0de16a4d96dd0e1ae1608fbb503", "guid": "bfdfe7dc352907fc980b868725387e98357d9d109a217a33404cc53f98b324cb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887acd4bc02b292f8e43063e7fb531a09", "guid": "bfdfe7dc352907fc980b868725387e98bc2ac361b5bb26f01f9c96313bd48eed", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98857813c5f0164307395e0120e25f4b01", "guid": "bfdfe7dc352907fc980b868725387e98964842599d12db858ac824a31eeb5d9a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4ee952c03c858057190785f9676d7b0", "guid": "bfdfe7dc352907fc980b868725387e9822e49455a5b6f5809fef4b5dae020480", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803a9fc7643ac550ed3b9b0de0f6a6cfb", "guid": "bfdfe7dc352907fc980b868725387e98cb3812af08b8fa0b298d1f79089928ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ff4bec8cdc75d992820b770b8d2983d", "guid": "bfdfe7dc352907fc980b868725387e984f11d704fb7172e400b1ff0b55e36415", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989698653edd50f03ddf2f122ae8b84acf", "guid": "bfdfe7dc352907fc980b868725387e98f6eae0c41135c665c144d983a3af3861", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d348f71c0ebfa13757812f649a00617", "guid": "bfdfe7dc352907fc980b868725387e9817f419f53cab3141d36c6e2a81432b57", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb6100ecc56c7462c3329e7a894fe3e2", "guid": "bfdfe7dc352907fc980b868725387e9876b830b5cce409209be8a44a91e7406f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848857ec2b2a87776ae194b020aa87703", "guid": "bfdfe7dc352907fc980b868725387e98b2d6338a6df8e8bbdf9d1db021b49e58", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bb09c7df6d7402377ce08ff389604ae", "guid": "bfdfe7dc352907fc980b868725387e984e641d66a56e170911f73de296cb3d21", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887e8557b5c5e41b6aba8efd0f12f6bbe", "guid": "bfdfe7dc352907fc980b868725387e98955fbf7862eba5cb2595da6c73b29551", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98869a45a0f0a06efee0f77e41e77fafdc", "guid": "bfdfe7dc352907fc980b868725387e987019b3d46e37c9f94ed435a05f972b03", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840c4de4dd217696a017d6c871a654f62", "guid": "bfdfe7dc352907fc980b868725387e98270d5d35cf7f1a54fae3963425f550ef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3a2d8ca02eaa7edf987450f2c875574", "guid": "bfdfe7dc352907fc980b868725387e98a2f9b633c0501a4b314ff6848bbeee45", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98197af041de71c4843a1fe4e7e32a70e0", "guid": "bfdfe7dc352907fc980b868725387e9813c4dfaefe58258fa669134cfcd6716e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5b7d8ded01f1cf7e888225f516c34dc", "guid": "bfdfe7dc352907fc980b868725387e98558dc76f87bd6b27b7ad0e8f8d23ce3b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d71d305e2842505a41c75e32c8d2736", "guid": "bfdfe7dc352907fc980b868725387e98010669e9bc037ca58f722541e87b3b97", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987901efd61e18627c0aed292e01161f47", "guid": "bfdfe7dc352907fc980b868725387e98f86ca1ca129e7e66ac81d755f2f306a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5803a9084c0a9542ba52d4fe699aedf", "guid": "bfdfe7dc352907fc980b868725387e98d9410524b1445de317b750b6981aa8e2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b6a02030758096ae4ce8cee3f158238", "guid": "bfdfe7dc352907fc980b868725387e980c9f0c5ac5f5b8b66ffc5c2c65b342fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a9c5715533d8c138c0c3034767fca96", "guid": "bfdfe7dc352907fc980b868725387e9802fa02a99a7378676182c77bfe41c9e7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d309822a34d6bc63885f554b101f7809", "guid": "bfdfe7dc352907fc980b868725387e98506c68278466b8677f74b79747c15775", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9084415f22296a9059b21ce5a678fe6", "guid": "bfdfe7dc352907fc980b868725387e983e32e792ed0aa856a2dab21c72e8daa1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98767cd422d5ccead901874e8db4f793a5", "guid": "bfdfe7dc352907fc980b868725387e9889fcd7c1147643aaa05964161cc521b3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98627aab5b88d8cde0c65c3b5e6a31e43b", "guid": "bfdfe7dc352907fc980b868725387e983dfe4adca07e3a0d1041f54291209f49", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b26039b6f9c01b2fd1bb131553442b0", "guid": "bfdfe7dc352907fc980b868725387e98bc82d57791991ed48bea792635042ad7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d30cb4de0386f43191d0439e70c35c3", "guid": "bfdfe7dc352907fc980b868725387e98ca6b08d29715e6d6e66d9948e7aba397", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9891c943483c2016da43744c7467a4f8e2", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983a0ef862f587a93cabd897757c793765", "guid": "bfdfe7dc352907fc980b868725387e98a61a4b33fbfff9a0bfb8b923388fd704"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982eafb99071bac3411a29ef01de744943", "guid": "bfdfe7dc352907fc980b868725387e9872304483ffca8581475fd8d8f739ebbe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b86f90479c52a40aec23185a20979de5", "guid": "bfdfe7dc352907fc980b868725387e9872a23de0245205fdb9716824c85ef4db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fa0f7aaede1edf84fd38d2702e31c8b", "guid": "bfdfe7dc352907fc980b868725387e98ba927b796c6575ea11c5faf2d8ba16c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1b30e5e19ef25ac9d6590a6a346cef0", "guid": "bfdfe7dc352907fc980b868725387e984609380f551156a95bd4d49548431f6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b584d7c7af0478cd042308b7479b010", "guid": "bfdfe7dc352907fc980b868725387e98b9710f4eb8bf0037179dfc5b315fcf29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9e3318fbf0a536518a03e79a26d4a0e", "guid": "bfdfe7dc352907fc980b868725387e98fbdeca482cb924ef774a4912904dd983"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa8df11f0cb5b4c2017868e4348cf9d9", "guid": "bfdfe7dc352907fc980b868725387e98244ac1b33f81cdd06640ac61b6072235"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846b9d523a964b8a5bdf1bb89e0f72f6f", "guid": "bfdfe7dc352907fc980b868725387e9818eaf7bed18fca2202647aa3e2a4014a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1054bf85c021e4b297eee9691f67829", "guid": "bfdfe7dc352907fc980b868725387e98919e520b2ff57f5bae6175e6a80384d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872ace873d7a5edd9d48c1ab3d7010685", "guid": "bfdfe7dc352907fc980b868725387e9839b8c8e0181e686ccad7c1f366b79e2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4ef88d449cce02cae6ba73115a0a8a3", "guid": "bfdfe7dc352907fc980b868725387e9851cc24dfc71f17164ab59920df538293"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836873deef39e124051b2984c115280ed", "guid": "bfdfe7dc352907fc980b868725387e989842d26fea0c2277bb8519408ea3a67d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98287e919ace70bfb03bcd00f5972392ef", "guid": "bfdfe7dc352907fc980b868725387e986a66e1f233eb020c3e2537af407537a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865c9e3a74af97571e706ed3de3043b81", "guid": "bfdfe7dc352907fc980b868725387e98f56ea10c63931c041a00ab29c2c3450f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c19757328d201e833e94f4ff26e8394", "guid": "bfdfe7dc352907fc980b868725387e98ad840f5b2fd88911afe0d661c6e1b4cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980359267bcf934b75e57e028ffc917934", "guid": "bfdfe7dc352907fc980b868725387e986d84b64801096711675a8d6aae1950c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980aef1f1da8f43b7a83e1e05a2acff652", "guid": "bfdfe7dc352907fc980b868725387e986fab623bd43172ee701fa07f3406f6b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887db9fc9c69a81e3f6c298b0dfd73c2f", "guid": "bfdfe7dc352907fc980b868725387e98f673114ef87cef5f91b46f3fe63de05c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864dd4dc6eeaa1fbdec057ba22eaf64d8", "guid": "bfdfe7dc352907fc980b868725387e98e7fb55afadffd1e76bf55587f83953a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887332d914c303131f6054157b10202cb", "guid": "bfdfe7dc352907fc980b868725387e98c3a9e5a7f6e6159ae1a74e4940a436f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5faf333cd03a0ca117f9077e0c24ef1", "guid": "bfdfe7dc352907fc980b868725387e98222b5a562071deb2c7d328a51a6014c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892318f9c765963574a9704eafc817784", "guid": "bfdfe7dc352907fc980b868725387e984a495c987bbcffd2ae0b0f4f5e94a9a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986496cf269c1b3720c84a28af4f99c7a0", "guid": "bfdfe7dc352907fc980b868725387e988b219c2cc7c774eda812a86f40dc7da9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c2eb46d0ea03d45336e947450c7a7b7", "guid": "bfdfe7dc352907fc980b868725387e98274add86ba9d20b2f34686ddc6020612"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed1432b1784a286491c6a2471219ba1f", "guid": "bfdfe7dc352907fc980b868725387e986a4b5492d165681d4a2ee8d914639e10"}], "guid": "bfdfe7dc352907fc980b868725387e98db15b5bc5bb1cdbc0ca1b3da50133ed0", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988338730910fc8b3bf8d9a66916e6c151", "guid": "bfdfe7dc352907fc980b868725387e98ad94d49b9e211acac8e5cd2fbbef71fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832da6cef011dc8f1916c05159e037b65", "guid": "bfdfe7dc352907fc980b868725387e98bb0a2d5664410cd3502b61a5f4e47a63"}], "guid": "bfdfe7dc352907fc980b868725387e9871294775f37065f0979a422d7e439887", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9873e95e3d366d9be7a80201c0b60e5666", "targetReference": "bfdfe7dc352907fc980b868725387e98e6c2666f7221fa58caaa0668238bc4de"}], "guid": "bfdfe7dc352907fc980b868725387e983687be1437f75a8c61b423d7122b8f6c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e6c2666f7221fa58caaa0668238bc4de", "name": "photo_manager-photo_manager_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98cd1e97b2c7c0c2a96b4035a4c62b427d", "name": "photo_manager", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9841b881a585d538cf3da17a18e8b8ed12", "name": "photo_manager.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug-YL", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-JS", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-JS2", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release-YL", "provisioningStyle": 1}], "type": "standard"}