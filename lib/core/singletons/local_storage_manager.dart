import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';

enum LocalStorageKey {
  loginInfo,
  userInfo,
  loggedInPlatformIds,
  fundPwdError,
}

class LocalStorageManager {
  static final LocalStorageManager _instance = LocalStorageManager._internal();
  factory LocalStorageManager() => _instance;
  LocalStorageManager._internal();

  SharedPreferences? _prefs;
  Future<SharedPreferences> get _lazyPrefs async {
    return _prefs ??= await SharedPreferences.getInstance();
  }

  Future<void> saveObject(LocalStorageKey key, Map<String, dynamic> json) async {
    final prefs = await _lazyPrefs;
    String jsonStr = jsonEncode(json);
    await prefs.setString(key.name, jsonStr);
  }

  Future<T?> getObject<T>(LocalStorageKey key, T Function(Map<String, dynamic>) fromJson) async {
    final prefs = await _lazyPrefs;
    String? jsonStr = prefs.getString(key.name);
    if (jsonStr == null) return null;
    Map<String, dynamic> map = jsonDecode(jsonStr);
    return fromJson(map);
  }

  Future<void> saveStringList(LocalStorageKey key, List<String> value) async {
    final prefs = await _lazyPrefs;
    await prefs.setStringList(key.name, value);
  }

  Future<List<String>> getStringList(LocalStorageKey key) async {
    final prefs = await _lazyPrefs;
    return prefs.getStringList(key.name) ?? [];
  }

  Future<void> remove(LocalStorageKey key) async {
    final prefs = await _lazyPrefs;
    await prefs.remove(key.name);
  }

  destroyAllStorage() async {
    final prefs = await _lazyPrefs;
    for (final key in LocalStorageKey.values) {
      await prefs.remove(key.name);
    }
  }
}
