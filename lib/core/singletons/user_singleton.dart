// import 'dart:async';
// import 'dart:convert';
//
// import 'package:wd/core/models/apis/notification.dart';
// import 'package:wd/core/models/apis/user.dart';
// import 'package:wd/core/models/entities/login_entity.dart';
// import 'package:wd/core/models/entities/tc_sdk_config_entity.dart';
// import 'package:wd/core/models/entities/user_info_entity.dart';
// import 'package:wd/core/models/entities/user_vip_entity.dart';
// import 'package:wd/core/utils/global_config.dart';
// import 'package:wd/core/utils/log_util.dart';
// import 'package:wd/features/page/3_chat/chat_cubit.dart';
// import 'package:wd/features/page/4_mine/notifications/notification_cubit.dart';
// import 'package:wd/features/page/main/screens/main_screen_cubit.dart';
// import 'package:wd/injection_container.dart';
// import 'package:rxdart/rxdart.dart';
// import 'package:shared_preferences/shared_preferences.dart';
//
// // 用户单例
// class UserSingleton {
//   /// 远程推送token
//   String? remotePushDeviceToken;
//
//   List<int> loggedInPlatformIds = [];
//
//   static final UserSingleton _instance = UserSingleton._internal();
//   LoginTokenUser? _loginInfo;
//   UserInfoEntity? _userInfo;
//   UserBalanceEntity? _balanceInfo;
//   UserVipEntity? _vipInfo;
//   TCSDKConfigEntity? tencentConfig;
//   final BehaviorSubject<UserInfoEntity?> _userController = BehaviorSubject<UserInfoEntity?>();
//   final BehaviorSubject<bool> _loginController = BehaviorSubject<bool>();
//   final BehaviorSubject<UserBalanceEntity?> _balanceController = BehaviorSubject<UserBalanceEntity?>();
//   final BehaviorSubject<UserVipEntity?> _vipController = BehaviorSubject<UserVipEntity?>();
//
//   // Stream<UserInfoEntity?> get userStream => _userController.stream;
//
//
//   // Stream<UserBalanceEntity?> get balanceStream => _balanceController.stream;
//   //
//   // Stream<UserVipEntity?> get vipStream => _vipController.stream;
//   //
//   // Stream<Map<String, dynamic>> get combinedStream => Rx.combineLatest4(
//   //       _loginController.stream,
//   //       _userController.stream,
//   //       _balanceController.stream,
//   //       _vipController.stream,
//   //       (bool isLoggedIn, UserInfoEntity? userInfo, UserBalanceEntity? balanceInfo, UserVipEntity? vipInfo) {
//   //         return {
//   //           'isLoggedIn': isLoggedIn,
//   //           'userInfo': userInfo,
//   //           'balanceInfo': balanceInfo,
//   //           'vipInfo': vipInfo,
//   //         };
//   //       },
//   //     );
//
//   // 构造方法
//   factory UserSingleton() {
//     return _instance;
//   }
//
//   UserSingleton._internal() {
//     fetchVideoWatchLimit();
//   }
//
//   bool get isLogin => loginInfo != null;
//
//   LoginTokenUser? get loginInfo => _loginInfo;
//
//   String? _videoWatchLimit;
//   String get videoWatchLimit => _videoWatchLimit ?? "3,5,1,3"; // 短视频未登录观看条数，短视频已登录观看条数，长视频未登录观看分钟，长视频登录观看分钟
//
//   int get shortVideoCountLimit {
//     final videoWatchLimitList = videoWatchLimit.split(',').map((e) => int.tryParse(e) ?? 3).toList();
//     return isLogin ? videoWatchLimitList[1] : videoWatchLimitList[0];
//   }
//
//   int get filmMinutesLimit {
//     final videoWatchLimitList = videoWatchLimit.split(',').map((e) => int.tryParse(e) ?? 1).toList();
//     return isLogin ? videoWatchLimitList[3] : videoWatchLimitList[2];
//   }
//
//   // set loginInfo(LoginTokenUser? user) {
//   //   _loginInfo = user;
//   //
//   //   fetchVideoWatchLimit();
//   //   if (user != null) {
//   //     uploadRemotePushToken();
//   //     _saveLoginInfo(user);
//   //     _loginController.add(true);
//   //     fetchUserInfo();
//   //     fetchUserBalance();
//   //     fetchUserVip();
//   //     if (sl.isRegistered<NotificationsCubit>()) {
//   //       sl<NotificationsCubit>().fetchNotifications(updateCount: true, reset: true);
//   //     }
//   //   } else {
//   //     _clearUser();
//   //     _loginController.add(false);
//   //   }
//   // }
//
//   uploadRemotePushToken() {
//     if (remotePushDeviceToken != null) {
//       NotificationApi.uploadDeviceToken(remotePushDeviceToken!);
//     }
//   }
//
//   // UserInfoEntity? get userInfo => _userInfo;
//
//   set userInfo(UserInfoEntity? user) {
//     _userInfo = user;
//     if (user != null) {
//       _saveUserInfo(user);
//       _userController.add(user);
//     } else {
//       _clearUser();
//       _userController.add(null);
//     }
//   }
//
//   // UserBalanceEntity? get balanceInfo => _balanceInfo;
//
//   set balanceInfo(UserBalanceEntity? balanceInfo) {
//     _balanceInfo = balanceInfo;
//     if (balanceInfo != null) {
//       _balanceController.add(balanceInfo);
//     } else {
//       _balanceController.add(null);
//     }
//   }
//
//   UserVipEntity? get vipInfo => _vipInfo;
//
//   set vipInfo(UserVipEntity? vipInfo) {
//     _vipInfo = vipInfo;
//     if (vipInfo != null) {
//       _vipController.add(vipInfo);
//     } else {
//       _vipController.add(null);
//     }
//   }
//
//   void addPlatformId(int platformId) async {
//     if (!loggedInPlatformIds.contains(platformId)) {
//       loggedInPlatformIds.add(platformId);
//       await _saveLoggedInPlatformIds();
//     }
//   }
//
//   void removePlatformId(int platformId) async {
//     if (loggedInPlatformIds.contains(platformId)) {
//       loggedInPlatformIds.remove(platformId);
//       await _saveLoggedInPlatformIds();
//     }
//   }
//
//   fetchUserInfo() async {
//     if (!isLogin) return;
//     final result = await UserApi.fetchUserInfo();
//     if (result != null) userInfo = result;
//   }
//
//   fetchUserBalance() async {
//     final result = await UserApi.fetchBalanceInfo();
//     if (result != null) balanceInfo = result;
//   }
//
//   fetchUserVip() async {
//     if (!isLogin) return;
//     final result = await UserApi.fetchVipInfo();
//     if (result != null) vipInfo = result;
//   }
//
//   fetchVideoWatchLimit() async {
//     var res = await GlobalConfig().getConfigValueByKey("video_watch_limit");
//     if (res != null) {
//       _videoWatchLimit = res;
//     }
//   }
//
//   fetchTencentConfig() async {
//     final sdkConfig = await UserApi.fetchTencentUserSig();
//     if (sdkConfig != null) {
//       tencentConfig = sdkConfig;
//       sl<ChatCubit>().initChats(
//           int.parse(tencentConfig!.sdkAppId), tencentConfig!.userId, tencentConfig!.userSig, loginInfo?.token ?? "");
//     }
//   }
//
//   Future<void> loadUser() async {
//     // key-value 本地数据库
//     SharedPreferences prefs = await SharedPreferences.getInstance();
//
//     List<String> stringIds = prefs.getStringList('loggedInPlatformIds') ?? [];
//     loggedInPlatformIds = stringIds.map((id) => int.parse(id)).toList();
//
//     String? userJson = prefs.getString('userInfo');
//     if (userJson != null) {
//       Map<String, dynamic> userMap = json.decode(userJson);
//       _userInfo = UserInfoEntity.fromJson(userMap);
//       _userController.add(_userInfo);
//       LogD("Nickname: ${_userInfo?.nickName}");
//     }
//
//     String? loginJson = prefs.getString('loginInfo');
//     if (loginJson != null) {
//       Map<String, dynamic> loginMap = json.decode(loginJson);
//       // loginInfo = LoginTokenUser.fromJson(loginMap);
//       LogD("当前用户token: ${_loginInfo?.token}");
//     }
//
//     String? vipJson = prefs.getString('vipInfo');
//     if (vipJson != null) {
//       Map<String, dynamic> vipMap = json.decode(vipJson);
//       vipInfo = UserVipEntity.fromJson(vipMap);
//       _vipController.add(vipInfo);
//     }
//   }
//
//   Future<void> _saveLoginInfo(LoginTokenUser user) async {
//     SharedPreferences prefs = await SharedPreferences.getInstance();
//     String userJson = json.encode(user.toJson());
//     await prefs.setString('loginInfo', userJson);
//     _loginInfo = user;
//   }
//
//   Future<void> _saveUserInfo(UserInfoEntity user) async {
//     SharedPreferences prefs = await SharedPreferences.getInstance();
//     String userJson = json.encode(user.toJson());
//     await prefs.setString('userInfo', userJson);
//     _userInfo = user;
//   }
//
//   Future<void> _saveVipInfo(UserVipEntity vipInfo) async {
//     SharedPreferences prefs = await SharedPreferences.getInstance();
//     String vipJson = json.encode(vipInfo.toJson());
//     await prefs.setString('vipInfo', vipJson);
//     _vipInfo = vipInfo;
//   }
//
//   Future<void> _saveLoggedInPlatformIds() async {
//     SharedPreferences prefs = await SharedPreferences.getInstance();
//     List<String> stringIds = loggedInPlatformIds.map((id) => id.toString()).toList();
//     await prefs.setStringList('loggedInPlatformIds', stringIds);
//   }
//
//   Future<void> _clearUser() async {
//     // TencentImUtil.resetSDK();
//     SharedPreferences prefs = await SharedPreferences.getInstance();
//     await prefs.remove('loginInfo');
//     await prefs.remove('userInfo');
//     await prefs.remove('vipInfo');
//     _loginInfo = null;
//     _userInfo = null;
//     _balanceInfo = null;
//     _vipInfo = null;
//     _userController.add(null);
//     _loginController.add(false);
//     _balanceController.add(null);
//     _vipController.add(null);
//     loggedInPlatformIds = [];
//     if (sl.isRegistered<NotificationsCubit>()) {
//       sl<NotificationsCubit>().resetState();
//     }
//     sl<MainScreenCubit>().startSignupBonusTimer();
//   }
// }
