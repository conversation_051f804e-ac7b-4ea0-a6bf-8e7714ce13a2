import 'package:flutter/material.dart';
import 'package:wd/core/utils/font_size.dart';

@immutable
class AppTextTheme extends ThemeExtension<AppTextTheme> {
  final TextStyle amountPrimary;      // 主金额
  final TextStyle amountSecondary;    // 次级金额
  final TextStyle amountTertiary;     // 更次级金额
  final TextStyle amountOnDark;       // 深色背景上的金额
  final TextStyle amountBrand;        // 品牌色金额
  final TextStyle amountSuccess;      // 成功含义金额,如收益、到账等
  final TextStyle amountError;        // 错误、警告含义金额

  const AppTextTheme({
    required this.amountPrimary,
    required this.amountSecondary,
    required this.amountTertiary,
    required this.amountOnDark,
    required this.amountBrand,
    required this.amountSuccess,
    required this.amountError,
  });

  @override
  AppTextTheme copyWith({
    TextStyle? amountPrimary,
    TextStyle? amountSecondary,
    TextStyle? amountTertiary,
    TextStyle? amountOnDark,
    TextStyle? amountBrand,
    TextStyle? amountSuccess,
    TextStyle? amountError,
  }) {
    return AppTextTheme(
      amountPrimary: amountPrimary ?? this.amountPrimary,
      amountSecondary: amountSecondary ?? this.amountSecondary,
      amountTertiary: amountTertiary ?? this.amountTertiary,
      amountOnDark: amountOnDark ?? this.amountOnDark,
      amountBrand: amountBrand ?? this.amountBrand,
      amountSuccess: amountSuccess ?? this.amountSuccess,
      amountError: amountError ?? this.amountError,
    );
  }

  // @override
  @override
  AppTextTheme lerp(ThemeExtension<AppTextTheme>? other, double t) {
    if (other is! AppTextTheme) return this;
    return AppTextTheme(
      amountPrimary: TextStyle.lerp(amountPrimary, other.amountPrimary, t)!,
      amountSecondary: TextStyle.lerp(amountSecondary, other.amountSecondary, t)!,
      amountTertiary: TextStyle.lerp(amountTertiary, other.amountTertiary, t)!,
      amountOnDark: TextStyle.lerp(amountOnDark, other.amountOnDark, t)!,
      amountBrand: TextStyle.lerp(amountBrand, other.amountBrand, t)!,
      amountSuccess: TextStyle.lerp(amountSuccess, other.amountSuccess, t)!,
      amountError: TextStyle.lerp(amountError, other.amountError, t)!,
    );
  }
}

// ===================== Flavor 实现 =====================
/// amountPrimary     主金额
/// amountSecondary   次级金额
/// amountTertiary    更次级金额
/// amountOnDark      深色背景上的金额
/// amountBrand       品牌色金额
/// amountSuccess     成功含义金额,如收益、到账等
/// amountError       错误、警告含义金额

final appTextThemeLight = AppTextTheme(
  amountPrimary: TextStyle(fontFamily: 'DINCond-Bold', fontSize: 28.fs, color: Colors.white), // 白色
  amountSecondary: const TextStyle(fontFamily: 'DINCond-Bold', fontSize: 20, color: Color(0xff3B416B)), // 深偏蓝
  amountTertiary: const TextStyle(fontFamily: 'DINCond-Bold', fontSize: 18, color: Colors.black38),
  amountOnDark: const TextStyle(fontFamily: 'DINCond-Bold', fontSize: 22, color: Color(0xff4F536E)), // 偏蓝
  amountBrand: const TextStyle(fontFamily: 'DINCond-Bold', fontSize: 22, color: Color(0xFFB39572)), // 金色
  amountSuccess: const TextStyle(fontFamily: 'DINCond-Bold', fontSize: 22, color: Colors.green),
  amountError: const TextStyle(fontFamily: 'DINCond-Bold', fontSize: 22, color: Colors.red),
);

/// Not configured
final appTextThemeDark = AppTextTheme(
  amountPrimary: TextStyle(fontFamily: 'DINCond-Bold', fontSize: 28.fs, color: Colors.black),
  amountSecondary: const TextStyle(fontFamily: 'DINCond-Bold', fontSize: 20, color: Colors.black54),
  amountTertiary: const TextStyle(fontFamily: 'DINCond-Bold', fontSize: 18, color: Colors.black38),
  amountOnDark: const TextStyle(fontFamily: 'DINCond-Bold', fontSize: 22, color: Colors.white),
  amountBrand: const TextStyle(fontFamily: 'DINCond-Bold', fontSize: 22, color: Color(0xFF00C853)), // 绿色
  amountSuccess: const TextStyle(fontFamily: 'DINCond-Bold', fontSize: 22, color: Color(0xFF00C853)),
  amountError: const TextStyle(fontFamily: 'DINCond-Bold', fontSize: 22, color: Colors.red),
);

// ===================== 使用扩展 ===================== //

extension AppTextThemeGetter on BuildContext {
  AppTextTheme get appText => Theme.of(this).extension<AppTextTheme>()!;
}
