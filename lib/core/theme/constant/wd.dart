import 'package:flutter/material.dart';

import '../custom_text_theme.dart';
import '../custom_theme_color.dart';

class CustomColorThemeConstantWD {
  /// 亮色主题配色 Light mode color palette
  static const CustomColorTheme lightDefault = CustomColorTheme(
    textPrimary: Color(0xFF4062D8),
    textTitle: Color(0xFF525A79),
    textRegular: Color(0xFF4E5B83),
    textSecondary: Color(0xFFFFFFFF),
    textHighlight: Color(0xFF4366DE),
    tabActive: Colors.black,
    tabInactive: Color(0xFFBDBDBD),
    btnBgPrimary: Color(0xFFFFD038),
    btnBgSecondary: Color(0xFF4F58A7),
    btnBgTertiary: Color(0xFF030303),
    btnTitlePrimary: Color(0xFF030303),
    btnTitleSecondary: Color(0xFFFFFFFF),
    btnTitleTertiary: Color(0xFFFFFFFF),
    btnBorderPrimary: Color(0xFFFFE258),
    btnBorderSecondary: Color(0xFF606AC1),
    btnBorderTertiary: Color(0xFF030303),
    foregroundColor: Color(0xFF030303),
    borderA: Color(0xFFD9D9D9),
    borderB: Color(0xFFD9D9D9),
    borderC: Color(0xFFD9D9D9),
    borderD: Color(0xFFD9D9D9),
    borderE: Color(0xFFD9D9D9),
    tabItemBgA: Color(0xFFD9D9D9),
    tabItemBgB: Color(0xFFD9D9D9),
    iconBgA: Color(0xFFD9D9D9),
    highlightForeground: Color(0xFFD9D9D9),
  );

  /// 暗黑主题配色 Dark mode color palette
  static const CustomColorTheme dartDefault = CustomColorTheme(
    textPrimary: Color(0xFFFFD038),
    textTitle: Color(0xFFB4B3B3),
    textRegular: Color(0xFFF4F4F4),
    textSecondary: Color(0xFFFFFFFF),
    textHighlight: Color(0xFF636363),
    tabActive: Colors.white,
    tabInactive: Color(0xFF666666),
    btnBgPrimary: Color(0xFFFFD038),
    btnBgSecondary: Color(0xFF4F58A7),
    btnBgTertiary: Color(0xFF030303),
    btnTitlePrimary: Color(0xFF030303),
    btnTitleSecondary: Color(0xFFFFFFFF),
    btnTitleTertiary: Color(0xFFFFFFFF),
    btnBorderPrimary: Color(0xFFFFE258),
    btnBorderSecondary: Color(0xFF606AC1),
    btnBorderTertiary: Color(0xFF030303),
    foregroundColor: Color(0xFF212121),
    borderA: Color(0xFF212121),
    borderB: Color(0xFF2B2B2B),
    borderC: Color(0xFF2E2E2E),
    borderD: Color(0xFFD9D9D9),
    borderE: Color(0xFFC09405),
    tabItemBgA: Color(0xFF6F5502),
    tabItemBgB: Color(0xFF212121),
    iconBgA: Color(0xFF373737),
    highlightForeground: Color(0xFF161616),
  );

  /// ... 继续添加颜色主题
  /// static const CustomColorTheme lightOrange = CustomColorTheme(
//     textPrimary: Color(0xFF222222),
//     textRegular: Color(0xFF4E5B83),
//     textSecondary: Color(0xFFFFFFFF),
//     textHighlight: Color(0xFFC4A06C),
//     tabActive: Colors.black,
//     tabInactive: Color(0xFFBDBDBD),
//     buttonPrimary: Colors.white,
//     buttonSecondary: Color(0xFF000000),
//     stockRed: Color(0xFFC92C31),
//     stockGreen: Color(0xFF1CB570),
//     border: Color(0xFFD9D9D9),
//     pending: Color(0xFFF8BB18),
//   );
}

class ThemeConstantWD {
  ThemeConstantWD._();

  static ThemeConstantWD instance = ThemeConstantWD._();

  ThemeData get lightDefault => ThemeData.light().copyWith(
        // 主题色 / Primary theme color
        primaryColor: const Color(0xFF3150BD),

        // 主题色-浅色 / Light variant of primary color
        primaryColorLight: const Color(0xFF4366DE),

        // Scaffold的默认背景颜色 / Default background color of Scaffold
        scaffoldBackgroundColor: const Color(0xFFF8F8F8),

        // 用于提示文本或占位符文本的颜色 / Color for hint or placeholder text
        hintColor: const Color(0xFFAFB8CB),

        // 点击时的高亮效果 / Highlight color on tap
        splashColor: Colors.transparent,

        // 长按时的扩散效果 / Ripple color on long press
        highlightColor: Colors.transparent,

        // 用于未选中的复选框 / Color for unselected checkbox
        unselectedWidgetColor: const Color(0xffE9EDF3),

        // 禁用状态下部件的颜色 / Color for disabled components
        disabledColor: const Color(0xff9BA6B6),

        // 鼠标悬停时的颜色 / Hover color when mouse hovers
        hoverColor: const Color(0xffE7E7E7),

        // 阴影颜色 / Shadow color
        shadowColor: const Color(0x14354677),

        // 焦点状态下的颜色 / Focus color
        focusColor: const Color(0xffFFF2E8),

        // 用于画布背景颜色 / Canvas background color
        // canvasColor: Colors.transparent,

        // 分割线 / Divider line color
        dividerColor: const Color(0xFF0F0F0F).withAlpha(10),

        appBarTheme: const AppBarTheme(
          // AppBar背景色 / AppBar background color
          backgroundColor: Color(0xFFFFFFFF),

          titleTextStyle: TextStyle(
            color: Color(0xFF101010),
            fontWeight: FontWeight.w500,
            fontFamily: "AnekDevanagari",
          ),

          // AppBar图标颜色 / Icon color in AppBar
          iconTheme: IconThemeData(
            color: Color(0xFF4062D8),
          ),
        ),

        bottomNavigationBarTheme: const BottomNavigationBarThemeData(
          // BottomNavigation背景 / Background of bottom navigation
          backgroundColor: Color(0xFFFFFFFF),
          // 选中项颜色 / Selected item color
          selectedItemColor: Color(0xFF4366DE),
          // 未选中项颜色 / Unselected item color
          unselectedItemColor: Color(0xFFAFB8CB),
        ),

        tabBarTheme: TabBarTheme(
          // 取消水波纹 / Disable ripple
          splashFactory: NoSplash.splashFactory,
          // 点击阴影为透明 / Transparent overlay on tap
          overlayColor: WidgetStateProperty.all(Colors.transparent),
          // 分隔线颜色 / Divider color
          dividerColor: Colors.transparent,
        ),

        // 卡片背景色 / Card background color
        cardColor: const Color(0xFFFFFFFF),

        cardTheme: const CardTheme(
          // 卡片背景色 / Card background color
          color: Color(0xFFFFFFFF),
        ),

        // icon 背景色
        iconButtonTheme: IconButtonThemeData(
          style: ButtonStyle(
            backgroundColor: WidgetStateProperty.all(Colors.transparent),
            minimumSize: WidgetStateProperty.all(const Size(40, 40)),
            shape: WidgetStateProperty.all(const CircleBorder()),
          ),
        ),

        buttonTheme: const ButtonThemeData(
          // 点击时的高亮效果 / Highlight on tap
          splashColor: Colors.transparent,
          // 长按时的扩散效果 / Ripple on long press
          highlightColor: Colors.transparent,
        ),

        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ButtonStyle(
            // 去除 ElevatedButton 阴影 / Remove elevation
            elevation: WidgetStateProperty.all(0),
          ),
        ),

        inputDecorationTheme: const InputDecorationTheme(
          filled: true,

          // TextField 背景色 / Background color of input field
          fillColor: Color.fromARGB(255, 243, 248, 255),

          hintStyle: TextStyle(
            // 设置 hintText 的颜色 / Hint text color
            color: Color(0xFFAFB8CB),
          ),

          focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(
              color: Color(0xFF3150BD),
            ),
          ),
        ),
        dividerTheme: const DividerThemeData(
          color: Color(0xFF030303),
        ),
        extensions: <ThemeExtension<dynamic>>[
          CustomColorThemeConstantWD.lightDefault,
          CustomTextTheme.light,
        ],
      );

  ThemeData get darkDefault => ThemeData.dark().copyWith(
        // 主题色 / Primary theme color
        primaryColor: const Color(0xFFFFD038),

        // 主题色-浅色 / Light variant of primary color
        primaryColorLight: const Color(0xFFFFE258),

        // Scaffold的默认背景颜色 / Default background color of Scaffold
        scaffoldBackgroundColor: const Color(0xFF030303),

        // 用于提示文本或占位符文本的颜色 / Color for hint or placeholder text
        hintColor: const Color(0xFF6B6254),

        // 点击时的高亮效果 / Highlight color on tap
        splashColor: Colors.transparent,

        // 长按时的扩散效果 / Ripple color on long press
        highlightColor: Colors.transparent,

        // 用于未选中的复选框 / Color for unselected checkbox
        unselectedWidgetColor: const Color(0xffE9EDF3),

        // 禁用状态下部件的颜色 / Color for disabled components
        disabledColor: const Color(0xff9BA6B6),

        // 鼠标悬停时的颜色 / Hover color when mouse hovers
        hoverColor: const Color(0xffE7E7E7),

        // 阴影颜色 / Shadow color
        shadowColor: const Color(0x14354677),

        // 焦点状态下的颜色 / Focus color
        focusColor: const Color(0xffFFF2E8),

        // 用于画布背景颜色 / Canvas background color
        // canvasColor: Colors.transparent,

        // 分割线 / Divider line color
        dividerColor: const Color(0xFF212121),

        appBarTheme: const AppBarTheme(
          // AppBar背景色 / AppBar background color
          backgroundColor: Color(0xFF101010),

          titleTextStyle: TextStyle(
            color: Color(0xFFFFFFFF),
            fontWeight: FontWeight.w500,
            fontFamily: "AnekDevanagari",
          ),

          // AppBar图标颜色 / Icon color in AppBar
          iconTheme: IconThemeData(color: Color(0xFFFFFFFF)),
        ),

        bottomNavigationBarTheme: const BottomNavigationBarThemeData(
          // BottomNavigation背景 / Background of bottom navigation
          backgroundColor: Color(0xFF101010),
          // 选中项颜色 / Selected item color
          selectedItemColor: Color(0xFFFFD038),
          // 未选中项颜色 / Unselected item color
          unselectedItemColor: Color(0xFF9F9E9C),
        ),

        tabBarTheme: TabBarTheme(
          // 取消水波纹 / Disable ripple
          splashFactory: NoSplash.splashFactory,
          // 点击阴影为透明 / Transparent overlay on tap
          overlayColor: WidgetStateProperty.all(Colors.transparent),
          // 分隔线颜色 / Divider color
          dividerColor: Colors.transparent,
        ),

        // 卡片背景色 / Card background color
        cardColor: const Color(0xFF101010),

        cardTheme: const CardTheme(
          // 卡片背景色 / Card background color
          color: Color(0xFF1F1911),
        ),

        // icon 背景色
        iconButtonTheme: IconButtonThemeData(
          style: ButtonStyle(
            backgroundColor: WidgetStateProperty.all(Colors.transparent),
            minimumSize: WidgetStateProperty.all(const Size(40, 40)),
            shape: WidgetStateProperty.all(const CircleBorder()),
          ),
        ),

        buttonTheme: const ButtonThemeData(
          // 点击时的高亮效果 / Highlight on tap
          splashColor: Colors.transparent,
          // 长按时的扩散效果 / Ripple on long press
          highlightColor: Colors.transparent,
        ),

        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ButtonStyle(
            // 去除 ElevatedButton 阴影 / Remove elevation
            elevation: WidgetStateProperty.all(0),
          ),
        ),

        inputDecorationTheme: const InputDecorationTheme(
          filled: true,
          contentPadding: EdgeInsets.fromLTRB(16, 0, 16, 0),
          // TextField 背景色 / Background color of input field
          fillColor: Color(0xFF101010),
          hintStyle: TextStyle(
            // 设置 hintText 的颜色 / Hint text color
            color: Color(0xFF636363),
            fontFamily: "AnekDevanagari",
          ),

          focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(
              color: Color(0xFF212121),
              width: 1,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderSide: BorderSide(
              color: Color(0xFF212121),
              width: 0.5,
            ),
          ),
        ),
        dividerTheme: const DividerThemeData(
          color: Color(0xFF030303),
        ),
        extensions: <ThemeExtension<dynamic>>[
          CustomColorThemeConstantWD.dartDefault,
          CustomTextTheme.dart,
        ],
      );
}
