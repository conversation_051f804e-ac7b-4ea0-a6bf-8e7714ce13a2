import 'package:flutter/material.dart';
import 'package:wd/core/theme/constant/wd.dart';
import 'custom_text_theme.dart';
import 'custom_theme_color.dart';

export 'custom_text_theme.dart';
export 'custom_theme_color.dart';

/// 应用皮肤风格，
/// TemplateA ~ TemplateD 是临时命名，用于占位不同的皮肤风格。
/// 模板会根据首个使用它的具体项目或品牌进行重命名。
///
/// App skin styles
/// TemplateA to TemplateD are temporary placeholders for skin styles.
///  Will be renamed based on the first project or brand that adopts them.
enum AppSkinStyle {
  kTemplateA,
  kTemplateB,
  kTemplateC,
  kTemplateD,
}

/// BuildContext 扩展：快速访问主题
/// Extension on BuildContext for quick access to Theme
extension CustomTextThemeExtension on BuildContext {
  /// 获取当前主题
  /// Get current ThemeData
  ThemeData get theme => Theme.of(this);

  /// 获取当前自定义文字主题
  /// Get current CustomTextTheme from ThemeExtensions
  CustomTextTheme get textTheme => Theme.of(this).extension<CustomTextTheme>()!;

  /// 获取当前自定义颜色主题
  /// Get current CustomColorTheme from ThemeExtensions
  CustomColorTheme get colorTheme => Theme.of(this).extension<CustomColorTheme>()!;
}

/// app主题相关，使用context.theme调用该主题
class AppTheme {
  AppTheme._();

  static final AppTheme instance = AppTheme._();

  /// 先根据皮肤样式，再选择主题颜色
  ThemeData getThemeLight() {
    return ThemeConstantWD.instance.lightDefault;
  }

  ThemeData getThemeDark() {
    return ThemeConstantWD.instance.darkDefault;
  }
}
