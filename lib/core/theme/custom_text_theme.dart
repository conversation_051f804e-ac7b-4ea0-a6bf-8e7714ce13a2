import 'package:flutter/material.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';

import 'custom_theme_color.dart';

/// TextStyle 扩展：链式设置字体大小、字重、字体族
/// Extension on TextStyle for chaining font size, weight and family
extension CustomFontExtension on TextStyle {
  /* 字号扩展 Font size extensions (scaled with .fs) */
  TextStyle get fs8 => copyWith(fontSize: 8.gw);

  TextStyle get fs9 => copyWith(fontSize: 9.gw);

  TextStyle get fs10 => copyWith(fontSize: 10.gw);

  TextStyle get fs11 => copyWith(fontSize: 11.gw);

  TextStyle get fs12 => copyWith(fontSize: 12.gw);

  TextStyle get fs13 => copyWith(fontSize: 13.gw);

  TextStyle get fs15 => copyWith(fontSize: 15.gw);

  TextStyle get fs16 => copyWith(fontSize: 16.gw);

  TextStyle get fs17 => copyWith(fontSize: 17.gw);

  TextStyle get fs18 => copyWith(fontSize: 18.gw);

  TextStyle get fs19 => copyWith(fontSize: 19.gw);

  TextStyle get fs20 => copyWith(fontSize: 20.gw);

  TextStyle get fs21 => copyWith(fontSize: 21.gw);

  TextStyle get fs22 => copyWith(fontSize: 22.gw);

  TextStyle get fs23 => copyWith(fontSize: 23.gw);

  TextStyle get fs24 => copyWith(fontSize: 24.gw);

  TextStyle get fs25 => copyWith(fontSize: 25.gw);

  TextStyle get fs26 => copyWith(fontSize: 26.gw);

  TextStyle get fs27 => copyWith(fontSize: 27.gw);

  TextStyle get fs28 => copyWith(fontSize: 28.gw);

  TextStyle get fs29 => copyWith(fontSize: 29.gw);

  TextStyle get fs30 => copyWith(fontSize: 30.gw);

  TextStyle get fs31 => copyWith(fontSize: 31.gw);

  TextStyle get fs32 => copyWith(fontSize: 32.gw);

  /* 字重扩展 Font weight extensions */
  TextStyle get w300 => copyWith(fontWeight: FontWeight.w300);

  TextStyle get w500 => copyWith(fontWeight: FontWeight.w500);

  TextStyle get w600 => copyWith(fontWeight: FontWeight.w600);

  TextStyle get w700 => copyWith(fontWeight: FontWeight.w700);

  TextStyle get w800 => copyWith(fontWeight: FontWeight.w800);

  /* 颜色透明度扩展 Font color opacity extensions */
  TextStyle get opa10 => copyWith(color: color?.withOpacity(0.1));

  TextStyle get opa20 => copyWith(color: color?.withOpacity(0.2));

  TextStyle get opa30 => copyWith(color: color?.withOpacity(0.3));

  TextStyle get opa40 => copyWith(color: color?.withOpacity(0.4));

  TextStyle get opa50 => copyWith(color: color?.withOpacity(0.5));

  TextStyle get opa60 => copyWith(color: color?.withOpacity(0.6));

  TextStyle get opa70 => copyWith(color: color?.withOpacity(0.7));

  TextStyle get opa80 => copyWith(color: color?.withOpacity(0.8));

  TextStyle get opa90 => copyWith(color: color?.withOpacity(0.9));

  /* 字体族扩展 Font family extensions */
  // TextStyle get ffAnek => copyWith();
  TextStyle get ffAne => copyWith(fontFamily: 'AnekDevanagari');
}

/// 自定义文字主题，用于统一管理多套风格和颜色
/// Custom text theme to manage font styles & colors across themes
///
/// 可通过 [context.colorTheme] 获取当前主题颜色集
/// Access via [context.colorTheme] in your widgets
class CustomTextTheme extends ThemeExtension<CustomTextTheme> {
  final TextStyle primary; // 主要文字 Main text
  final TextStyle title; // 标题文字 Title text
  final TextStyle regular; // 常规文字 Regular text
  final TextStyle secondary; // 次级文字 Secondary text
  final TextStyle highlight; // 高亮文字 Highlight text
  final TextStyle active; // 功能激活文字 Active state text
  final TextStyle btnPrimary; // 按钮基础颜色
  final TextStyle btnSecondary; // 按钮次级颜色
  final TextStyle btnTertiary; // 按钮更次级颜色

  const CustomTextTheme({
    required this.primary,
    required this.title,
    required this.regular,
    required this.secondary,
    required this.highlight,
    required this.active,
    required this.btnPrimary,
    required this.btnSecondary,
    required this.btnTertiary,
  });

  /// 工厂构建函数：根据 ThemeColor 构建 TextStyle
  factory CustomTextTheme.fromColor(CustomColorTheme colorSet) {
    return CustomTextTheme(
      primary: TextStyle(fontSize: 14.gw, color: colorSet.textPrimary),
      title: TextStyle(fontSize: 14.gw, color: colorSet.textTitle),
      regular: TextStyle(fontSize: 14.gw, color: colorSet.textRegular),
      secondary: TextStyle(fontSize: 14.gw, color: colorSet.textSecondary),
      highlight: TextStyle(fontSize: 14.gw, color: colorSet.textHighlight),
      active: TextStyle(fontSize: 14.gw, color: colorSet.tabActive),
      btnPrimary: TextStyle(fontSize: 14.gw, color: colorSet.btnTitlePrimary),
      btnSecondary: TextStyle(fontSize: 14.gw, color: colorSet.btnTitleSecondary),
      btnTertiary: TextStyle(fontSize: 14.gw, color: colorSet.btnTitleTertiary),
    );
  }

  // FIXME
  static CustomTextTheme light = CustomTextTheme.fromColor(CustomColorThemeHelper.getThemeLight());

  // FIXME
  static CustomTextTheme dart = CustomTextTheme.fromColor(CustomColorThemeHelper.getThemeDark());

  /// 拷贝样式 Copy current theme with overrides
  @override
  CustomTextTheme copyWith({
    TextStyle? primary,
    TextStyle? title,
    TextStyle? regular,
    TextStyle? secondary,
    TextStyle? highlight,
    TextStyle? active,
    TextStyle? button,
    TextStyle? stockRed,
    TextStyle? stockGreen,
    TextStyle? pending,
    TextStyle? btnPrimary,
    TextStyle? btnSecondary,
    TextStyle? btnTertiary,
  }) {
    return CustomTextTheme(
      primary: primary ?? this.primary,
      title: title ?? this.title,
      regular: regular ?? this.regular,
      secondary: secondary ?? this.secondary,
      highlight: highlight ?? this.highlight,
      active: active ?? this.active,
      btnPrimary: btnPrimary ?? this.btnPrimary,
      btnSecondary: btnSecondary ?? this.btnSecondary,
      btnTertiary: btnTertiary ?? this.btnTertiary,
    );
  }

  /// 主题动画渐变插值，用于主题切换动画
  /// Interpolation for theme switching animation
  @override
  CustomTextTheme lerp(ThemeExtension<CustomTextTheme>? other, double t) {
    if (other is! CustomTextTheme) return this;
    return CustomTextTheme(
      primary: TextStyle.lerp(primary, other.primary, t)!,
      title: TextStyle.lerp(title, other.title, t)!,
      regular: TextStyle.lerp(regular, other.regular, t)!,
      secondary: TextStyle.lerp(secondary, other.secondary, t)!,
      highlight: TextStyle.lerp(highlight, other.highlight, t)!,
      active: TextStyle.lerp(active, other.active, t)!,
      btnPrimary: TextStyle.lerp(btnPrimary, other.btnPrimary, t)!,
      btnSecondary: TextStyle.lerp(btnSecondary, other.btnSecondary, t)!,
      btnTertiary: TextStyle.lerp(btnTertiary, other.btnTertiary, t)!,
    );
  }
}
