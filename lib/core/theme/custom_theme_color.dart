import 'package:flutter/material.dart';

import 'constant/wd.dart';


class CustomColorThemeHelper {
  static CustomColorTheme getThemeLight() {
    return CustomColorThemeConstantWD.lightDefault;
  }

  static CustomColorTheme getThemeDark() {
    return CustomColorThemeConstantWD.dartDefault;
  }
}

/// 自定义颜色主题，用于配合 [CustomTextTheme] 构建统一配色方案
/// Custom color theme used to define unified colors across the app
///
/// 可通过 [context.colorTheme] 获取当前主题颜色集
/// Access via [context.colorTheme] in your widgets
class CustomColorTheme extends ThemeExtension<CustomColorTheme> {
  final Color textPrimary; // 主要文字 Main text
  final Color textTitle; // 标题文字 Title text
  final Color textRegular; // 常规文字 Regular text
  final Color textSecondary; // 次级文字 Secondary text
  final Color textHighlight; // 高亮文字 Highlight text
  final Color tabActive; // 激活标签页文字 Tab active color
  final Color tabInactive; // 非激活标签页文字 Tab inactive color
  final Color btnBgPrimary; // 按钮背景主色 Button primary background color
  final Color btnBgSecondary; // 次按钮背景颜色 Secondary button background color
  final Color btnBgTertiary; // 更次按钮背景颜色 Tertiary button background color
  final Color btnTitlePrimary; // 按钮标题主色 Button primary title color
  final Color btnTitleSecondary; // 次按钮标题颜色 Secondary button title color
  final Color btnTitleTertiary; // 更次按钮标题颜色 Tertiary button title color
  final Color btnBorderPrimary; // 按钮边框主色 Button border primary color
  final Color btnBorderSecondary; // 次按钮边框颜色 Secondary border button color
  final Color btnBorderTertiary; // 更次按边框钮颜色 Tertiary border button color
  final Color foregroundColor; // 前景色 Foreground color
  final Color borderA; // 边框颜色 Border color
  final Color borderB; // 边框颜色 Border color
  final Color borderC; // 边框颜色 Border color
  final Color borderD; // 边框颜色 Border color
  final Color borderE; // 边框颜色 Border color
  final Color tabItemBgA; // TabItemBgA Border color
  final Color tabItemBgB; // TabItemBgA Border color
  final Color iconBgA; // Icon Background color
  final Color highlightForeground; // highlightForeground color

  const CustomColorTheme({
    required this.textPrimary,
    required this.textTitle,
    required this.textRegular,
    required this.textSecondary,
    required this.textHighlight,
    required this.tabActive,
    required this.tabInactive,
    required this.btnBgPrimary,
    required this.btnBgSecondary,
    required this.btnBgTertiary,
    required this.btnTitlePrimary,
    required this.btnTitleSecondary,
    required this.btnTitleTertiary,
    required this.btnBorderPrimary,
    required this.btnBorderSecondary,
    required this.btnBorderTertiary,
    required this.foregroundColor,
    required this.borderA,
    required this.borderB,
    required this.borderC,
    required this.borderD,
    required this.borderE,
    required this.tabItemBgA,
    required this.tabItemBgB,
    required this.iconBgA,
    required this.highlightForeground,
  });

  /// 拷贝当前配色，可用于动态生成主题
  @override
  CustomColorTheme copyWith({
    Color? textPrimary,
    Color? textTitle,
    Color? textRegular,
    Color? textSecondary,
    Color? textHighlight,
    Color? tabActive,
    Color? tabInactive,
    Color? btnBgPrimary,
    Color? btnBgSecondary,
    Color? btnBgTertiary,
    Color? btnTitlePrimary,
    Color? btnTitleSecondary,
    Color? btnTitleTertiary,
    Color? btnBorderPrimary,
    Color? btnBorderSecondary,
    Color? btnBorderTertiary,
    Color? foregroundColor,
    Color? borderA,
    Color? borderB,
    Color? borderC,
    Color? borderD,
    Color? borderE,
    Color? tabItemBgA,
    Color? tabItemBgB,
    Color? iconBgA,
    Color? highlightForeground,
  }) {
    return CustomColorTheme(
      textPrimary: textPrimary ?? this.textPrimary,
      textTitle: textTitle ?? this.textTitle,
      textRegular: textRegular ?? this.textRegular,
      textSecondary: textSecondary ?? this.textSecondary,
      textHighlight: textHighlight ?? this.textHighlight,
      tabActive: tabActive ?? this.tabActive,
      tabInactive: tabInactive ?? this.tabInactive,
      btnBgPrimary: btnBgPrimary ?? this.btnBgPrimary,
      btnBgSecondary: btnBgSecondary ?? this.btnBgSecondary,
      btnBgTertiary: btnBgTertiary ?? this.btnBgTertiary,
      btnTitlePrimary: btnTitlePrimary ?? this.btnTitlePrimary,
      btnTitleSecondary: btnTitleSecondary ?? this.btnTitleSecondary,
      btnTitleTertiary: btnTitleTertiary ?? this.btnTitleTertiary,
      btnBorderPrimary: btnBorderPrimary ?? this.btnBorderPrimary,
      btnBorderSecondary: btnBorderSecondary ?? this.btnBorderSecondary,
      btnBorderTertiary: btnBorderTertiary ?? this.btnBorderTertiary,
      foregroundColor: foregroundColor ?? this.foregroundColor,
      borderA: borderA ?? this.borderA,
      borderB: borderB ?? this.borderB,
      borderC: borderC ?? this.borderC,
      borderD: borderD ?? this.borderD,
      borderE: borderE ?? this.borderE,
      tabItemBgA: tabItemBgA ?? this.tabItemBgA,
      tabItemBgB: tabItemBgB ?? this.tabItemBgB,
      iconBgA: iconBgA ?? this.iconBgA,
      highlightForeground: highlightForeground ?? this.highlightForeground,
    );
  }

  /// 用于主题切换动画渐变插值
  @override
  CustomColorTheme lerp(ThemeExtension<CustomColorTheme>? other, double t) {
    if (other is! CustomColorTheme) return this;
    return CustomColorTheme(
      textPrimary: Color.lerp(textPrimary, other.textPrimary, t)!,
      textTitle: Color.lerp(textTitle, other.textTitle, t)!,
      textRegular: Color.lerp(textRegular, other.textRegular, t)!,
      textSecondary: Color.lerp(textSecondary, other.textSecondary, t)!,
      textHighlight: Color.lerp(textHighlight, other.textHighlight, t)!,
      tabActive: Color.lerp(tabActive, other.tabActive, t)!,
      tabInactive: Color.lerp(tabInactive, other.tabInactive, t)!,
      btnBgPrimary: Color.lerp(btnBgPrimary, other.btnBgPrimary, t)!,
      btnBgSecondary: Color.lerp(btnBgSecondary, other.btnBgSecondary, t)!,
      btnBgTertiary: Color.lerp(btnBgTertiary, other.btnBgTertiary, t)!,
      btnTitlePrimary: Color.lerp(btnTitlePrimary, other.btnTitlePrimary, t)!,
      btnTitleSecondary: Color.lerp(btnTitleSecondary, other.btnTitleSecondary, t)!,
      btnTitleTertiary: Color.lerp(btnTitleTertiary, other.btnTitleTertiary, t)!,
      btnBorderPrimary: Color.lerp(btnBorderPrimary, other.btnBorderPrimary, t)!,
      btnBorderSecondary: Color.lerp(btnBorderSecondary, other.btnBorderSecondary, t)!,
      btnBorderTertiary: Color.lerp(btnBorderTertiary, other.btnBorderTertiary, t)!,
      foregroundColor: Color.lerp(foregroundColor, other.foregroundColor, t)!,
      borderA: Color.lerp(borderA, other.borderA, t)!,
      borderB: Color.lerp(borderB, other.borderB, t)!,
      borderC: Color.lerp(borderC, other.borderC, t)!,
      borderD: Color.lerp(borderD, other.borderD, t)!,
      borderE: Color.lerp(borderE, other.borderE, t)!,
      tabItemBgA: Color.lerp(tabItemBgA, other.tabItemBgA, t)!,
      tabItemBgB: Color.lerp(tabItemBgB, other.tabItemBgB, t)!,
      iconBgA: Color.lerp(iconBgA, other.iconBgA, t)!,
      highlightForeground: Color.lerp(highlightForeground, other.highlightForeground, t)!,
    );
  }
}
