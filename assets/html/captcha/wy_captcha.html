<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>验证码</title>
  <script src="scripts/load.min.js?t=<MINUTE_TIMESTAMP>"></script>
  <style>
    body { margin: 0; padding: 0; }
  </style>
</head>
<body>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const MAX_RETRIES = 5;
      const RETRY_DELAY = 1000;
      let retryCount = 0;

      function initializeCaptcha() {
        const config = {
          captchaId: '<CAPTCHA_ID>',
          mode: 'popup',
          apiVersion: 2,
          popupStyles: {
            position: 'fixed',
            top: '20%'
          },
          onClose: () => {
            log('User closed captcha');
            sendMessage({ error: 'user_cancelled' });
          },
          onVerify: (err, data) => {
            log('onVerify called:', err, data);
            if (err) {
              log('Sending error:', err);
              return sendMessage({ error: err });
            }
            log('Sending success:', data);
            sendMessage({ validate: data.validate });
          }
        };
        try {
          initNECaptcha(config, 
            (instance) => {
              log('Captcha loaded successfully');
              instance.verify();
            },
            (err) => {
              log('Captcha initialization error:', err);
              sendMessage({ error: err });
            }
          );
        } catch (e) {
          log('Captcha initialization error:', e);
          sendMessage({ error: e });
        }
      }
      function sendMessage(data) {
        window.parent.postMessage({
          type: 'captcha_verify',
          ...data
        }, '*');
      }
      const isDebug = '<IS_DEBUG>';
      function log(message) {
        if (isDebug === 'true') {
          console.log(message);
        }
      }
      function tryInitCaptcha() {
        if (typeof initNECaptcha === 'undefined') {
          if (retryCount < MAX_RETRIES) {
            log(`Retrying captcha initialization... Attempt ${retryCount + 1} of ${MAX_RETRIES}`);
            const script = document.createElement('script');
            script.src = `scripts/load.min.js?t=${Date.now()}`;
            document.head.appendChild(script);
            retryCount++;
            setTimeout(tryInitCaptcha, RETRY_DELAY);
            return;
          }
          
          console.error('Failed to load captcha after maximum retries');
          sendMessage({ error: 'Failed to load captcha after maximum retries' });
          return;
        }
        initializeCaptcha();
      }
      log('Initializing captcha...');
      tryInitCaptcha();
    });
  </script>
</body>
</html> 