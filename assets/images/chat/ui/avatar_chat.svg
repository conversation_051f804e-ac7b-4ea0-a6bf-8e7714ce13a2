<svg width="61" height="61" viewBox="0 0 61 61" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_iiiii_318_2)">
<circle cx="30.5" cy="30.5" r="30.5" fill="#E2E2E2"/>
<circle cx="30.5" cy="30.5" r="30.5" fill="url(#paint0_radial_318_2)"/>
</g>
<path d="M30.5 15C26.3579 15 23 18.3621 23 22.5095C23 26.6569 26.3579 30.0191 30.5 30.0191C34.6421 30.0191 38 26.6569 38 22.5095C38 18.3621 34.6421 15 30.5 15Z" fill="url(#paint1_linear_318_2)"/>
<path d="M22.5 34.0241C18.3579 34.0241 15 37.3863 15 41.5337V43.9133C15 45.4216 16.0918 46.7077 17.5785 46.9508C26.1362 48.3497 34.8638 48.3497 43.4215 46.9508C44.9082 46.7077 46 45.4216 46 43.9133V41.5337C46 37.3863 42.6421 34.0241 38.5 34.0241H37.8183C37.4493 34.0241 37.0826 34.0826 36.7319 34.1972L35.0008 34.7632C32.0763 35.7194 28.9237 35.7194 25.9992 34.7632L24.2681 34.1972C23.9174 34.0826 23.5507 34.0241 23.1817 34.0241H22.5Z" fill="url(#paint2_linear_318_2)"/>
<defs>
<filter id="filter0_iiiii_318_2" x="-10" y="-12" width="77" height="82" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="3" dy="4"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.7 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_318_2"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-10" dy="-12"/>
<feGaussianBlur stdDeviation="9"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_318_2" result="effect2_innerShadow_318_2"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_318_2" result="effect3_innerShadow_318_2"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="6" dy="9"/>
<feGaussianBlur stdDeviation="4.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_318_2" result="effect4_innerShadow_318_2"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-3"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect4_innerShadow_318_2" result="effect5_innerShadow_318_2"/>
</filter>
<radialGradient id="paint0_radial_318_2" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(19.5 12) rotate(55.9679) scale(46.457 46.457)">
<stop stop-color="white"/>
<stop offset="0.697917" stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint1_linear_318_2" x1="30.5" y1="15" x2="30.5" y2="48" gradientUnits="userSpaceOnUse">
<stop/>
<stop offset="1" stop-color="#666666"/>
</linearGradient>
<linearGradient id="paint2_linear_318_2" x1="30.5" y1="15" x2="30.5" y2="48" gradientUnits="userSpaceOnUse">
<stop/>
<stop offset="1" stop-color="#666666"/>
</linearGradient>
</defs>
</svg>
