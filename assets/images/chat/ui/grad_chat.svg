<svg width="82" height="82" viewBox="0 0 82 82" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_2_5)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M41 48C37.134 48 34 44.866 34 41C34 37.134 37.134 34 41 34C44.866 34 48 37.134 48 41C48 42.2925 47.6497 43.5032 47.0389 44.5423C46.9627 44.6719 46.9426 44.8278 46.9901 44.9704L47.6838 47.0513C47.8141 47.4422 47.4422 47.8141 47.0513 47.6838L44.9704 46.9901C44.8278 46.9426 44.6719 46.9627 44.5423 47.0389C43.5032 47.6497 42.2925 48 41 48Z" fill="url(#paint0_linear_2_5)"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M49 51C45.134 51 42 47.866 42 44C42 40.134 45.134 37 49 37C52.866 37 56 40.134 56 44C56 45.2925 55.6497 46.5032 55.0389 47.5423C54.9627 47.6719 54.9426 47.8278 54.9901 47.9704L55.6838 50.0513C55.8141 50.4422 55.4422 50.8141 55.0513 50.6838L52.9704 49.9901C52.8278 49.9426 52.6719 49.9627 52.5423 50.0389C51.5032 50.6497 50.2925 51 49 51Z" fill="url(#paint1_linear_2_5)"/>
<g filter="url(#filter1_bd_2_5)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M39 51C44.5228 51 49 46.5228 49 41C49 35.4772 44.5228 31 39 31C33.4772 31 29 35.4772 29 41C29 42.8828 29.5204 44.6442 30.4251 46.148C30.5026 46.2768 30.5224 46.4328 30.4749 46.5754L29.3162 50.0513C29.1859 50.4422 29.5578 50.8141 29.9487 50.6838L33.4246 49.5251C33.5672 49.4776 33.7232 49.4974 33.852 49.5749C35.3558 50.4796 37.1172 51 39 51Z" fill="url(#paint2_linear_2_5)" shape-rendering="crispEdges"/>
<path d="M48.5 41C48.5 46.2467 44.2467 50.5 39 50.5C37.2105 50.5 35.538 50.0057 34.1098 49.1465C33.8613 48.9969 33.5537 48.955 33.2665 49.0508L29.7906 50.2094L30.9492 46.7335C31.045 46.4463 31.0031 46.1387 30.8535 45.8902C29.9943 44.462 29.5 42.7895 29.5 41C29.5 35.7533 33.7533 31.5 39 31.5C44.2467 31.5 48.5 35.7533 48.5 41Z" stroke="url(#paint3_linear_2_5)" shape-rendering="crispEdges"/>
</g>
<g filter="url(#filter2_di_2_5)">
<rect width="2" height="2" rx="1" transform="matrix(1 0 0 -1 34 42)" fill="url(#paint4_linear_2_5)" shape-rendering="crispEdges"/>
</g>
<g filter="url(#filter3_di_2_5)">
<rect width="2" height="2" rx="1" transform="matrix(1 0 0 -1 38 42)" fill="url(#paint5_linear_2_5)" shape-rendering="crispEdges"/>
</g>
<g filter="url(#filter4_di_2_5)">
<rect width="2" height="2" rx="1" transform="matrix(1 0 0 -1 42 42)" fill="url(#paint6_linear_2_5)" shape-rendering="crispEdges"/>
</g>
<defs>
<filter id="filter0_f_2_5" x="0" y="0" width="82" height="82" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="17" result="effect1_foregroundBlur_2_5"/>
</filter>
<filter id="filter1_bd_2_5" x="25" y="27" width="28" height="32" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_2_5"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_backgroundBlur_2_5" result="effect2_dropShadow_2_5"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_2_5" result="shape"/>
</filter>
<filter id="filter2_di_2_5" x="32.5" y="39.5" width="5" height="5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.75"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2_5"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2_5" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_2_5"/>
</filter>
<filter id="filter3_di_2_5" x="36.5" y="39.5" width="5" height="5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.75"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2_5"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2_5" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_2_5"/>
</filter>
<filter id="filter4_di_2_5" x="40.5" y="39.5" width="5" height="5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.75"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2_5"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2_5" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_2_5"/>
</filter>
<linearGradient id="paint0_linear_2_5" x1="48" y1="34" x2="34" y2="48" gradientUnits="userSpaceOnUse">
<stop stop-color="#BC8F4F"/>
<stop offset="1" stop-color="#EFD8B7"/>
</linearGradient>
<linearGradient id="paint1_linear_2_5" x1="56" y1="37" x2="42" y2="51" gradientUnits="userSpaceOnUse">
<stop stop-color="#BC8F4F"/>
<stop offset="1" stop-color="#EFD8B7"/>
</linearGradient>
<linearGradient id="paint2_linear_2_5" x1="26" y1="53" x2="49" y2="31" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.2"/>
<stop offset="1" stop-color="white" stop-opacity="0.5"/>
</linearGradient>
<linearGradient id="paint3_linear_2_5" x1="47" y1="53" x2="29" y2="31.5" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint4_linear_2_5" x1="-0.3" y1="2.2" x2="2" y2="-1.71363e-08" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.2"/>
<stop offset="1" stop-color="white" stop-opacity="0.5"/>
</linearGradient>
<linearGradient id="paint5_linear_2_5" x1="-0.3" y1="2.2" x2="2" y2="-1.71363e-08" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.2"/>
<stop offset="1" stop-color="white" stop-opacity="0.5"/>
</linearGradient>
<linearGradient id="paint6_linear_2_5" x1="-0.3" y1="2.2" x2="2" y2="-1.71363e-08" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.2"/>
<stop offset="1" stop-color="white" stop-opacity="0.5"/>
</linearGradient>
</defs>
</svg>
