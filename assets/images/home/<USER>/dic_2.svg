<svg width="41" height="40" viewBox="0 0 41 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_9_28)">
<rect x="0.0566406" width="40" height="40" rx="3" fill="url(#paint0_linear_9_28)"/>
</g>
<g filter="url(#filter1_d_9_28)">
<circle cx="20.5011" cy="28.8888" r="4.44444" fill="url(#paint1_linear_9_28)"/>
</g>
<g filter="url(#filter2_d_9_28)">
<ellipse cx="4.44444" cy="4.44444" rx="4.44444" ry="4.44444" transform="matrix(1 0 0 -1 16.0566 15.5557)" fill="url(#paint2_linear_9_28)"/>
</g>
<defs>
<filter id="filter0_i_9_28" x="0.0566406" y="0" width="40" height="40" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.98 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_9_28"/>
</filter>
<filter id="filter1_d_9_28" x="15.6566" y="24.3443" width="9.68892" height="9.68889" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.3"/>
<feGaussianBlur stdDeviation="0.2"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_9_28"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_9_28" result="shape"/>
</filter>
<filter id="filter2_d_9_28" x="15.6566" y="6.56678" width="9.68892" height="9.68889" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.3"/>
<feGaussianBlur stdDeviation="0.2"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_9_28"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_9_28" result="shape"/>
</filter>
<linearGradient id="paint0_linear_9_28" x1="0.0566409" y1="2.5" x2="33.0566" y2="40" gradientUnits="userSpaceOnUse">
<stop offset="0.208333" stop-color="#FCFCFC"/>
<stop offset="1" stop-color="#DDDDDD"/>
</linearGradient>
<linearGradient id="paint1_linear_9_28" x1="20.5011" y1="24.4443" x2="20.5011" y2="33.3332" gradientUnits="userSpaceOnUse">
<stop stop-color="#1C1C1C"/>
<stop offset="1" stop-color="#636363"/>
</linearGradient>
<linearGradient id="paint2_linear_9_28" x1="4.44444" y1="0" x2="4.44444" y2="8.88889" gradientUnits="userSpaceOnUse">
<stop stop-color="#1C1C1C"/>
<stop offset="1" stop-color="#636363"/>
</linearGradient>
</defs>
</svg>
