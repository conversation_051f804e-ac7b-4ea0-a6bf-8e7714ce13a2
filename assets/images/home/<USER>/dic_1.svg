<svg width="41" height="40" viewBox="0 0 41 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_9_32)">
<rect x="0.0566406" width="40" height="40" rx="3" fill="url(#paint0_linear_9_32)"/>
</g>
<g filter="url(#filter1_d_9_32)">
<circle cx="19.3899" cy="19.3333" r="6" fill="url(#paint1_linear_9_32)"/>
</g>
<defs>
<filter id="filter0_i_9_32" x="0.0566406" y="0" width="40" height="40" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.98 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_9_32"/>
</filter>
<filter id="filter1_d_9_32" x="12.9899" y="13.2333" width="12.8" height="12.8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.3"/>
<feGaussianBlur stdDeviation="0.2"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_9_32"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_9_32" result="shape"/>
</filter>
<linearGradient id="paint0_linear_9_32" x1="0.0566409" y1="2.5" x2="33.0566" y2="40" gradientUnits="userSpaceOnUse">
<stop offset="0.208333" stop-color="#FCFCFC"/>
<stop offset="1" stop-color="#DDDDDD"/>
</linearGradient>
<linearGradient id="paint1_linear_9_32" x1="19.3899" y1="13.3333" x2="19.3899" y2="25.3333" gradientUnits="userSpaceOnUse">
<stop stop-color="#992F38"/>
<stop offset="1" stop-color="#CF3340"/>
</linearGradient>
</defs>
</svg>
