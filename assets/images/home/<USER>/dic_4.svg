<svg width="41" height="40" viewBox="0 0 41 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_9_17)">
<rect x="0.0566406" width="40" height="40" rx="3" fill="url(#paint0_linear_9_17)"/>
</g>
<g filter="url(#filter1_d_9_17)">
<circle cx="11.1678" cy="11.1111" r="4.44444" fill="url(#paint1_linear_9_17)"/>
</g>
<g filter="url(#filter2_d_9_17)">
<circle cx="28.9454" cy="28.8889" r="4.44444" fill="url(#paint2_linear_9_17)"/>
</g>
<g filter="url(#filter3_d_9_17)">
<ellipse cx="4.44444" cy="4.44444" rx="4.44444" ry="4.44444" transform="matrix(1 0 0 -1 6.72339 33.3333)" fill="url(#paint3_linear_9_17)"/>
</g>
<g filter="url(#filter4_d_9_17)">
<ellipse cx="4.44444" cy="4.44444" rx="4.44444" ry="4.44444" transform="matrix(1 0 0 -1 24.501 15.5555)" fill="url(#paint4_linear_9_17)"/>
</g>
<defs>
<filter id="filter0_i_9_17" x="0.0566406" y="0" width="40" height="40" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.98 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_9_17"/>
</filter>
<filter id="filter1_d_9_17" x="6.32339" y="6.56666" width="9.68892" height="9.68889" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.3"/>
<feGaussianBlur stdDeviation="0.2"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_9_17"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_9_17" result="shape"/>
</filter>
<filter id="filter2_d_9_17" x="24.101" y="24.3445" width="9.68892" height="9.68889" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.3"/>
<feGaussianBlur stdDeviation="0.2"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_9_17"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_9_17" result="shape"/>
</filter>
<filter id="filter3_d_9_17" x="6.32339" y="24.3445" width="9.68892" height="9.68889" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.3"/>
<feGaussianBlur stdDeviation="0.2"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_9_17"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_9_17" result="shape"/>
</filter>
<filter id="filter4_d_9_17" x="24.101" y="6.56666" width="9.68892" height="9.68889" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.3"/>
<feGaussianBlur stdDeviation="0.2"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_9_17"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_9_17" result="shape"/>
</filter>
<linearGradient id="paint0_linear_9_17" x1="0.0566409" y1="2.5" x2="33.0566" y2="40" gradientUnits="userSpaceOnUse">
<stop offset="0.208333" stop-color="#FCFCFC"/>
<stop offset="1" stop-color="#DDDDDD"/>
</linearGradient>
<linearGradient id="paint1_linear_9_17" x1="11.1678" y1="6.66666" x2="11.1678" y2="15.5555" gradientUnits="userSpaceOnUse">
<stop stop-color="#992F38"/>
<stop offset="1" stop-color="#CF3340"/>
</linearGradient>
<linearGradient id="paint2_linear_9_17" x1="28.9454" y1="24.4445" x2="28.9454" y2="33.3333" gradientUnits="userSpaceOnUse">
<stop stop-color="#992F38"/>
<stop offset="1" stop-color="#CF3340"/>
</linearGradient>
<linearGradient id="paint3_linear_9_17" x1="4.44444" y1="0" x2="4.44444" y2="8.88889" gradientUnits="userSpaceOnUse">
<stop stop-color="#992F38"/>
<stop offset="1" stop-color="#CF3340"/>
</linearGradient>
<linearGradient id="paint4_linear_9_17" x1="4.44444" y1="0" x2="4.44444" y2="8.88889" gradientUnits="userSpaceOnUse">
<stop stop-color="#992F38"/>
<stop offset="1" stop-color="#CF3340"/>
</linearGradient>
</defs>
</svg>
