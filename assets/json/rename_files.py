import os
import glob
import re

# Define the folder path
folder_path = '/Users/<USER>/Documents/Projects/game_store/assets/images/avatars'

# List all files in the folder
all_files = glob.glob(os.path.join(folder_path, '*.jpg'))

# Initialize a flag to check if any .jpg files were found
found_jpgs = False

# Process each file
for file_path in all_files:
    file_name = os.path.basename(file_path)
    # Check if the file ends with .jpg
    if file_name.endswith('.jpg'):
        found_jpgs = True  # Set the flag to True if a .jpg file is found
        
        try:
            # Use regex to find the numeric part before the hyphen
            # Assuming the pattern is "number-hexcode.jpg"
            match = re.match(r'^(\d+)-', file_name)
            if match:
                number = match.group(1)  # Extract the matched group
                
                # Construct the new filename
                new_file_name = f'{number}.jpg'
                
                # Rename the file
                os.rename(file_path, os.path.join(folder_path, new_file_name))
                print(f'Renamed {file_name} to {new_file_name}')
            else:
                print(f'No numeric part found in {file_name}, skipping.')
        except Exception as e:
            print(f'An error occurred while processing {file_name}: {e}')

# Inform the user if no .jpg files were found
if not found_jpgs:
    print('No .jpg files found in the folder.')
