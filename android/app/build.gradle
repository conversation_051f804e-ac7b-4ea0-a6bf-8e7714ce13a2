plugins {
    id "com.android.application"
    // START: FlutterFire Configuration
    id 'com.google.gms.google-services'
    id 'com.google.firebase.crashlytics'
    // END: FlutterFire Configuration
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}
def localProperties = new Properties()
def localPropertiesFile = rootProject.file("local.properties")
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader("UTF-8") { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty("flutter.versionCode")
if (flutterVersionCode == null) {
    flutterVersionCode = "1"
}

def flutterVersionName = localProperties.getProperty("flutter.versionName")
if (flutterVersionName == null) {
    flutterVersionName = "1.0"
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}


android {

    // ----- BEGIN flavorDimensions (autogenerated by flutter_flavorizr) -----
    flavorDimensions += "default"

    productFlavors {
        WD {
            dimension "default"
            applicationId "com.fwd.wdwd"
            manifestPlaceholders.appScheme = "ikztoj"
            manifestPlaceholders.OPENINSTALL_APPKEY = "ikztoj"
            manifestPlaceholders.ENGAGELAB_PRIVATES_APPKEY = "689a76e7db852a0043f8cf5e"
            manifestPlaceholders.ENGAGELAB_PRIVATES_CHANNEL = "developer-default"
            manifestPlaceholders.ENGAGELAB_PRIVATES_PROCESS = ":remote"
            manifestPlaceholders.applicationName = "io.flutter.app.FlutterApplication"
            manifestPlaceholders.JPUSH_PKGNAME = applicationId
            manifestPlaceholders.JPUSH_APPKEY = "4fe5e1bc4737d3de76877194"
            manifestPlaceholders.JPUSH_CHANNEL = "developer-default"
            manifestPlaceholders.XIAOMI_APPID = ""
            manifestPlaceholders.XIAOMI_APPKEY = ""
            manifestPlaceholders.MEIZU_APPID = ""
            manifestPlaceholders.MEIZU_APPKEY = ""
            manifestPlaceholders.OPPO_APPID = ""
            manifestPlaceholders.OPPO_APPKEY = ""
            manifestPlaceholders.OPPO_APPSECRET = ""
            manifestPlaceholders.VIVO_APPID = ""
            manifestPlaceholders.VIVO_APPKEY = ""
            manifestPlaceholders.HONOR_APPID = ""
            manifestPlaceholders.APP_TCP_SSL = ""
            manifestPlaceholders.APP_DEBUG = ""
            manifestPlaceholders.COUNTRY_CODE = ""
            resValue "string", "app_name", "WD GAME"
        }
    }

    // ----- END flavorDimensions (autogenerated by flutter_flavorizr) -----

   namespace = "com.fwd.f_wd"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = "17"
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.fwd.f_wd"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = 24
        targetSdk = 34
        versionCode = flutter.versionCode
        versionName = flutter.versionName
        ndk {
            abiFilters 'armeabi-v7a', 'arm64-v8a' // Optional: excludes x86, x86_64 if not needed
        }
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias'] ?: "wd-key-alias"
            keyPassword keystoreProperties['keyPassword'] ?: "123456"
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : file("${System.getenv('HOME')}/wd-keystore.jks")
            storePassword keystoreProperties['storePassword'] ?: "123456"
        }
    }
}

flutter {
    source = "../.."
}
