<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>945554583431-pjm343b13aoo9lqlj5f1pjo3sb552v1r.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.945554583431-pjm343b13aoo9lqlj5f1pjo3sb552v1r</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>945554583431-8g9vtj0haogtbos07da7mifeqekg8t9j.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyDBFXql-9XeV6W0YWI40bjEoz0wTvnE2vQ</string>
	<key>GCM_SENDER_ID</key>
	<string>945554583431</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.fwd.wdwd</string>
	<key>PROJECT_ID</key>
	<string>wd-tenant</string>
	<key>STORAGE_BUCKET</key>
	<string>wd-tenant.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:945554583431:ios:ef684e48a03fcb7b6cad22</string>
</dict>
</plist>