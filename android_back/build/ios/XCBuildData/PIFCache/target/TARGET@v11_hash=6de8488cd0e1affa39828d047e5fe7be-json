{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9867b1432ee25a9534ecccec96edfe9a9f", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98076c14d395f435511569ab4ad9e7b484", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989dd90d93e6fadd1c4a020969bffe8372", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e981982494a844e2cd76ed4dfeb8c7e999f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989dd90d93e6fadd1c4a020969bffe8372", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98420d0b33d81c65660dc1e5af16f01d1a", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98649b362e3aa3fa34ce7af88f7941a8f9", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e985db720278459bf6fbf5e327cb734fc67", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9854389dc0c549e85902cff0682e05bf12", "guid": "bfdfe7dc352907fc980b868725387e9836f417ec6b790c9b98c6edf91578377e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ca8f321eb22198c7d2bddab2d8fc481", "guid": "bfdfe7dc352907fc980b868725387e984cee8f771ecfbd0a923b02f19dd8c639"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adb2f6ae9ad9ae796e10cf87db0c1db4", "guid": "bfdfe7dc352907fc980b868725387e989e2cfe680912e0924928141a35f42dec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd1f9a638e5d4de3b16902251808448a", "guid": "bfdfe7dc352907fc980b868725387e981cd94b05a9d40ef9887dd41cdcdec9aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c762dc2a46728917634679cb3a179841", "guid": "bfdfe7dc352907fc980b868725387e9866d79f14f97ed0205650b5e14c51817c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9209eb43e30fd5143381c19e1efb8b5", "guid": "bfdfe7dc352907fc980b868725387e98c890754979aa2282c0eabb9a9eee54df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98968aa544ce70e70c834890bca6f27561", "guid": "bfdfe7dc352907fc980b868725387e98c45e9b4331a9b8cf2fe9128902c21934"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867273646d95b119de20c743adce7f391", "guid": "bfdfe7dc352907fc980b868725387e98105430415230f482d092f0c56281db21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98801d612f796f0925560c31acf412ed4f", "guid": "bfdfe7dc352907fc980b868725387e9816cc16cc8f5b2976206c4bf0cc6a8ea1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986020bf58ea913001833dc22b7389b278", "guid": "bfdfe7dc352907fc980b868725387e98c455703596b87904da22e084addafc70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98266266272db31dc8da4df3799b9e787d", "guid": "bfdfe7dc352907fc980b868725387e987ad33ee860f932aa3672140b0ab107e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ee9615791620f02f2ba3369f15388cb", "guid": "bfdfe7dc352907fc980b868725387e9857a918d6b2147ecc48320df305b96543"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d73052afe2e1175d1421ef5962dae5d1", "guid": "bfdfe7dc352907fc980b868725387e9815057bd99dbbd06a4fd66fd721afc24c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803cd61f555ad0017141e7deedbc13264", "guid": "bfdfe7dc352907fc980b868725387e98267710157bc9f4f6195ff1ba86ed1d63"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875482bfd841f7ae12d69364416ff65a6", "guid": "bfdfe7dc352907fc980b868725387e982229fba388ddf6d0d173d1f6c0ae7c1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c24949975ddb8a7d7fc7849708ef0ca2", "guid": "bfdfe7dc352907fc980b868725387e98e83d285d7cc155258980ad642caaf18f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98109d5674234854c36d6c23e13713041d", "guid": "bfdfe7dc352907fc980b868725387e98ac5c3e5f41e360e3c1bc6f417ff905a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982353e4023b45477ae179c0cc07a6b7a6", "guid": "bfdfe7dc352907fc980b868725387e9822e7bc181776a87d2a53ab3fadd2cc81"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a4dfac20d5da07752e99b8ea99112d6", "guid": "bfdfe7dc352907fc980b868725387e987781c240fd66c7cc335ea0d31c23e68b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989692aec757492b128dd366698b5cf1e3", "guid": "bfdfe7dc352907fc980b868725387e984b2ab273dea4bd856499fc7cb866c0ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed3f38107f19be21032bb9956e3207e7", "guid": "bfdfe7dc352907fc980b868725387e981660faf4077af607b51f2662b206155f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d728a3ec92aef9b1120b82454f766f15", "guid": "bfdfe7dc352907fc980b868725387e989505b791ff2777753af608f44c6ae6e2"}], "guid": "bfdfe7dc352907fc980b868725387e983f9b74286e708cf9465b6bedaea9ed01", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ab5e1f747dfe477b655528b07584898d", "name": "DKImagePickerController.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}