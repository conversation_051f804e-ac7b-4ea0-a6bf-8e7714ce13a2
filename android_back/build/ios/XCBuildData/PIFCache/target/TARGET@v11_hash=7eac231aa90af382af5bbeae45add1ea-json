{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dc9a2feb5b93c5f5820520fd98fd155e", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a71dd808fadfff9e395cd17ca280e743", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981a347f9fe21ccbf061e617b4c96be06c", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983e3103b752c650cb4cced184db36a83c", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981a347f9fe21ccbf061e617b4c96be06c", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983ed2510e1cbacf53b15a85292b20a2e8", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a0a4e78343bcb0e161d51692ebea9029", "guid": "bfdfe7dc352907fc980b868725387e9882c1ac3f0a89964e6a68117717523739", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989f1340bed5afab1208e5981aa993e9a3", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98027f06cf31f8d09bcdc79527a3e03b6e", "guid": "bfdfe7dc352907fc980b868725387e98415a53cdb272473a86b40ffefbe23414"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fd45d2f06784841fa4dcb3b069c0e0e", "guid": "bfdfe7dc352907fc980b868725387e98b3e1118f25aef9b9485b8332c417c381"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b17b58b99b28d2cbf1e8bb3f3aff7bd", "guid": "bfdfe7dc352907fc980b868725387e98357e1047964ad36e53822f40bb473b89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a8b720c05f450943d200a641a23d9f6", "guid": "bfdfe7dc352907fc980b868725387e98b84ebf34e8bb5f4b9ed8f8748c891231"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98266586134d1497823e0ec459f05808b7", "guid": "bfdfe7dc352907fc980b868725387e984a8504fce973e9e6d53f768c717559d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9095746894b5ddc7d863f5909761e3a", "guid": "bfdfe7dc352907fc980b868725387e98e7c5c8ab3adf46caf88ac39533b953ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7ad6959b225c19ae95de0c57e03c3eb", "guid": "bfdfe7dc352907fc980b868725387e9850e2da9982573064ffe552e4c7c8778f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98431a1e8d44d46aa9001bd90011eed140", "guid": "bfdfe7dc352907fc980b868725387e988d865f755ae55751b7ba8f7817f61657"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818284b471996b41ebad8631bcce5a87e", "guid": "bfdfe7dc352907fc980b868725387e98eaa5b382240565c73cdf6223c430873f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d88ebf8e3a5887a05ff764aa7eab8e52", "guid": "bfdfe7dc352907fc980b868725387e980a8d5a3609b8397a44b8ad48e18959ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d577059b2a741e57a2b13477da6ec001", "guid": "bfdfe7dc352907fc980b868725387e987acc8486b567ae50e89859fda7d94a82"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98706882458b296bd7a1917588b1a13963", "guid": "bfdfe7dc352907fc980b868725387e986a8aa14ce9f8b098fdce78ef4b54332a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988216cb4a9dc98bbec3aed1c274b8a2ec", "guid": "bfdfe7dc352907fc980b868725387e981bab11382c3f2010c81b7218dfc72404"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aefe12dffeb0cd9e05691343af63c51a", "guid": "bfdfe7dc352907fc980b868725387e9828d933cdb6b5e1c1587a69f4c31d3f22"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e65fb6998b11f331db3aa4c7426ca827", "guid": "bfdfe7dc352907fc980b868725387e98b650e07be572c230240d94c4425a4759"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98179a2683d936cf2e57ed8528750d4fc8", "guid": "bfdfe7dc352907fc980b868725387e981b20a8f448f4ab636bf1806782369a35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856ba2aa4d0311a59164fb13f6ebf5000", "guid": "bfdfe7dc352907fc980b868725387e987cdf99809932218c6ab3f12557771241"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cb0cfe31d1135aacdb985b4bf5dddd8", "guid": "bfdfe7dc352907fc980b868725387e98ffa215b5f69ae4a69bc8e4983f9247f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98779ea1b3a8458090394dd1a44f605d34", "guid": "bfdfe7dc352907fc980b868725387e98dd35bcbf0c860c2658daf0e040971a5f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbfdbb5fd2d9d14feea8e28e4502fa7d", "guid": "bfdfe7dc352907fc980b868725387e9866ab81503bc566f8a2cbf56bd9f8cea2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983edf78b3cf5bc88b3031498680b8433d", "guid": "bfdfe7dc352907fc980b868725387e98e77d2143fdb5c6b99f10e52ec99360f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854481f7fbe70a94090884402a3fb3cba", "guid": "bfdfe7dc352907fc980b868725387e98976a0986421247797b19f037386bbc80"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985845a633025ca8aa5733f3b05284a315", "guid": "bfdfe7dc352907fc980b868725387e9844871fe7a647dd1e67173115c4fe19b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e88b657614edb94e3e2fe1303d7dadb", "guid": "bfdfe7dc352907fc980b868725387e98a50d81a4201c8a987218a31eb03f19d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832e4602288b622757e762457eb4f2cde", "guid": "bfdfe7dc352907fc980b868725387e981270b1d3a3125e51047cd4ab7b22e749"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7eefd66922716ee3cafb9920de9ea38", "guid": "bfdfe7dc352907fc980b868725387e9808eba781f6768d4b01b409e272ed1624"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aae15533473737d6248399d592e2645e", "guid": "bfdfe7dc352907fc980b868725387e98dd39044830e36e831491bbdca45c7318"}], "guid": "bfdfe7dc352907fc980b868725387e98817d76604ed656b796f871f82dcd2e45", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9850f446553ac3d61e4e51ded4b5c94143", "guid": "bfdfe7dc352907fc980b868725387e9819964da38b4271d804081d6a3311b90e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889cffe79f13615ec0e57770689f93e11", "guid": "bfdfe7dc352907fc980b868725387e98bed56eb560790db74196e3fdc02176bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869664cd59b56171ce6bd4a54b67071c4", "guid": "bfdfe7dc352907fc980b868725387e987e7f3bb6424287ac49764627ebe560d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984264deed309e6d519543a22165ea948e", "guid": "bfdfe7dc352907fc980b868725387e982a2611f55260da5ed7ac92496e8063e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e8189122d5421d1589ca17eda172100", "guid": "bfdfe7dc352907fc980b868725387e984c148fcf096924b3e39d0260d7e6e94b"}], "guid": "bfdfe7dc352907fc980b868725387e980ef2ebe7ad57ae9f4e2b89770dfa80e3", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9848e0dd5e20ca1aa2e6b9deb19203e7ef", "targetReference": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937"}], "guid": "bfdfe7dc352907fc980b868725387e98257f3ff1751be6eee5b841bd0daeceed", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e90c628ccd44af657bee5ff4af2f692", "name": "DKPhotoGallery.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}