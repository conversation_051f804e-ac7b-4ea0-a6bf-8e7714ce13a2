{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9811c7444e58cbba29c64cad5bf78adf18", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/media_kit_video/media_kit_video-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/media_kit_video/media_kit_video-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/media_kit_video/media_kit_video.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "media_kit_video", "PRODUCT_NAME": "media_kit_video", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988a507aefa2bcb928c4e446ef3b928722", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a2d430475a41db1ec59e091166969f15", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/media_kit_video/media_kit_video-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/media_kit_video/media_kit_video-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/media_kit_video/media_kit_video.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "media_kit_video", "PRODUCT_NAME": "media_kit_video", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986a70cc1357bf6c994b9489f37be19c91", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a2d430475a41db1ec59e091166969f15", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/media_kit_video/media_kit_video-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/media_kit_video/media_kit_video-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/media_kit_video/media_kit_video.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "media_kit_video", "PRODUCT_NAME": "media_kit_video", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9847459f47783032052024464440fb12bb", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983d2efd8fb71732450e6c5b4cb5cf79d3", "guid": "bfdfe7dc352907fc980b868725387e98c0cd4cf386d33ca099b5c61980292add", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e8c05b24bf78ac2f4729da1c13deca4", "guid": "bfdfe7dc352907fc980b868725387e9866a0a0bbebb513f0618c2a6977070bf7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98865920ffc2eb550c8d16444f7374560e", "guid": "bfdfe7dc352907fc980b868725387e980de42e4757b4d5b579118e80b0581776", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98145613b4789cb2ab9280782593401b39", "guid": "bfdfe7dc352907fc980b868725387e98e7c54a4525e28b2d923b4970362e846c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876e28cc69b6229bd72afe1a35791690f", "guid": "bfdfe7dc352907fc980b868725387e9842b093823160236ad733ce214a1e062f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98506f1b9923d6cc6ab9b200cc99b35917", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9813f069ee0982a8374a9e4ca2dc9cd242", "guid": "bfdfe7dc352907fc980b868725387e9831058237beea73fc8f15a33c99ba924d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d46c6125c431deb5588a2a889e6bafbe", "guid": "bfdfe7dc352907fc980b868725387e9817c6240bb116a47cb8533ff2d8f6a8d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ae333315138065ea2ce474b98ee24c3", "guid": "bfdfe7dc352907fc980b868725387e985b25c546b7d2151a925e33057bb661ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98981f857bfa635c970a7fb384c6412737", "guid": "bfdfe7dc352907fc980b868725387e98295d52200d06a1a2503d8ddac5a8fab3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc6f3c67c04a48bf991b5585102af199", "guid": "bfdfe7dc352907fc980b868725387e98dd88c04f40c092385afdb35d58aea39e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98306eea880120b9e55871dc574c934d33", "guid": "bfdfe7dc352907fc980b868725387e98007f4f23231a44945b8570e8be01f4d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfed363b37a406e01e3bf38dbd70d380", "guid": "bfdfe7dc352907fc980b868725387e98cc2301a1cc10fa05d4edb191713f4741"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5ea05b482d5787acc408106dfa100ec", "guid": "bfdfe7dc352907fc980b868725387e9890af6c385b3cbc94a87a2c0700837f21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811a9ad6d49d9ad28964f1d0c24c7b009", "guid": "bfdfe7dc352907fc980b868725387e98e5abb8c08238db9f97bde4dbedf0d907"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823d14d2704ac8ca038ff90df15696f71", "guid": "bfdfe7dc352907fc980b868725387e981bfa0bfddc2acd2a2e183a8ad48a5712"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bfddaab8ab265f52ffb0763694c8c8e", "guid": "bfdfe7dc352907fc980b868725387e983d4fd705b2d19be5c437d1173f2d3831"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e0ac67eb3312c6271eafa1eaffbd271", "guid": "bfdfe7dc352907fc980b868725387e98f219f2c174e3aba7835afbaccf6ee1e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d98c790909c37c990e8c2f45d507f9a3", "guid": "bfdfe7dc352907fc980b868725387e98350fe99da2065367968ef50388332de4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3f9abe3e369ea429857763d219b2446", "guid": "bfdfe7dc352907fc980b868725387e98858aefebff03aa93f2c39af7c8380ad4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986023fc874ca8c8002e623af220431dc0", "guid": "bfdfe7dc352907fc980b868725387e982260df8a05ebbe6a8d0897269d915b46"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f49fb594adf9b1630c185c970630ae08", "guid": "bfdfe7dc352907fc980b868725387e98b99f9869629cf22122d2822b94284ee9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2400b86961c09d136f2edb84fbd59b1", "guid": "bfdfe7dc352907fc980b868725387e9821dfd572e47ce85e2bafc01096d2685c"}], "guid": "bfdfe7dc352907fc980b868725387e980799124e6b1bec10a31601734d965978", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9869664cd59b56171ce6bd4a54b67071c4", "guid": "bfdfe7dc352907fc980b868725387e98e50d8ce8b45885cf65405c3be67f6abe"}], "guid": "bfdfe7dc352907fc980b868725387e98f798410d711bf2060f608703a93a4cb2", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98d1f3bbcb4cc251b86cd820939e996acd", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e98ef586bf23362aebce5c719b4482fa695", "name": "media_kit_video", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9897cdf84f28cf59650494803c009f76dc", "name": "media_kit_video.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}