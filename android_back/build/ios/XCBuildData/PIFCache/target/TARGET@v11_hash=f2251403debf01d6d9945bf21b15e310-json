{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98098689b759a33d508f2784ee48a1e763", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/PINCache/PINCache-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/PINCache/PINCache-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PINCache/PINCache.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "PINCache", "PRODUCT_NAME": "PINCache", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d68d4e2ee37ccdd560d554881c731417", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e4a2638801cc0c3b85cc1e3a53f1b21c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/PINCache/PINCache-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/PINCache/PINCache-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PINCache/PINCache.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "PINCache", "PRODUCT_NAME": "PINCache", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9824216a82538bd0eab4b7e05cf9fda677", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e4a2638801cc0c3b85cc1e3a53f1b21c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "GCC_PREFIX_HEADER": "Target Support Files/PINCache/PINCache-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/PINCache/PINCache-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PINCache/PINCache.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "PINCache", "PRODUCT_NAME": "PINCache", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9817ce3be47c6d8e28aa0dddad41422d4e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981308aa1a5b660905b920f4774763f6b5", "guid": "bfdfe7dc352907fc980b868725387e9831970903b9696e059afffb1272fbd409", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bc364dd8f0a34ad21200e2e309a3dfb", "guid": "bfdfe7dc352907fc980b868725387e98513d568f27e6a76d8e43c60d1f1b4f3d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f022a16c651fe83fe62310978cdd944", "guid": "bfdfe7dc352907fc980b868725387e9867fa8834f9d51842d051f74fc270c1d4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af3cc7b6515563fc7e2e80e58d662e49", "guid": "bfdfe7dc352907fc980b868725387e98af9864363aae84eab72d9ff9555cad5d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f40c382bff0c35ff2ae902f474f2cba4", "guid": "bfdfe7dc352907fc980b868725387e988f45e866bae7c01e5029821c7eed6967", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ccc859a3bb58a0b4bb21a5d54cba03f", "guid": "bfdfe7dc352907fc980b868725387e98b7912edb4b1ab86968c16531e14f86a0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98717b73af57e79686dff0ef670ba64701", "guid": "bfdfe7dc352907fc980b868725387e989ffac63b78da38b36dd6f045febea0ce", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98875e25995d6d1e703a9d0fc827c6c526", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f7d080969c6301a7b77b2a34fc5e2f79", "guid": "bfdfe7dc352907fc980b868725387e98a21a8d924e6d561e5e7933b6bc6f0394"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980af9f1fb2542f725b3fb1d36ce4744b3", "guid": "bfdfe7dc352907fc980b868725387e98f03e1c2d93fff66c1604bae584a5c900"}, {"additionalCompilerOptions": "-fobjc-arc-exceptions", "fileReference": "bfdfe7dc352907fc980b868725387e985cc4563e4575b483d2d4b49563824000", "guid": "bfdfe7dc352907fc980b868725387e98c2094c1909379855588f72007af1753d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807125b683515c305688458a2d58621d2", "guid": "bfdfe7dc352907fc980b868725387e98aed9251d4d8f39a0d1f73f5706e9dbf6"}], "guid": "bfdfe7dc352907fc980b868725387e98488afc940377ec866812efeb66e520b8", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9869664cd59b56171ce6bd4a54b67071c4", "guid": "bfdfe7dc352907fc980b868725387e989fd8abed05e5bdd2d4a5b96bab1faeb1"}], "guid": "bfdfe7dc352907fc980b868725387e980a710041e7a239d6e688db1713f6a4fa", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9809571fb5861ffbd0b365a6630771e3b2", "targetReference": "bfdfe7dc352907fc980b868725387e98956b709ce41abfabe34269a056c97973"}], "guid": "bfdfe7dc352907fc980b868725387e9891ce51e21c57730b08a9ce5169c24657", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98956b709ce41abfabe34269a056c97973", "name": "PINCache-PINCache"}, {"guid": "bfdfe7dc352907fc980b868725387e98d09cfc976f4622a7180a4de1a3b0a290", "name": "PINOperation"}], "guid": "bfdfe7dc352907fc980b868725387e980a765b211b0c8c508dddcb830a52bbab", "name": "PINCache", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98329516f5a905f2fb033999940b6a62f1", "name": "PINCache.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}