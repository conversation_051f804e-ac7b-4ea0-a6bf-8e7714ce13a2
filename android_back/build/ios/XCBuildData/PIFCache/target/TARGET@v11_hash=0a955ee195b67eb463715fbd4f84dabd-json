{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9871f4f15549b855eb652c31537a454806", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d15420b18834da786f148242969989e7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9866483ace9ef6dc910c96b7678c83e68c", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98acf95ee3ab2a6ee729287c0d42603188", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9866483ace9ef6dc910c96b7678c83e68c", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "arm64", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_MICROPHONE=1 PERMISSION_CAMERA=1 PERMISSION_PHOTOS=1", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b68d75e79f98bc6640329a44a26f353a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ce9c1def6869cf33ebf29c21780bccb9", "guid": "bfdfe7dc352907fc980b868725387e987b2a96482406cd48d3c5e964dbef8bc2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899d0adb93386ec9ee01aea4477d552e4", "guid": "bfdfe7dc352907fc980b868725387e9834bcd61548e1006692ffca0df79d05ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982345222a33d88d582e9a4724212f8e45", "guid": "bfdfe7dc352907fc980b868725387e9826d0a88b076166c4b864cdbdbce92bef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d711f24994147bee1ea4582617765aa", "guid": "bfdfe7dc352907fc980b868725387e98f06dd451ac8f1a44c57764e553b527a6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878b2f8b7e0a66d3cbb5a5a75d0afe6c2", "guid": "bfdfe7dc352907fc980b868725387e9858166e44940ebd93c8b5a766b12b84f5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986702382f217de9ba17e94bb51fdf898f", "guid": "bfdfe7dc352907fc980b868725387e985bf0c4cd98e92817abbdcc35bdc5531f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ca62e12f7ceb91f02648a3290af08a3", "guid": "bfdfe7dc352907fc980b868725387e98c51f26b87df945a97112699464d5bdf0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c939f9ba0b3b9481c2b263668fdb500", "guid": "bfdfe7dc352907fc980b868725387e98af4c2ceadace42a2f4adc0d63da70812", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc16134b2746a5c43cf877f1ed641537", "guid": "bfdfe7dc352907fc980b868725387e986dc9311543863ced0c90776181c9ea38", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98893a5c664e87a1502c6d7412e820a41e", "guid": "bfdfe7dc352907fc980b868725387e982c85902d07d31bb1e5ed529b4fec6022", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ff88bad5d17914a7a152c6a47d08003", "guid": "bfdfe7dc352907fc980b868725387e98a00ffc876eed62fce6ba5794f8479b12", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837df569b079600ebfcb55297cc3076a0", "guid": "bfdfe7dc352907fc980b868725387e983ad80b28dc127b28a95d9b21b3eba752", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98787ad6eb63848a14d347412669080613", "guid": "bfdfe7dc352907fc980b868725387e989e97ad7faf44c970ff1e6892a1c6ff4c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98559648e7795bd0decf05baf7a499bf5b", "guid": "bfdfe7dc352907fc980b868725387e986c3f6d4e621a705b01b32cad0135ff32", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806d34d641e1e82fa2edf74f84a3ed814", "guid": "bfdfe7dc352907fc980b868725387e98908859acf0314667d813df278ebc7971", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c12811c844e3f90eab510fec02e544f0", "guid": "bfdfe7dc352907fc980b868725387e9835060e3266401f00c0e55803e560ef3d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4e63a673a6dc45b039787e262cc1634", "guid": "bfdfe7dc352907fc980b868725387e9892c65e1f99e88c517940337dea3b8c13", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98125ba1d76e764a54de6a1177e018952b", "guid": "bfdfe7dc352907fc980b868725387e9870e3688111116f66aea186a02ca7fedc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e889002afca15f25b3eafa82224cf72c", "guid": "bfdfe7dc352907fc980b868725387e98d8787100502fa9502542bcd3a823833c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c39b726028ed25f5b47ee894e99bc937", "guid": "bfdfe7dc352907fc980b868725387e988052de86f70283058d324ddc6ab5acdd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983515a3fbfd9fa01dd1428e6312a59f75", "guid": "bfdfe7dc352907fc980b868725387e9872a4c5a4082445e692c9d19c755c8ca6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883faf5c005123a327525c06836ca42db", "guid": "bfdfe7dc352907fc980b868725387e9804b492d3ba5a3c526fd4ef87a8c1332c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdad8bbb7eb760627e8bf0ffe1856a42", "guid": "bfdfe7dc352907fc980b868725387e98a6052558bee4cf5128d3d0f542c926ed", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98d5c8c539c8b21555bcb4eb3367cc806c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984e5579e07155780e8a2134c92e9ad544", "guid": "bfdfe7dc352907fc980b868725387e9849d0047527a358bfff8af909b731bda0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8e8bfc47d3114333e8f9755b0b7f86b", "guid": "bfdfe7dc352907fc980b868725387e98058b771dd892aad80c349da616705ad7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b91b390f3f31616f7737a45abb7929f0", "guid": "bfdfe7dc352907fc980b868725387e98a33accb89e3fe72aed974b9e3f2fa683"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843465b14f3e2976bad447d8c85579ae9", "guid": "bfdfe7dc352907fc980b868725387e98531d786c9c0863ff8e634db7caba945e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875b61e242a6d0ac4b073b42ba7d84e6f", "guid": "bfdfe7dc352907fc980b868725387e9809daffbf8d209e4827b067f350b66ad5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c620ea6273ebb678232b608833fe375c", "guid": "bfdfe7dc352907fc980b868725387e981ec6e69e78436e9f90db8be66c2da384"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b49d1db266fa2acd1e641c948854135d", "guid": "bfdfe7dc352907fc980b868725387e98e87b27bbc76fcba88e2da7f95c269456"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8f0f52800e3fa31358359a0e87d5708", "guid": "bfdfe7dc352907fc980b868725387e9898a2705ad2089817ede15eeeeacfb22f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d0ba492e0e913ff769eb2aaa4db5d36", "guid": "bfdfe7dc352907fc980b868725387e987b1fe7dcbc4de956ab960b813043de76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983db384f1f7e3a21222cc0918247c4471", "guid": "bfdfe7dc352907fc980b868725387e985e6b60e54a68d747cff302ba76b28f72"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c88486c394290d8b568853a0b69988e", "guid": "bfdfe7dc352907fc980b868725387e98b02270ba1701e242ede905bb791e4bb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e67ab0c496259705ba48c6253d8b7e4", "guid": "bfdfe7dc352907fc980b868725387e98a5300e0a36e0f37f36453390299cc559"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d26d3344dbf4e370136d57ec673f2ea8", "guid": "bfdfe7dc352907fc980b868725387e98c0229d70a6a984dd4b305a2b34d61e96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989434f9e47f0ab5b1df97883abf7e0ad4", "guid": "bfdfe7dc352907fc980b868725387e98187ffa737b89431fd12596f736d5b0ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fe42c3cf87b437f0df0e3a763e5ad03", "guid": "bfdfe7dc352907fc980b868725387e98df7b7f5210ab096b5b90dc28d6a1ff40"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c95be35c005229a8cd1261c211dbe78e", "guid": "bfdfe7dc352907fc980b868725387e983cfef307d010a7dce17c978e0e3a8387"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4986c3b18dbb9e278d26c9110c3a67f", "guid": "bfdfe7dc352907fc980b868725387e98e02cbf019d1e54682905237fda670b58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98326a5f5995bf167feb4acde315f69afd", "guid": "bfdfe7dc352907fc980b868725387e98a58a723c82c2b84fd623236bed4e346b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2a0f63c88de750b9140466551378e05", "guid": "bfdfe7dc352907fc980b868725387e986f4b52efa0acd85e7ad08118a848fa36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e503355c97fa656f8868f4f3740ac49c", "guid": "bfdfe7dc352907fc980b868725387e98fe02c6ba8bbe97e0b7513989d619e5b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98605da03631c4af9c253538757166ee9a", "guid": "bfdfe7dc352907fc980b868725387e984f1bf9fec22522dbec969a28b45a6acc"}], "guid": "bfdfe7dc352907fc980b868725387e98057eb75c841554d25f0f5035104e0263", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9869664cd59b56171ce6bd4a54b67071c4", "guid": "bfdfe7dc352907fc980b868725387e980017a4fef110a270efddb901b92f2e25"}], "guid": "bfdfe7dc352907fc980b868725387e9810423e99f15760aeac349670a9357a85", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9862935e1388b76505ac12d06927e9652c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}