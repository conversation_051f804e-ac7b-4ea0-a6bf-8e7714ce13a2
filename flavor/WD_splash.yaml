## flavor/YL_splash.yaml
#flutter_native_splash:


flutter_native_splash:
  # 本插件会生成原生代码，用来自定义 Flutter 默认白色启动页背景
  # 你可以通过配置背景色和启动图来个性化你的 App 启动页。

  # 修改下面的参数后，请运行以下命令生成启动页资源：
  # dart run flutter_native_splash:create

  # 若想恢复 Flutter 默认的白色启动页，请运行：
  # dart run flutter_native_splash:remove

  # ⚠️ 重要提示：以下大部分参数不适用于 Android 12 及更高版本，
  # 因为 Android 12+ 使用了新的启动页系统，需使用 `android_12` 下的参数单独配置。

  # 【必须参数】color 或 background_image 至少设置一个：
  # - color 设置纯色背景
  # - background_image 设置背景图（可用于渐变），图像会自动拉伸到屏幕大小
  # ⚠️ 注意二者不可同时设置
  #background_image: "assets/background.png"
  color: "#ffffff"
  # 【可选参数】

  # 设置启动页中的 LOGO 图片（PNG），建议为 4x 密度图（如 512x512）
  image: assets/images/logo/logo.png

  # 设置品牌图标（通常显示在底部，可设置 branding_mode 控制位置）
  #branding: assets/dart.png

  # 设置品牌图标的位置：bottom（默认）、bottomRight、bottomLeft
  #branding_mode: bottom

  # 设置品牌图标离底部的距离（仅支持 iOS 和 Android，不支持 Web）
  #branding_bottom_padding: 24

  # 深色模式下的设置（如不设置，则继承上面浅色配置）
  #color_dark: "#042a49"
  #background_image_dark: "assets/dark-background.png"
  #image_dark: assets/splash-invert.png
  #branding_dark: assets/dart_dark.png

  # Android 12+ 独立配置区（Android 12 启动页系统已改变）
  android_12:
  # Android 12 启动图标（圆形裁剪），如果不设置会默认用 App icon
  # - 如果带 icon 背景：960x960，主元素居中 640px 圆形
  # - 不带背景：1152x1152，主元素在直径 768px 圆中
  #image: assets/android12splash.png

  # 启动页背景色
    color: "#ffffff"

  # 启动图标背景色（位于圆圈后面）
  #icon_background_color: "#111111"

  # 品牌图标（Android 12）
  #branding: assets/images/logo/logo.png

  # 深色模式下的 Android 12 配置
  #image_dark: assets/android12splash-invert.png
  #color_dark: "#042a49"
  #icon_background_color_dark: "#eeeeee"

  # 可以禁用某个平台的启动页生成
  android: true
  ios: true
  web: false

  # 平台专用图像或颜色（优先级高于通用设置）
  #color_android: "#42a5f5"
  #color_dark_android: "#042a49"
  #color_ios: "#42a5f5"
  #color_dark_ios: "#042a49"
  #color_web: "#42a5f5"
  #color_dark_web: "#042a49"
  #image_android: assets/splash-android.png
  #image_dark_android: assets/splash-invert-android.png
  #image_ios: assets/splash-ios.png
  #image_dark_ios: assets/splash-invert-ios.png
  #image_web: assets/splash-web.gif
  #image_dark_web: assets/splash-invert-web.gif
  #background_image_android: "assets/background-android.png"
  #background_image_dark_android: "assets/dark-background-android.png"
  #background_image_ios: "assets/background-ios.png"
  #background_image_dark_ios: "assets/dark-background-ios.png"
  #background_image_web: "assets/background-web.png"
  #background_image_dark_web: "assets/dark-background-web.png"
  #branding_android: assets/brand-android.png
  #branding_bottom_padding_android: 24
  #branding_dark_android: assets/dart_dark-android.png
  #branding_ios: assets/brand-ios.png
  #branding_bottom_padding_ios: 24
  #branding_dark_ios: assets/dart_dark-ios.png
  #branding_web: assets/brand-web.gif
  #branding_dark_web: assets/dart_dark-web.gif

  # 设置启动图的位置
  #android_gravity: center           # Android：参考 Gravity 常量组合
  #ios_content_mode: center          # iOS：参考 UIView.ContentMode
  #web_image_mode: center            # Web：center、contain、stretch、cover

  # Android 设置屏幕方向：如 sensorLandscape 横屏感应
  #android_screen_orientation: sensorLandscape

  # 是否全屏（隐藏状态栏）：
  # ⚠️ Android 会在 App 启动后自动恢复状态栏；iOS 则不会，需要手动设置。
  fullscreen: false

  # 如果你修改了 iOS 的 Info.plist 文件名，需显式指定：
  #info_plist_files:
